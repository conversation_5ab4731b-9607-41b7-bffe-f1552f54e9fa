#!/bin/bash

# Generate nginx configuration from template
# Usage: ./generate-nginx-config.sh [domain_name]

set -e

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
NGINX_DIR="$PROJECT_ROOT/infrastructure/nginx"

# Default domain name
DEFAULT_DOMAIN="civicpoll.local"

# Get domain name from argument or environment variable
DOMAIN_NAME="${1:-${DOMAIN_NAME:-$DEFAULT_DOMAIN}}"

# Validate domain name
if [[ ! "$DOMAIN_NAME" =~ ^[a-zA-Z0-9.-]+$ ]]; then
    echo "Error: Invalid domain name '$DOMAIN_NAME'"
    echo "Domain name should only contain letters, numbers, dots, and hyphens"
    exit 1
fi

echo "Generating nginx configuration for domain: $DOMAIN_NAME"

# Check if template exists
TEMPLATE_FILE="$NGINX_DIR/civicpoll.conf.template"
if [[ ! -f "$TEMPLATE_FILE" ]]; then
    echo "Error: Template file not found: $TEMPLATE_FILE"
    exit 1
fi

# Generate configuration file
OUTPUT_FILE="$NGINX_DIR/civicpoll.conf"
export DOMAIN_NAME
envsubst < "$TEMPLATE_FILE" > "$OUTPUT_FILE"

echo "Generated nginx configuration: $OUTPUT_FILE"
echo "Domain name: $DOMAIN_NAME"

# Validate nginx configuration syntax if nginx is available
if command -v nginx >/dev/null 2>&1; then
    echo "Validating nginx configuration..."
    if nginx -t -c "$OUTPUT_FILE" 2>/dev/null; then
        echo "✓ Nginx configuration is valid"
    else
        echo "⚠ Warning: Nginx configuration validation failed"
        echo "Please check the configuration manually"
    fi
else
    echo "Note: nginx not found, skipping configuration validation"
fi

echo "Done!"
