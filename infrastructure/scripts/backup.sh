#!/bin/bash
# CivicPoll Backup Script
# This script performs a complete backup of the CivicPoll platform
# including database, media files, configuration, and audit logs

set -euo pipefail

# Configuration
BACKUP_ROOT="${BACKUP_ROOT:-/opt/civicpoll/backups}"
S3_BUCKET="${S3_BUCKET:-s3://civicpoll-backups/fr}"
RETENTION_DAYS="${RETENTION_DAYS:-30}"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$BACKUP_ROOT/$DATE"
LOG_FILE="$BACKUP_ROOT/backup.log"
ALERTMANAGER_URL="${ALERTMANAGER_URL:-http://localhost:9093}"

# Database configuration
DB_HOST="${DATABASE_HOST:-localhost}"
DB_PORT="${DATABASE_PORT:-5432}"
DB_NAME="${DATABASE_NAME:-civicpoll_fr}"
DB_USER="${DATABASE_USERNAME:-civicpoll_user}"
DB_PASSWORD="${DATABASE_PASSWORD}"

# Encryption key
BACKUP_ENCRYPTION_KEY="${BACKUP_ENCRYPTION_KEY}"

# Ensure backup directory exists
mkdir -p "$BACKUP_DIR"

# Function to log messages
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to send alert
send_alert() {
    local severity="$1"
    local summary="$2"
    local description="$3"
    
    if [ -n "$ALERTMANAGER_URL" ]; then
        curl -X POST "$ALERTMANAGER_URL/api/v1/alerts" \
            -H "Content-Type: application/json" \
            -d "[{
                \"labels\": {
                    \"alertname\": \"BackupStatus\",
                    \"severity\": \"$severity\",
                    \"service\": \"civicpoll-backup\"
                },
                \"annotations\": {
                    \"summary\": \"$summary\",
                    \"description\": \"$description\"
                }
            }]" &>/dev/null || true
    fi
}

# Function to handle errors
handle_error() {
    local line_number="$1"
    log "ERROR: Backup failed at line $line_number"
    send_alert "critical" "Backup Failed" "The backup process failed at line $line_number on $(date)"
    
    # Cleanup partial backup
    rm -rf "$BACKUP_DIR"
    
    exit 1
}

# Set error trap
trap 'handle_error $LINENO' ERR

# Start backup
log "Starting backup process"
send_alert "info" "Backup Started" "Backup process started at $(date)"

# 1. Database backup
log "Backing up PostgreSQL database"
PGPASSWORD="$DB_PASSWORD" pg_dump \
    -h "$DB_HOST" \
    -p "$DB_PORT" \
    -U "$DB_USER" \
    -d "$DB_NAME" \
    --no-owner \
    --no-acl \
    --clean \
    --if-exists \
    --verbose \
    | gzip -9 > "$BACKUP_DIR/database.sql.gz"

# Verify database backup
if [ ! -s "$BACKUP_DIR/database.sql.gz" ]; then
    log "ERROR: Database backup file is empty"
    exit 1
fi

# 2. Strapi media files
log "Backing up Strapi media files"
if [ -d "/opt/civicpoll/apps/backend/public/uploads" ]; then
    tar -czf "$BACKUP_DIR/media.tar.gz" \
        -C /opt/civicpoll/apps/backend \
        public/uploads
else
    log "WARNING: Media directory not found, skipping"
    touch "$BACKUP_DIR/media.tar.gz"
fi

# 3. Configuration files
log "Backing up configuration files"
tar -czf "$BACKUP_DIR/config.tar.gz" \
    -C /opt/civicpoll \
    --exclude=node_modules \
    --exclude=.next \
    --exclude=build \
    --exclude=dist \
    --exclude=.git \
    --exclude=logs \
    apps/backend/config \
    apps/backend/.env \
    apps/frontend/.env.local \
    infrastructure/nginx \
    ecosystem.config.js \
    .env 2>/dev/null || true

# 4. Audit logs export (GDPR compliance)
log "Exporting audit logs"
PGPASSWORD="$DB_PASSWORD" psql \
    -h "$DB_HOST" \
    -p "$DB_PORT" \
    -U "$DB_USER" \
    -d "$DB_NAME" \
    -c "\COPY (SELECT * FROM audit_logs WHERE timestamp >= NOW() - INTERVAL '7 days') TO '$BACKUP_DIR/audit_logs.csv' CSV HEADER"

# 5. Database schema export
log "Exporting database schema"
PGPASSWORD="$DB_PASSWORD" pg_dump \
    -h "$DB_HOST" \
    -p "$DB_PORT" \
    -U "$DB_USER" \
    -d "$DB_NAME" \
    --schema-only \
    --no-owner \
    --no-acl \
    > "$BACKUP_DIR/schema.sql"

# 6. Create backup manifest
log "Creating backup manifest"
cat > "$BACKUP_DIR/manifest.json" << EOF
{
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "version": "$(cd /opt/civicpoll && git rev-parse HEAD 2>/dev/null || echo 'unknown')",
    "type": "full",
    "retention_days": $RETENTION_DAYS,
    "components": {
        "database": "database.sql.gz",
        "schema": "schema.sql",
        "media": "media.tar.gz",
        "config": "config.tar.gz",
        "audit_logs": "audit_logs.csv"
    },
    "checksums": {
        "database": "$(sha256sum $BACKUP_DIR/database.sql.gz | cut -d' ' -f1)",
        "schema": "$(sha256sum $BACKUP_DIR/schema.sql | cut -d' ' -f1)",
        "media": "$(sha256sum $BACKUP_DIR/media.tar.gz | cut -d' ' -f1)",
        "config": "$(sha256sum $BACKUP_DIR/config.tar.gz | cut -d' ' -f1)",
        "audit_logs": "$(sha256sum $BACKUP_DIR/audit_logs.csv | cut -d' ' -f1)"
    },
    "size": {
        "database": $(stat -c%s "$BACKUP_DIR/database.sql.gz"),
        "schema": $(stat -c%s "$BACKUP_DIR/schema.sql"),
        "media": $(stat -c%s "$BACKUP_DIR/media.tar.gz"),
        "config": $(stat -c%s "$BACKUP_DIR/config.tar.gz"),
        "audit_logs": $(stat -c%s "$BACKUP_DIR/audit_logs.csv"),
        "total": $(du -sb "$BACKUP_DIR" | cut -f1)
    }
}
EOF

# 7. Create encrypted archive
log "Creating encrypted backup archive"
tar -czf - -C "$BACKUP_ROOT" "$DATE" | \
    openssl enc -aes-256-cbc -salt -pbkdf2 -pass pass:"$BACKUP_ENCRYPTION_KEY" \
    > "$BACKUP_ROOT/civicpoll_backup_$DATE.tar.gz.enc"

# 8. Upload to S3 (if configured)
if [ -n "${S3_BUCKET:-}" ] && command -v aws &> /dev/null; then
    log "Uploading backup to S3"
    aws s3 cp "$BACKUP_ROOT/civicpoll_backup_$DATE.tar.gz.enc" \
        "$S3_BUCKET/civicpoll_backup_$DATE.tar.gz.enc" \
        --storage-class STANDARD_IA \
        --metadata "retention-days=$RETENTION_DAYS,backup-date=$DATE"
    
    # Upload manifest separately for easier access
    aws s3 cp "$BACKUP_DIR/manifest.json" \
        "$S3_BUCKET/manifests/civicpoll_backup_$DATE.json" \
        --content-type "application/json"
else
    log "S3 upload skipped (not configured or AWS CLI not available)"
fi

# 9. Cleanup old backups
log "Cleaning up old backups"
find "$BACKUP_ROOT" -name "civicpoll_backup_*.tar.gz.enc" -mtime +$RETENTION_DAYS -delete

# Also cleanup old S3 backups if configured
if [ -n "${S3_BUCKET:-}" ] && command -v aws &> /dev/null; then
    aws s3 ls "$S3_BUCKET/" | grep "civicpoll_backup_" | while read -r line; do
        backup_date=$(echo "$line" | awk '{print $4}' | sed 's/civicpoll_backup_//' | sed 's/.tar.gz.enc//')
        backup_timestamp=$(date -d "${backup_date:0:8} ${backup_date:9:2}:${backup_date:11:2}:${backup_date:13:2}" +%s 2>/dev/null || echo 0)
        current_timestamp=$(date +%s)
        age_days=$(( (current_timestamp - backup_timestamp) / 86400 ))
        
        if [ $age_days -gt $RETENTION_DAYS ]; then
            aws s3 rm "$S3_BUCKET/$(echo "$line" | awk '{print $4}')"
        fi
    done
fi

# 10. Test backup integrity
log "Testing backup integrity"
openssl enc -aes-256-cbc -d -salt -pbkdf2 -pass pass:"$BACKUP_ENCRYPTION_KEY" \
    -in "$BACKUP_ROOT/civicpoll_backup_$DATE.tar.gz.enc" | \
    tar -tzf - > /dev/null

if [ $? -eq 0 ]; then
    log "Backup integrity test passed"
else
    log "ERROR: Backup integrity test failed"
    exit 1
fi

# 11. Update metrics (if metrics endpoint is available)
if command -v curl &> /dev/null && [ -n "${METRICS_TOKEN:-}" ]; then
    curl -X POST http://localhost:1337/api/metrics/backup \
        -H "Authorization: Bearer $METRICS_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"status\": \"success\",
            \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\",
            \"size\": $(stat -c%s "$BACKUP_ROOT/civicpoll_backup_$DATE.tar.gz.enc"),
            \"duration\": $SECONDS,
            \"components\": [\"database\", \"media\", \"config\", \"audit_logs\"]
        }" &>/dev/null || true
fi

# 12. Generate backup report
log "Generating backup report"
cat > "$BACKUP_ROOT/backup_report_$DATE.txt" << EOF
CivicPoll Backup Report
======================
Date: $(date)
Duration: $SECONDS seconds
Status: SUCCESS

Files Backed Up:
- Database: $(du -h "$BACKUP_DIR/database.sql.gz" | cut -f1)
- Media: $(du -h "$BACKUP_DIR/media.tar.gz" | cut -f1)
- Config: $(du -h "$BACKUP_DIR/config.tar.gz" | cut -f1)
- Audit Logs: $(du -h "$BACKUP_DIR/audit_logs.csv" | cut -f1)
- Schema: $(du -h "$BACKUP_DIR/schema.sql" | cut -f1)

Total Backup Size: $(du -h "$BACKUP_ROOT/civicpoll_backup_$DATE.tar.gz.enc" | cut -f1)
Encrypted: Yes
S3 Upload: $([ -n "${S3_BUCKET:-}" ] && echo "Yes" || echo "No")
Retention: $RETENTION_DAYS days

Git Commit: $(cd /opt/civicpoll && git rev-parse HEAD 2>/dev/null || echo 'unknown')
EOF

# Cleanup temporary directory
rm -rf "$BACKUP_DIR"

# Success notification
log "Backup completed successfully"
send_alert "info" "Backup Completed" "Backup completed successfully. Size: $(du -h "$BACKUP_ROOT/civicpoll_backup_$DATE.tar.gz.enc" | cut -f1)"

# Exit successfully
exit 0