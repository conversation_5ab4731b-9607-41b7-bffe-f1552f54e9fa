#!/bin/bash

# Setup environment variables for CivicPoll deployment
# Usage: ./setup-environment.sh [environment]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Environment (development, staging, production)
ENVIRONMENT="${1:-production}"

echo -e "${BLUE}CivicPoll Environment Setup${NC}"
echo -e "${BLUE}===========================${NC}"
echo ""

# Function to prompt for input with default value
prompt_with_default() {
    local prompt="$1"
    local default="$2"
    local var_name="$3"
    
    echo -e "${YELLOW}$prompt${NC}"
    if [[ -n "$default" ]]; then
        echo -e "Default: ${GREEN}$default${NC}"
    fi
    read -p "Enter value: " input
    
    if [[ -z "$input" && -n "$default" ]]; then
        input="$default"
    fi
    
    eval "$var_name='$input'"
}

# Set default values based on environment
case "$ENVIRONMENT" in
    "development")
        DEFAULT_DOMAIN="civicpoll-dev.fr.smatflow.xyz"
        DEFAULT_HOST="civicpoll-dev.fr.smatflow.xyz"
        ;;
    "staging")
        DEFAULT_DOMAIN="civicpoll-staging.fr.smatflow.xyz"
        DEFAULT_HOST="civicpoll-staging.fr.smatflow.xyz"
        ;;
    "production")
        DEFAULT_DOMAIN="civicpoll.fr.smatflow.xyz"
        DEFAULT_HOST="civicpoll.fr.smatflow.xyz"
        ;;
    *)
        echo -e "${RED}Error: Invalid environment '$ENVIRONMENT'${NC}"
        echo "Valid environments: development, staging, production"
        exit 1
        ;;
esac

echo -e "Setting up environment: ${GREEN}$ENVIRONMENT${NC}"
echo ""

# Collect configuration
prompt_with_default "Domain name for this environment:" "$DEFAULT_DOMAIN" "DOMAIN_NAME"
prompt_with_default "Email domain (for noreply@, support@ addresses):" "civicpoll.com" "EMAIL_DOMAIN"
prompt_with_default "Deployment host (server hostname):" "$DEFAULT_HOST" "DEPLOY_HOST"

echo ""
echo -e "${BLUE}Configuration Summary:${NC}"
echo -e "Environment: ${GREEN}$ENVIRONMENT${NC}"
echo -e "Domain: ${GREEN}$DOMAIN_NAME${NC}"
echo -e "Email Domain: ${GREEN}$EMAIL_DOMAIN${NC}"
echo -e "Deploy Host: ${GREEN}$DEPLOY_HOST${NC}"
echo ""

# Confirm
read -p "Is this configuration correct? (y/N): " confirm
if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    echo "Setup cancelled."
    exit 0
fi

# Create environment file
ENV_FILE="$PROJECT_ROOT/.env.$ENVIRONMENT"
echo -e "${BLUE}Creating environment file: $ENV_FILE${NC}"

cat > "$ENV_FILE" << EOF
# CivicPoll Environment Configuration - $ENVIRONMENT
# Generated on $(date)

# Domain Configuration
DOMAIN_NAME=$DOMAIN_NAME
EMAIL_DOMAIN=$EMAIL_DOMAIN

# Deployment Configuration
DEPLOY_HOST=$DEPLOY_HOST

# Environment
NODE_ENV=$ENVIRONMENT
EOF

echo -e "${GREEN}✓ Environment file created: $ENV_FILE${NC}"

# Generate nginx configuration
echo -e "${BLUE}Generating nginx configuration...${NC}"
export DOMAIN_NAME
"$SCRIPT_DIR/generate-nginx-config.sh" "$DOMAIN_NAME"

echo ""
echo -e "${GREEN}✓ Setup complete!${NC}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Review the generated files:"
echo "   - $ENV_FILE"
echo "   - $PROJECT_ROOT/infrastructure/nginx/civicpoll.conf"
echo ""
echo "2. Load the environment variables:"
echo "   source $ENV_FILE"
echo ""
echo "3. Deploy your application using the configured environment"
echo ""
echo -e "${BLUE}For more information, see: ENVIRONMENT_VARIABLES.md${NC}"
