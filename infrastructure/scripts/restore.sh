#!/bin/bash
# CivicPoll Restore Script
# This script restores a CivicPoll backup from an encrypted archive

set -euo pipefail

# Configuration
BACKUP_ROOT="${BACKUP_ROOT:-/opt/civicpoll/backups}"
RESTORE_DIR="/tmp/civicpoll_restore_$$"
LOG_FILE="$BACKUP_ROOT/restore.log"

# Database configuration
DB_HOST="${DATABASE_HOST:-localhost}"
DB_PORT="${DATABASE_PORT:-5432}"
DB_NAME="${DATABASE_NAME:-civicpoll_fr}"
DB_USER="${DATABASE_USERNAME:-civicpoll_user}"
DB_PASSWORD="${DATABASE_PASSWORD}"

# Encryption key
BACKUP_ENCRYPTION_KEY="${BACKUP_ENCRYPTION_KEY}"

# Function to log messages
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to handle errors
handle_error() {
    local line_number="$1"
    log "ERROR: Restore failed at line $line_number"
    
    # Cleanup
    rm -rf "$RESTORE_DIR"
    
    exit 1
}

# Set error trap
trap 'handle_error $LINENO' ERR

# Check if backup file is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 <backup_file> [--dry-run]"
    echo "Example: $0 civicpoll_backup_20240101_120000.tar.gz.enc"
    exit 1
fi

BACKUP_FILE="$1"
DRY_RUN=false

if [ "${2:-}" = "--dry-run" ]; then
    DRY_RUN=true
    log "Running in dry-run mode"
fi

# Check if backup file exists
if [ ! -f "$BACKUP_FILE" ]; then
    if [ ! -f "$BACKUP_ROOT/$BACKUP_FILE" ]; then
        log "ERROR: Backup file not found: $BACKUP_FILE"
        exit 1
    fi
    BACKUP_FILE="$BACKUP_ROOT/$BACKUP_FILE"
fi

# Create restore directory
mkdir -p "$RESTORE_DIR"

log "Starting restore process from $BACKUP_FILE"

# 1. Extract encrypted backup
log "Extracting encrypted backup"
openssl enc -aes-256-cbc -d -salt -pbkdf2 -pass pass:"$BACKUP_ENCRYPTION_KEY" \
    -in "$BACKUP_FILE" | \
    tar -xzf - -C "$RESTORE_DIR"

# Find the extracted directory
EXTRACTED_DIR=$(find "$RESTORE_DIR" -mindepth 1 -maxdepth 1 -type d | head -1)

if [ -z "$EXTRACTED_DIR" ]; then
    log "ERROR: No extracted directory found"
    exit 1
fi

# 2. Verify manifest
if [ -f "$EXTRACTED_DIR/manifest.json" ]; then
    log "Backup manifest found"
    cat "$EXTRACTED_DIR/manifest.json" | jq . || true
else
    log "WARNING: No manifest file found"
fi

# 3. Verify checksums
log "Verifying backup integrity"
for file in database.sql.gz schema.sql media.tar.gz config.tar.gz audit_logs.csv; do
    if [ -f "$EXTRACTED_DIR/$file" ]; then
        current_checksum=$(sha256sum "$EXTRACTED_DIR/$file" | cut -d' ' -f1)
        if [ -f "$EXTRACTED_DIR/manifest.json" ]; then
            expected_checksum=$(jq -r ".checksums.${file%.tar.gz}" "$EXTRACTED_DIR/manifest.json" 2>/dev/null || echo "")
            if [ -n "$expected_checksum" ] && [ "$current_checksum" != "$expected_checksum" ]; then
                log "ERROR: Checksum mismatch for $file"
                exit 1
            fi
        fi
        log "Verified: $file"
    fi
done

if [ "$DRY_RUN" = true ]; then
    log "Dry run completed successfully"
    rm -rf "$RESTORE_DIR"
    exit 0
fi

# 4. Stop services
log "Stopping services"
sudo systemctl stop civicpoll-frontend civicpoll-backend || \
    pm2 stop civicpoll-frontend civicpoll-backend || true

# 5. Backup current state
log "Creating backup of current state"
CURRENT_BACKUP="$BACKUP_ROOT/pre_restore_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$CURRENT_BACKUP"

# Backup current database
PGPASSWORD="$DB_PASSWORD" pg_dump \
    -h "$DB_HOST" \
    -p "$DB_PORT" \
    -U "$DB_USER" \
    -d "$DB_NAME" \
    --no-owner \
    --no-acl \
    | gzip -9 > "$CURRENT_BACKUP/database.sql.gz" || true

# 6. Restore database
log "Restoring database"
gunzip -c "$EXTRACTED_DIR/database.sql.gz" | \
    PGPASSWORD="$DB_PASSWORD" psql \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        -v ON_ERROR_STOP=1

# 7. Restore media files
log "Restoring media files"
if [ -f "$EXTRACTED_DIR/media.tar.gz" ] && [ -s "$EXTRACTED_DIR/media.tar.gz" ]; then
    # Backup current media
    if [ -d "/opt/civicpoll/apps/backend/public/uploads" ]; then
        mv /opt/civicpoll/apps/backend/public/uploads "$CURRENT_BACKUP/uploads_backup"
    fi
    
    # Extract media files
    tar -xzf "$EXTRACTED_DIR/media.tar.gz" -C /opt/civicpoll/apps/backend/
    
    # Set proper permissions
    chown -R strapi:nodejs /opt/civicpoll/apps/backend/public/uploads || true
fi

# 8. Restore configuration files (optional - prompt user)
read -p "Do you want to restore configuration files? This will overwrite current configs (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log "Restoring configuration files"
    
    # Backup current configs
    tar -czf "$CURRENT_BACKUP/config.tar.gz" \
        -C /opt/civicpoll \
        apps/backend/config \
        apps/backend/.env \
        apps/frontend/.env.local \
        infrastructure/nginx \
        ecosystem.config.js \
        .env 2>/dev/null || true
    
    # Extract configs
    tar -xzf "$EXTRACTED_DIR/config.tar.gz" -C /opt/civicpoll/
fi

# 9. Run migrations
log "Running database migrations"
cd /opt/civicpoll/apps/backend
NODE_ENV=production pnpm strapi migration:run || true

# 10. Rebuild applications
log "Rebuilding applications"
cd /opt/civicpoll
pnpm install --frozen-lockfile
pnpm run build

# 11. Start services
log "Starting services"
sudo systemctl start civicpoll-backend civicpoll-frontend || \
    pm2 start ecosystem.config.js || true

# 12. Health check
log "Performing health check"
sleep 30

if curl -f http://localhost:1337/api/health &>/dev/null; then
    log "Backend health check: PASSED"
else
    log "WARNING: Backend health check failed"
fi

if curl -f http://localhost:3000 &>/dev/null; then
    log "Frontend health check: PASSED"
else
    log "WARNING: Frontend health check failed"
fi

# 13. Import audit logs
if [ -f "$EXTRACTED_DIR/audit_logs.csv" ]; then
    log "Importing audit logs"
    PGPASSWORD="$DB_PASSWORD" psql \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        -c "\COPY audit_logs FROM '$EXTRACTED_DIR/audit_logs.csv' CSV HEADER" || true
fi

# Cleanup
rm -rf "$RESTORE_DIR"

log "Restore completed successfully"
log "Pre-restore backup saved at: $CURRENT_BACKUP"

# Exit successfully
exit 0