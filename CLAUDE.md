# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

CivicPoll is a monorepo-based polling/survey platform with geographic targeting capabilities. It uses:
- **Backend**: Strapi v5 CMS with TypeScript, PostgreSQL/PostGIS
- **Frontend**: Next.js 15 with <PERSON>act 19, TypeScript, Tailwind CSS v4
- **Monorepo**: pnpm workspaces with Turbo
- **Auth**: OAuth2 via SMATFLOW SSO

## Essential Commands

### Development
```bash
# Install all dependencies (from root)
pnpm install

# Start both frontend and backend dev servers
pnpm dev

# Run specific app
pnpm --filter frontend dev
pnpm --filter backend dev

# Build everything
pnpm build

# Run tests
pnpm test
pnpm test:watch
pnpm test:coverage

# Lint and format
pnpm lint
pnpm format
```

### Backend-specific
```bash
cd apps/backend

# Build Strapi plugins (required after plugin changes)
pnpm build:plugins
pnpm build:plugins:watch  # For development

# Strapi console
pnpm console

# Build admin panel
pnpm build
```

### Running Tests
```bash
# Run all tests
pnpm test

# Run tests in specific workspace
pnpm --filter backend test
pnpm --filter frontend test

# Single test file
pnpm --filter backend test src/api/poll/services/poll.test.ts
```

## Architecture Overview

### Content Model Relationships
```
Organization ──> Poll ──> Question ──> Option
                  │
                  └──> Participation ──> Response
                           │
                           └──> User (with SMATFLOW OAuth)
```

### Key API Patterns
- Standard CRUD: `/api/{content-type}` (poll, organization, etc.)
- Custom actions: `/api/polls/:id/validate-participation`
- Authentication: `/api/auth/smatflow/*` for OAuth flow
- All responses follow format: `{ data: {...}, meta: {...} }`

### Frontend Routes Structure
- Public: `/`, `/polls/*`, `/auth/*`
- Protected: `/dashboard/*`, `/profile/*`, `/location/validation`
- Server actions in `/app/actions/` for mutations

### Authentication Flow
1. User redirected to SMATFLOW OAuth
2. Callback exchanges code for JWT
3. JWT stored in HTTP-only cookie
4. Location validation required post-login
5. Refresh handled automatically

### Custom Strapi Plugins
- **GDPR Plugin** (`/apps/backend/src/plugins/gdpr/`): Data export, anonymization, consent management
- **Audit Log Plugin** (`/apps/backend/src/plugins/audit-log/`): Activity tracking for compliance

### Database Requirements
- PostgreSQL with PostGIS extension enabled
- Connection: `postgres://civicpoll_user:password@localhost:5432/civicpoll`

### Environment Setup
1. **Backend** (`/apps/backend/.env`):
   - Copy `.env.example` to `.env`
   - Required: DATABASE_*, APP_KEYS, JWT secrets, EMAIL_SENDGRID_API_KEY
   - For OAuth: SMATFLOW_CLIENT_ID, SMATFLOW_CLIENT_SECRET

2. **Frontend** (`/apps/frontend/.env.local`):
   - Copy `.env.local.example` to `.env.local`
   - Required: NEXT_PUBLIC_STRAPI_API_URL, NEXT_PUBLIC_APP_URL
   - For OAuth: SMATFLOW_CLIENT_ID, SMATFLOW_AUTH_URL

### Code Conventions
- API URLs: kebab-case (`/poll-template`)
- Attributes: camelCase (`requiresValidatedLocation`)
- Content types: singular (`poll`, not `polls`)
- Strapi v5 uses `documentId` for relations
- Privacy fields marked with `private: true`
- Use Server Components by default in Next.js
- Server Actions for mutations over API calls where possible

### Testing Approach
- Jest configured for both frontend and backend
- Co-locate test files with source code
- Coverage threshold: 70%
- Test database queries with actual PostgreSQL when possible

### Deployment
- Uses Ansible playbooks in `/infrastructure/ansible/`
- Three environments: development (auto-deploy), staging, production
- Docker Compose available at `/infrastructure/docker/docker-compose.yml`
- PM2 for process management in production

### Common Development Tasks
- After modifying Strapi plugins: run `pnpm build:plugins` in backend
- Database migrations auto-run on `pnpm dev`
- Frontend hot-reloads automatically
- Strapi admin rebuilds on config/model changes

### Running One-Time Scripts
For one-time data imports (like geographic data):
```bash
cd apps/backend
pnpm import:geographic-data
```
This will build the TypeScript files and run the import script with proper Strapi context.

### Common Authentication Issues
1. **"Server Components render error" on login**: Ensure `.env.local` exists in frontend with proper API URLs
2. **OAuth redirect fails**: Verify SMATFLOW_CLIENT_ID matches between frontend and backend
3. **Local login fails**: Check that Strapi backend is running on the configured port (default 1337)
4. **CORS errors**: Backend should auto-configure CORS for frontend URL from environment