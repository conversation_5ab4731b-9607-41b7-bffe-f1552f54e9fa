# CivicPoll Phase 1: Geographic Polling MVP Development Plan

## ⚠️ IMPORTANT: Development Best Practices

**Before implementing any feature or making any changes:**

1. **ALWAYS** check the current project structure and existing files
2. **ANALYZE** the global context and current implementation status
3. **UNDERSTAND** existing patterns, conventions, and architecture decisions
4. **VERIFY** dependencies and configurations already in place
5. **NEVER** assume the state of the project - always investigate first
6. **MAINTAIN** a `PHASE_1_STATUS.md` file to track progress (completed tasks, pending items, blockers, notes)

> This plan builds upon Phase 0's secure foundation (GDPR compliance, monitoring, CI/CD) and implements the geographic polling MVP features.

## 🏗️ Technology Stack & Implementation Guidelines

### Backend (Strapi v5 - TypeScript)

- **Framework**: Strapi v5 with full TypeScript support
- **Language**: TypeScript throughout (no JavaScript files)
- **Architecture**: Content-Type based with custom services and controllers
- **Key Patterns**:
    - Use `Core.Strapi` type import (not `Strapi`)
    - Factories for basic CRUD: `factories.createCore[Controller|Service|Router]`
    - Custom business logic in dedicated service files
    - Audit logging integration for all major operations

### Frontend (Next.js v15 - Server Actions)

- **Framework**: Next.js v15 App Router with React Server Components
- **Styling**: Tailwind CSS v4
- **Architecture**: Server-side first with Server Actions
- **Key Implementation Patterns** (from `/apps/frontend/examples/`):

#### 1. **Server Actions Structure**

```typescript
"use server";
import { ActionResult } from "@/types/actions";

export async function createPollAction(formData: FormData): Promise<ActionResult<Poll>> {
    // 1. Parse FormData with Zod
    const parseResult = pollFormDataSchema.safeParse(formData);

    // 2. Validate business rules
    const validationResult = pollFormSchema.safeParse(parsedData);

    // 3. Call API using serverMutation
    const response = await serverMutation<Poll>("/polls", validationResult.data, "POST");

    // 4. Revalidate caches
    revalidateTag("polls");
    revalidatePath("/polls");

    // 5. Return standardized result
    return { success: true, data: response.data };
}
```

#### 2. **Server API Client Pattern**

```typescript
// Use React cache() for deduplication
export const fetchPolls = cache(async (params?: any): Promise<Poll[]> => {
    const response = await serverFetch<Poll[]>("/polls", {
        next: { revalidate: 60, tags: ["polls"] },
    });
    return response.data;
});
```

#### 3. **Validation with Zod**

```typescript
const pollSchema = z.object({
    title: z.string().min(1, "Title is required"),
    geographicZoneId: z.number().positive("Zone is required"),
    // ... other fields
});
```

#### 4. **Standardized Action Results**

```typescript
export type ActionResult<T = any> = {
    success: boolean;
    data?: T;
    error?: string;
    fieldErrors?: Record<string, string>;
};
```

### Implementation Rules

1. **Always use Server Actions** for form submissions and mutations
2. **Implement server-side data fetching** with proper caching strategies
3. **Use Zod schemas** for all form validation
4. **Apply React cache()** for request deduplication
5. **Implement proper error handling** with user-friendly messages
6. **Use TypeScript** throughout - no any types unless absolutely necessary
7. **Follow the existing patterns** in `/apps/frontend/examples/`

### Status Tracking File Structure

Create and maintain a `PHASE_1_STATUS.md` file with the following structure:

```markdown
# Phase 1 Implementation Status

## Last Updated: [DATE]

## Completed Tasks

- [ ] Task description - Date completed
- [ ] Another task - Date completed

## In Progress

- [ ] Current task being worked on
    - Current status
    - Any blockers

## Pending Tasks

- [ ] Future task
- [ ] Another future task

## Notes & Decisions

- Important decisions made
- Technical notes
- Dependencies installed
```

## Executive Summary

This development plan outlines the implementation of Phase 1 for the CivicPoll platform, building upon the secure foundation established in Phase 0. Phase 1 introduces the core MVP features: **geographic-based polling with mandatory location validation**, SMATFLOW SSO integration, poll creation and management, real-time analytics, and citizen participation interfaces.

**Duration:** 6-8 weeks  
**Core Deliverable:** Fully functional geographic polling system with SSO authentication  
**Key Feature:** Mandatory geographic targeting ensuring only residents can participate in their zone's polls

## ✅ Current Implementation Status (Updated December 2024)

**Overall Progress: ~85% Complete**

### ✅ **Completed Major Components:**

#### **Backend Infrastructure (100% Complete)**

- ✅ **Strapi V5 Backend** with TypeScript throughout
- ✅ **PillarScan Integration Patterns** - Analyzed and applied best practices
- ✅ **Middleware Architecture** - Custom format-request-data middleware
- ✅ **Utils Directory** - Permission setup, logging, validation utilities
- ✅ **Permission System** - Complete role-based access control with setup-permissions
- ✅ **Database Schema** - All content types with proper relations
- ✅ **User Model** - Enhanced with default Strapi fields (username, email, password, confirmed, blocked, role)
- ✅ **DocumentId Compatibility** - Controllers updated to handle documentId from frontend

#### **Frontend-Backend Compatibility (100% Complete)**

- ✅ **Type System Update** - All interfaces use `documentId` instead of numeric `id`
- ✅ **User Role Relations** - Frontend types include proper role relations
- ✅ **API Endpoint Compatibility** - Updated to match Strapi V5 patterns
- ✅ **Controller Updates** - Custom controllers handle documentId→id conversion
- ✅ **Response Format** - API responses return documentId consistently

#### **Content Types & Database (95% Complete)**

- ✅ **All Core Content Types** - Poll, Question, Option, Participation, Response
- ✅ **Geographic System** - GeographicZone with hierarchical structure
- ✅ **User Location Validation** - Complete validation system
- ✅ **Organization Management** - Multi-type organization support
- ✅ **Poll Templates** - Reusable poll templates
- ✅ **Custom Plugins** - GDPR and Audit Log plugins

#### **Services & Business Logic (90% Complete)**

- ✅ **Poll Management Service** - Creation, validation, scheduling
- ✅ **Analytics Services** - Poll analytics with caching
- ✅ **Location Validation** - French address API integration
- ✅ **Permission Validation** - Geographic boundary checking
- ✅ **Cache Management** - Redis-based caching strategies

### 🔄 **In Progress/Remaining Work:**

#### **Frontend Implementation (70% Complete)**

- ✅ **Server Actions Architecture** - Following Next.js 15 patterns
- ✅ **Type Definitions** - Updated with documentId and role relations
- ✅ **API Client** - Server-side API client with caching
- 🔄 **Authentication Flow** - SMATFLOW SSO integration (needs completion)
- 🔄 **Location Validation UI** - Address form implementation
- 🔄 **Poll Participation** - Multi-step form interface
- 🔄 **Analytics Dashboard** - Real-time charts and statistics

#### **Testing & Quality Assurance (40% Complete)**

- 🔄 **Integration Tests** - Poll participation flow testing
- 🔄 **E2E Testing** - Complete user journey validation
- 🔄 **Performance Testing** - Load testing for concurrent users
- 🔄 **Security Testing** - Geographic validation bypass attempts

#### **Production Deployment (60% Complete)**

- ✅ **Infrastructure Setup** - Docker containers and CI/CD
- 🔄 **Geographic Data Import** - French administrative boundaries
- 🔄 **Email Templates** - Notification and confirmation emails
- 🔄 **Monitoring Setup** - Custom metrics for Phase 1 features

### 🎯 **Next Priority Tasks:**

1. **Complete SMATFLOW SSO Integration** - Frontend auth flow
2. **Finish Location Validation UI** - Address autocomplete and validation
3. **Implement Poll Participation Interface** - Multi-step questionnaire
4. **Add Integration Tests** - Critical user flows
5. **Deploy Geographic Data** - French cities, departments, regions

### 🔧 **Recent Major Improvements (December 2024):**

#### **Frontend-Backend Compatibility Overhaul**

- **✅ DocumentId Implementation**: Complete transition from numeric `id` to `documentId` for all frontend operations
- **✅ Type System Refactor**: All TypeScript interfaces updated to use `documentId` consistently
- **✅ Controller Compatibility**: Custom controllers now handle `documentId` from URLs while maintaining internal `id` operations
- **✅ API Response Format**: All API responses return `documentId` for frontend consumption
- **✅ User Role System**: Added proper role relations to User types with hierarchical permissions

#### **Backend Architecture Improvements**

- **✅ PillarScan Pattern Integration**: Analyzed and applied best practices from existing production Strapi V5 implementation
- **✅ Middleware Enhancement**: Added sophisticated request data formatting middleware
- **✅ Permission System**: Complete setup-permissions utility with role-based access control
- **✅ User Model Enhancement**: Added all default Strapi fields (username, email, password, confirmed, blocked, role)
- **✅ Database Recreation**: Fresh database with all improvements and proper schema

#### **Code Quality & Standards**

- **✅ Strapi V5 Compliance**: Full compliance with Strapi V5 patterns and best practices
- **✅ TypeScript Throughout**: No JavaScript files, complete TypeScript implementation
- **✅ Error Handling**: Comprehensive error handling in all custom controllers
- **✅ Logging Integration**: Audit logging for all major operations
- **✅ Cache Strategy**: Redis-based caching with proper invalidation

## Technology Stack (Building on Phase 0)

### Backend Enhancements

- **Authentication:** SMATFLOW OAuth2 SSO
- **Geocoding:** French Government API (api-adresse.data.gouv.fr)
- **Email:** SendGrid (already configured in Phase 0)
- **Maps:** Leaflet for frontend visualization

### Frontend Enhancements

- **UI Library:** Ant Design or Material-UI
- **State Management:** Zustand or Redux Toolkit
- **Forms:** React Hook Form with Zod validation
- **Maps:** React-Leaflet
- **Charts:** Recharts or Chart.js

## Development Phases

### Phase 1: SSO Integration & User Management (Week 1)

#### 1.1 SMATFLOW OAuth2 Integration

```bash
# First, check the current authentication setup
cd apps/backend
ls -la src/extensions/users-permissions
cat config/plugins.ts || cat config/plugins.js

# Install OAuth2 dependencies
pnpm add grant-koa passport-oauth2
pnpm add -D @types/grant-koa @types/passport-oauth2
```

#### 1.1.1 OAuth2 Configuration

```typescript
// apps/backend/config/plugins.ts
export default ({ env }) => ({
    "users-permissions": {
        config: {
            grant: {
                smatflow: {
                    enabled: true,
                    key: env("SMATFLOW_CLIENT_ID"),
                    secret: env("SMATFLOW_CLIENT_SECRET"),
                    callback: "/api/auth/smatflow/callback",
                    authorize_url: "https://auth.smatflow.com/oauth/authorize",
                    access_url: "https://auth.smatflow.com/oauth/token",
                    oauth: 2,
                    scope: ["profile", "email", "address"],
                    custom_params: {
                        required_fields: ["email", "postal_code", "city"],
                    },
                },
            },
        },
    },
});

// apps/backend/src/extensions/users-permissions/strapi-server.ts
export default (plugin) => {
    // Extend the SSO callback to handle SMATFLOW specific fields
    plugin.controllers.auth.callback = async (ctx) => {
        const provider = ctx.params.provider;

        if (provider === "smatflow") {
            const { email, postal_code, city, organization_id } = ctx.query;

            // Create or update user with SMATFLOW data
            let user = await strapi.query("plugin::users-permissions.user").findOne({
                where: { email },
            });

            if (!user) {
                user = await strapi.query("plugin::users-permissions.user").create({
                    data: {
                        email,
                        username: email.split("@")[0],
                        confirmed: true,
                        provider: "smatflow",
                        smatflowOrganizationId: organization_id,
                    },
                });
            }

            // Create initial user location (to be validated)
            await strapi.query("api::user-location.user-location").create({
                data: {
                    user: user.id,
                    postalCode: postal_code,
                    city,
                    validated: false,
                    validationRequired: true,
                },
            });

            // Generate JWT
            const jwt = strapi.plugins["users-permissions"].services.jwt.issue({
                id: user.id,
            });

            // Redirect to frontend with token
            ctx.redirect(`${env("FRONTEND_URL")}/auth/callback?token=${jwt}`);
        }
    };

    return plugin;
};
```

#### 1.2 User Location Validation System

```typescript
// apps/backend/src/api/user-location/content-types/user-location/schema.json
{
    "kind": "collectionType",
    "collectionName": "user_locations",
    "info": {
        "singularName": "user-location",
        "pluralName": "user-locations",
        "displayName": "User Location"
    },
    "options": {
        "draftAndPublish": false
    },
    "pluginOptions": {},
    "attributes": {
        "user": {
            "type": "relation",
            "relation": "oneToOne",
            "target": "plugin::users-permissions.user"
        },
        "address": {
            "type": "string",
            "required": true
        },
        "postalCode": {
            "type": "string",
            "required": true
        },
        "city": {
            "type": "string",
            "required": true
        },
        "latitude": {
            "type": "decimal",
            "required": true
        },
        "longitude": {
            "type": "decimal",
            "required": true
        },
        "geographicZone": {
            "type": "relation",
            "relation": "manyToOne",
            "target": "api::geographic-zone.geographic-zone"
        },
        "validated": {
            "type": "boolean",
            "default": false
        },
        "validatedAt": {
            "type": "datetime"
        },
        "validationToken": {
            "type": "string",
            "private": true
        },
        "validationRequired": {
            "type": "boolean",
            "default": true
        }
    }
}
```

### Phase 2: Geographic Data & Zone Management (Week 2)

#### 2.1 Geographic Zone Content Types

```bash
# First check existing content types
cd apps/backend
ls -la src/api/
```

```typescript
// apps/backend/src/api/geographic-zone/content-types/geographic-zone/schema.json
{
    "kind": "collectionType",
    "collectionName": "geographic_zones",
    "info": {
        "singularName": "geographic-zone",
        "pluralName": "geographic-zones",
        "displayName": "Geographic Zone"
    },
    "options": {
        "draftAndPublish": false
    },
    "attributes": {
        "name": {
            "type": "string",
            "required": true
        },
        "code": {
            "type": "string",
            "required": true,
            "unique": true
        },
        "type": {
            "type": "enumeration",
            "enum": ["country", "region", "department", "city"],
            "required": true
        },
        "parent": {
            "type": "relation",
            "relation": "manyToOne",
            "target": "api::geographic-zone.geographic-zone"
        },
        "children": {
            "type": "relation",
            "relation": "oneToMany",
            "target": "api::geographic-zone.geographic-zone",
            "mappedBy": "parent"
        },
        "boundary": {
            "type": "json",
            "description": "GeoJSON polygon for zone boundaries"
        },
        "center": {
            "type": "json",
            "description": "Center coordinates {lat, lng}"
        },
        "population": {
            "type": "integer"
        },
        "polls": {
            "type": "relation",
            "relation": "oneToMany",
            "target": "api::poll.poll",
            "mappedBy": "geographicZone"
        }
    }
}
```

#### 2.2 Geographic Data Import Script

```typescript
// apps/backend/src/scripts/import-geographic-data.ts
import { Strapi } from "@strapi/strapi";
import axios from "axios";
import fs from "fs";
import path from "path";

const DATASETS = {
    regions: "https://geo.api.gouv.fr/regions",
    departments: "https://geo.api.gouv.fr/departements",
    communes: "https://geo.api.gouv.fr/communes",
};

export async function importGeographicData(strapi: Strapi) {
    console.log("Starting geographic data import...");

    try {
        // 1. Create France (country level)
        const france = await strapi.db.query("api::geographic-zone.geographic-zone").create({
            data: {
                name: "France",
                code: "FR",
                type: "country",
                center: { lat: 46.603354, lng: 1.888334 },
            },
        });

        // 2. Import Regions
        const regionsResponse = await axios.get(DATASETS.regions);
        const regions = regionsResponse.data;

        for (const region of regions) {
            const createdRegion = await strapi.db
                .query("api::geographic-zone.geographic-zone")
                .create({
                    data: {
                        name: region.nom,
                        code: region.code,
                        type: "region",
                        parent: france.id,
                    },
                });

            // 3. Import Departments for each region
            const depsResponse = await axios.get(
                `${DATASETS.departments}?codeRegion=${region.code}`,
            );
            const departments = depsResponse.data;

            for (const dept of departments) {
                const createdDept = await strapi.db
                    .query("api::geographic-zone.geographic-zone")
                    .create({
                        data: {
                            name: dept.nom,
                            code: dept.code,
                            type: "department",
                            parent: createdRegion.id,
                        },
                    });

                // 4. Import Cities (limit to cities > 5000 inhabitants for MVP)
                const citiesResponse = await axios.get(
                    `${DATASETS.communes}?codeDepartement=${dept.code}&fields=nom,code,codesPostaux,centre,population&format=json&geometry=centre`,
                );
                const cities = citiesResponse.data.filter((c) => c.population > 5000);

                for (const city of cities) {
                    await strapi.db.query("api::geographic-zone.geographic-zone").create({
                        data: {
                            name: city.nom,
                            code: city.code,
                            type: "city",
                            parent: createdDept.id,
                            center: {
                                lat: city.centre.coordinates[1],
                                lng: city.centre.coordinates[0],
                            },
                            population: city.population,
                        },
                    });
                }
            }
        }

        console.log("Geographic data import completed successfully");
    } catch (error) {
        console.error("Geographic data import failed:", error);
        throw error;
    }
}
```

### Phase 3: Poll Management System (Week 3)

#### 3.1 Poll Content Types

```typescript
// apps/backend/src/api/poll/content-types/poll/schema.json
{
    "kind": "collectionType",
    "collectionName": "polls",
    "info": {
        "singularName": "poll",
        "pluralName": "polls",
        "displayName": "Poll"
    },
    "options": {
        "draftAndPublish": true
    },
    "attributes": {
        "title": {
            "type": "string",
            "required": true,
            "maxLength": 200
        },
        "description": {
            "type": "richtext",
            "required": true
        },
        "category": {
            "type": "enumeration",
            "enum": ["satisfaction", "opinion", "voting", "consultation"],
            "required": true
        },
        "organization": {
            "type": "relation",
            "relation": "manyToOne",
            "target": "api::organization.organization"
        },
        "geographicZone": {
            "type": "relation",
            "relation": "manyToOne",
            "target": "api::geographic-zone.geographic-zone",
            "required": true
        },
        "startDate": {
            "type": "datetime",
            "required": true
        },
        "endDate": {
            "type": "datetime",
            "required": true
        },
        "questions": {
            "type": "relation",
            "relation": "oneToMany",
            "target": "api::question.question",
            "mappedBy": "poll"
        },
        "participations": {
            "type": "relation",
            "relation": "oneToMany",
            "target": "api::participation.participation",
            "mappedBy": "poll"
        },
        "isAnonymous": {
            "type": "boolean",
            "default": true
        },
        "requiresValidatedLocation": {
            "type": "boolean",
            "default": true
        },
        "status": {
            "type": "enumeration",
            "enum": ["draft", "scheduled", "active", "closed", "archived"],
            "default": "draft"
        },
        "template": {
            "type": "relation",
            "relation": "manyToOne",
            "target": "api::poll-template.poll-template"
        }
    }
}

// apps/backend/src/api/question/content-types/question/schema.json
{
    "kind": "collectionType",
    "collectionName": "questions",
    "info": {
        "singularName": "question",
        "pluralName": "questions",
        "displayName": "Question"
    },
    "options": {
        "draftAndPublish": false
    },
    "attributes": {
        "poll": {
            "type": "relation",
            "relation": "manyToOne",
            "target": "api::poll.poll",
            "inversedBy": "questions"
        },
        "text": {
            "type": "string",
            "required": true
        },
        "type": {
            "type": "enumeration",
            "enum": ["single_choice", "multiple_choice", "rating", "text", "ranking"],
            "required": true
        },
        "required": {
            "type": "boolean",
            "default": true
        },
        "order": {
            "type": "integer",
            "required": true
        },
        "options": {
            "type": "relation",
            "relation": "oneToMany",
            "target": "api::option.option",
            "mappedBy": "question"
        },
        "ratingScale": {
            "type": "json",
            "description": "For rating questions: {min: 1, max: 5, labels: {...}}"
        }
    }
}
```

#### 3.2 Poll Management Service

```typescript
// apps/backend/src/api/poll/services/poll-management.ts
import { Strapi } from "@strapi/strapi";

export default ({ strapi }: { strapi: Strapi }) => ({
    async createPoll(data: any, organizationId: number) {
        // Validate geographic zone
        const zone = await strapi.db.query("api::geographic-zone.geographic-zone").findOne({
            where: { id: data.geographicZoneId },
        });

        if (!zone) {
            throw new Error("Invalid geographic zone");
        }

        // Create poll
        const poll = await strapi.db.query("api::poll.poll").create({
            data: {
                ...data,
                organization: organizationId,
                geographicZone: zone.id,
                status: "draft",
                publishedAt: null,
            },
        });

        // Schedule activation if start date is in the future
        if (new Date(data.startDate) > new Date()) {
            await this.schedulePollActivation(poll.id, data.startDate);
        }

        // Log creation event
        await strapi
            .plugin("audit-log")
            .service("auditLog")
            .create({
                event: "POLL_CREATED",
                entityId: poll.id,
                entityType: "poll",
                performedBy: data.createdBy,
                details: {
                    title: poll.title,
                    zone: zone.name,
                    zoneType: zone.type,
                },
            });

        return poll;
    },

    async validateUserParticipation(userId: number, pollId: number) {
        // Get poll with zone
        const poll = await strapi.db.query("api::poll.poll").findOne({
            where: { id: pollId },
            populate: ["geographicZone"],
        });

        if (!poll) {
            throw new Error("Poll not found");
        }

        // Check if poll is active
        const now = new Date();
        if (now < new Date(poll.startDate) || now > new Date(poll.endDate)) {
            throw new Error("Poll is not active");
        }

        // Get user location
        const userLocation = await strapi.db.query("api::user-location.user-location").findOne({
            where: { user: userId },
            populate: ["geographicZone"],
        });

        if (!userLocation || !userLocation.validated) {
            throw new Error("User location not validated");
        }

        // Check if user is in the correct zone
        const isInZone = await this.checkUserInZone(
            userLocation.geographicZone,
            poll.geographicZone,
        );

        if (!isInZone) {
            throw new Error("User is not in the poll geographic zone");
        }

        // Check if user already participated
        const existingParticipation = await strapi.db
            .query("api::participation.participation")
            .findOne({
                where: {
                    user: userId,
                    poll: pollId,
                },
            });

        if (existingParticipation && existingParticipation.completed) {
            throw new Error("User already participated in this poll");
        }

        return true;
    },

    async checkUserInZone(userZone: any, pollZone: any): Promise<boolean> {
        // Check if zones match or if user zone is a child of poll zone
        if (userZone.id === pollZone.id) {
            return true;
        }

        // Check hierarchy (e.g., user in a city that belongs to a department)
        let currentZone = userZone;
        while (currentZone.parent) {
            if (currentZone.parent === pollZone.id) {
                return true;
            }
            currentZone = await strapi.db.query("api::geographic-zone.geographic-zone").findOne({
                where: { id: currentZone.parent },
            });
        }

        return false;
    },
});
```

### Phase 4: Frontend Implementation (Week 4)

#### 4.1 Frontend Architecture Setup

```bash
# Check existing frontend structure
cd apps/frontend
ls -la src/
cat package.json

# Install required dependencies
pnpm add react-leaflet leaflet recharts zod react-hook-form @hookform/resolvers
pnpm add -D @types/leaflet
```

#### 4.1.1 Server API Client (Following Project Patterns)

```typescript
// apps/frontend/src/lib/server-api.ts
import { cache } from "react";
import { cookies } from "next/headers";
import type { Poll, UserLocation, GeographicZone } from "@/types";

const API_URL = process.env.STRAPI_API_URL || "http://localhost:1337/api";

async function serverFetch<T>(endpoint: string, options: RequestInit = {}): Promise<{ data: T }> {
    const cookieStore = cookies();
    const token = cookieStore.get("authToken")?.value;

    const response = await fetch(`${API_URL}${endpoint}`, {
        ...options,
        headers: {
            "Content-Type": "application/json",
            ...(token && { Authorization: `Bearer ${token}` }),
            ...options.headers,
        },
    });

    if (!response.ok) {
        throw new Error(`API Error: ${response.statusText}`);
    }

    return response.json();
}

// Cached data fetching functions
export const fetchPolls = cache(async (zoneId?: number): Promise<Poll[]> => {
    const params = zoneId ? `?filters[geographicZone][id][$eq]=${zoneId}` : "";
    const response = await serverFetch<Poll[]>(`/polls${params}&populate=*`, {
        next: { revalidate: 60, tags: ["polls"] },
    });
    return response.data;
});

export const fetchUserLocation = cache(async (): Promise<UserLocation | null> => {
    try {
        const response = await serverFetch<UserLocation>("/user-locations/mine", {
            cache: "no-store", // Always fresh for user data
        });
        return response.data;
    } catch {
        return null;
    }
});

export const fetchGeographicZones = cache(async (): Promise<GeographicZone[]> => {
    const response = await serverFetch<GeographicZone[]>("/geographic-zones?populate=*", {
        next: { revalidate: 3600 }, // Cache for 1 hour
    });
    return response.data;
});
```

#### 4.2 Location Validation Flow

```typescript
// apps/frontend/src/app/location/validation/actions.ts
"use server";

import { z } from 'zod';
import { revalidatePath, revalidateTag } from 'next/cache';
import { cookies } from 'next/headers';
import { ActionResult } from '@/types/actions';

const locationValidationSchema = z.object({
    address: z.string().min(1, "L'adresse est requise"),
    postalCode: z.string().regex(/^\d{5}$/, "Code postal invalide"),
    city: z.string().min(1, "La ville est requise"),
    latitude: z.number(),
    longitude: z.number()
});

export async function validateLocationAction(
    formData: FormData
): Promise<ActionResult<UserLocation>> {
    try {
        // Parse form data
        const data = {
            address: formData.get('address') as string,
            postalCode: formData.get('postalCode') as string,
            city: formData.get('city') as string,
            latitude: parseFloat(formData.get('latitude') as string),
            longitude: parseFloat(formData.get('longitude') as string)
        };

        // Validate data
        const validationResult = locationValidationSchema.safeParse(data);
        if (!validationResult.success) {
            return {
                success: false,
                fieldErrors: validationResult.error.flatten().fieldErrors
            };
        }

        // Call API
        const response = await serverMutation<UserLocation>(
            '/user-locations/validate',
            validationResult.data,
            'POST'
        );

        // Revalidate caches
        revalidateTag('user-location');
        revalidatePath('/dashboard');

        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        return {
            success: false,
            error: "Erreur lors de la validation de l'adresse"
        };
    }
}

// apps/frontend/src/components/LocationValidation/AddressForm.tsx
import { useState } from 'react';
import { useActionState } from 'react-dom';
import { validateLocationAction } from '@/app/location/validation/actions';

export function AddressForm() {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedAddress, setSelectedAddress] = useState<any>(null);
    const [suggestions, setSuggestions] = useState<any[]>([]);
    const [state, formAction, isPending] = useActionState(validateLocationAction, { success: false });

    const searchAddresses = async (query: string) => {
        if (query.length < 3) return;

        const response = await fetch(
            `https://api-adresse.data.gouv.fr/search/?q=${encodeURIComponent(query)}&limit=5`
        );
        const data = await response.json();
        setSuggestions(data.features);
    };

    return (
        <div className="max-w-2xl mx-auto p-6">
            <h2 className="text-2xl font-bold mb-6">Validation de votre adresse</h2>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <p className="text-sm text-blue-800">
                    Pour participer aux sondages de votre zone, vous devez valider votre adresse de résidence.
                </p>
            </div>

            <form action={formAction}>
                <div className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium mb-1">
                            Recherchez votre adresse
                        </label>
                        <input
                            type="text"
                            value={searchQuery}
                            onChange={(e) => {
                                setSearchQuery(e.target.value);
                                searchAddresses(e.target.value);
                            }}
                            className="w-full px-3 py-2 border rounded-lg"
                            placeholder="Commencez à taper votre adresse..."
                        />
                    </div>

                    {suggestions.length > 0 && (
                        <div className="border rounded-lg divide-y">
                            {suggestions.map((suggestion) => (
                                <button
                                    key={suggestion.properties.id}
                                    type="button"
                                    onClick={() => {
                                        setSelectedAddress(suggestion);
                                        setSearchQuery(suggestion.properties.label);
                                        setSuggestions([]);
                                    }}
                                    className="w-full text-left p-3 hover:bg-gray-50"
                                >
                                    <div className="font-medium">
                                        {suggestion.properties.label}
                                    </div>
                                    <div className="text-sm text-gray-500">
                                        {suggestion.properties.postcode} {suggestion.properties.city}
                                    </div>
                                </button>
                            ))}
                        </div>
                    )}

                    {selectedAddress && (
                        <>
                            <input type="hidden" name="address" value={selectedAddress.properties.label} />
                            <input type="hidden" name="postalCode" value={selectedAddress.properties.postcode} />
                            <input type="hidden" name="city" value={selectedAddress.properties.city} />
                            <input type="hidden" name="latitude" value={selectedAddress.geometry.coordinates[1]} />
                            <input type="hidden" name="longitude" value={selectedAddress.geometry.coordinates[0]} />

                            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                <h4 className="font-semibold mb-2">Adresse sélectionnée :</h4>
                                <p>{selectedAddress.properties.label}</p>
                                <p className="text-sm text-gray-600">
                                    {selectedAddress.properties.postcode} {selectedAddress.properties.city}
                                </p>
                            </div>
                        </>
                    )}

                    {state.error && (
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                            <p className="text-red-800">{state.error}</p>
                        </div>
                    )}

                    <button
                        type="submit"
                        disabled={!selectedAddress}
                        className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-700"
                    >
                        Valider mon adresse
                    </button>
                </div>
            </form>
        </div>
    );
}
```

#### 4.3 Poll Participation Interface

```typescript
// apps/frontend/src/app/polls/[id]/participate/actions.ts
"use server";

import { z } from 'zod';
import { revalidatePath, revalidateTag } from 'next/cache';
import { ActionResult } from '@/types/actions';

const participationSchema = z.object({
    pollId: z.string(),
    responses: z.record(z.any())
});

export async function submitParticipationAction(
    formData: FormData
): Promise<ActionResult<Participation>> {
    try {
        const pollId = formData.get('pollId') as string;
        const responsesJson = formData.get('responses') as string;
        const responses = JSON.parse(responsesJson);

        // Validate participation eligibility
        const validationResponse = await serverFetch(
            `/polls/${pollId}/validate-participation`,
            { method: 'POST' }
        );

        if (!validationResponse.data.canParticipate) {
            return {
                success: false,
                error: "Vous n'êtes pas éligible pour ce sondage"
            };
        }

        // Submit participation
        const response = await serverMutation<Participation>(
            '/participations',
            {
                poll: pollId,
                responses,
                completedAt: new Date().toISOString()
            },
            'POST'
        );

        // Revalidate caches
        revalidateTag('participations');
        revalidateTag(`poll-${pollId}`);
        revalidatePath(`/polls/${pollId}/results`);

        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        return {
            success: false,
            error: "Erreur lors de la soumission"
        };
    }
}

// apps/frontend/src/components/Poll/PollParticipation.tsx
import { useState } from 'react';
import { useActionState } from 'react-dom';
import { submitParticipationAction } from '@/app/polls/[id]/participate/actions';

interface PollParticipationProps {
    poll: Poll;
}

export function PollParticipation({ poll }: PollParticipationProps) {
    const [currentQuestion, setCurrentQuestion] = useState(0);
    const [responses, setResponses] = useState<Record<string, any>>({});
    const [state, formAction, isPending] = useActionState(submitParticipationAction, { success: false });

    const question = poll.attributes.questions.data[currentQuestion];
    const progress = ((currentQuestion + 1) / poll.attributes.questions.data.length) * 100;

    const handleNext = () => {
        if (currentQuestion < poll.questions.length - 1) {
            setCurrentQuestion(currentQuestion + 1);
        }
    };

    const renderQuestion = () => {
        switch (question.type) {
            case 'single_choice':
                return (
                    <div className="space-y-3">
                        {question.options.map((option: any) => (
                            <label key={option.id} className="flex items-center p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                                <input
                                    type="radio"
                                    name={`question-${question.id}`}
                                    value={option.id}
                                    checked={responses[question.id] === option.id}
                                    onChange={(e) => setResponses({
                                        ...responses,
                                        [question.id]: e.target.value
                                    })}
                                    className="mr-3"
                                />
                                <span className="text-lg">{option.text}</span>
                            </label>
                        ))}
                    </div>
                );

            case 'multiple_choice':
                return (
                    <div className="space-y-3">
                        {question.options.map((option: any) => (
                            <label key={option.id} className="flex items-center p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                                <input
                                    type="checkbox"
                                    value={option.id}
                                    checked={(responses[question.id] || []).includes(option.id)}
                                    onChange={(e) => {
                                        const current = responses[question.id] || [];
                                        const updated = e.target.checked
                                            ? [...current, option.id]
                                            : current.filter((id: string) => id !== option.id);
                                        setResponses({
                                            ...responses,
                                            [question.id]: updated
                                        });
                                    }}
                                    className="mr-3"
                                />
                                <span className="text-lg">{option.text}</span>
                            </label>
                        ))}
                    </div>
                );

            case 'rating':
                return (
                    <div className="flex justify-center space-x-2 py-4">
                        {[1, 2, 3, 4, 5].map((value) => (
                            <button
                                key={value}
                                type="button"
                                onClick={() => setResponses({
                                    ...responses,
                                    [question.id]: value
                                })}
                                className={`w-12 h-12 rounded-full text-2xl ${
                                    responses[question.id] >= value
                                        ? 'bg-yellow-400 text-white'
                                        : 'bg-gray-200 text-gray-500'
                                } hover:bg-yellow-300`}
                            >
                                ★
                            </button>
                        ))}
                    </div>
                );

            case 'text':
                return (
                    <textarea
                        value={responses[question.id] || ''}
                        onChange={(e) => setResponses({
                            ...responses,
                            [question.id]: e.target.value
                        })}
                        rows={4}
                        maxLength={500}
                        className="w-full px-3 py-2 border rounded-lg resize-none"
                        placeholder="Votre réponse..."
                    />
                );

            default:
                return null;
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        const form = e.target as HTMLFormElement;
        const formData = new FormData(form);
        formData.append('pollId', poll.id);
        formData.append('responses', JSON.stringify(responses));
        formAction(formData);
    };

    if (state.success) {
        return (
            <div className="max-w-3xl mx-auto p-6">
                <div className="bg-green-50 border border-green-200 rounded-lg p-8 text-center">
                    <h2 className="text-2xl font-bold text-green-800 mb-4">
                        Merci pour votre participation !
                    </h2>
                    <p className="text-green-700">
                        Vos réponses ont été enregistrées avec succès.
                    </p>
                    <a href={`/polls/${poll.id}/results`} className="mt-6 inline-block bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700">
                        Voir les résultats
                    </a>
                </div>
            </div>
        );
    }

    return (
        <div className="max-w-3xl mx-auto p-6">
            <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="w-full bg-gray-200 rounded-full h-2 mb-6">
                    <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progress}%` }}
                    />
                </div>

                <h2 className="text-2xl font-bold mb-2">{poll.title}</h2>
                <p className="text-gray-600 mb-6">
                    Question {currentQuestion + 1} sur {poll.questions.length}
                </p>

                <form onSubmit={handleSubmit}>
                    <div className="mb-8">
                        <h3 className="text-xl mb-4">{question.text}</h3>
                        {renderQuestion()}
                    </div>

                    {state.error && (
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                            <p className="text-red-800">{state.error}</p>
                        </div>
                    )}

                    <div className="flex justify-between">
                        <button
                            type="button"
                            onClick={() => setCurrentQuestion(currentQuestion - 1)}
                            disabled={currentQuestion === 0}
                            className="px-4 py-2 border rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                        >
                            Précédent
                        </button>

                        {currentQuestion === poll.questions.length - 1 ? (
                            <button
                                type="submit"
                                disabled={!responses[question.id] && question.required}
                                className="px-6 py-2 bg-blue-600 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-700"
                            >
                                Terminer
                            </button>
                        ) : (
                            <button
                                type="button"
                                onClick={handleNext}
                                disabled={!responses[question.id] && question.required}
                                className="px-6 py-2 bg-blue-600 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-700"
                            >
                                Suivant
                            </button>
                        )}
                    </div>
                </form>
            </div>
        </div>
    );
}
```

### Phase 5: Analytics & Monitoring (Week 5)

#### 5.1 Real-time Analytics Dashboard

```typescript
// apps/backend/src/api/poll/controllers/analytics.ts
export default {
    async getAnalytics(ctx) {
        const { id } = ctx.params;
        const poll = await strapi.db.query("api::poll.poll").findOne({
            where: { id },
            populate: ["geographicZone", "questions.options"],
        });

        if (!poll) {
            return ctx.notFound("Poll not found");
        }

        // Get participation stats
        const participations = await strapi.db.query("api::participation.participation").findMany({
            where: { poll: id, completed: true },
        });

        const totalPopulation = poll.geographicZone.population || 100000;
        const participationRate = (participations.length / totalPopulation) * 100;

        // Get responses by question
        const responsesByQuestion = await strapi.db.connection.raw(
            `
            SELECT 
                q.id as question_id,
                q.text as question_text,
                q.type as question_type,
                r.value,
                COUNT(*) as count
            FROM responses r
            JOIN questions q ON r.question = q.id
            WHERE q.poll = ?
            GROUP BY q.id, q.text, q.type, r.value
        `,
            [id],
        );

        // Get geographic distribution
        const geoDistribution = await strapi.db.connection.raw(
            `
            SELECT 
                gz.name as zone_name,
                gz.type as zone_type,
                COUNT(DISTINCT p.user) as participants
            FROM participations p
            JOIN user_locations ul ON p.user = ul.user
            JOIN geographic_zones gz ON ul.geographic_zone = gz.id
            WHERE p.poll = ?
            GROUP BY gz.id, gz.name, gz.type
        `,
            [id],
        );

        // Get temporal data
        const temporalData = await strapi.db.connection.raw(
            `
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as daily_participations
            FROM participations
            WHERE poll = ? AND completed = true
            GROUP BY DATE(created_at)
            ORDER BY date
        `,
            [id],
        );

        return {
            poll: {
                id: poll.id,
                title: poll.title,
                startDate: poll.startDate,
                endDate: poll.endDate,
                status: poll.pollStatus,
            },
            statistics: {
                totalParticipants: participations.length,
                targetPopulation: totalPopulation,
                participationRate: participationRate.toFixed(2),
                averageCompletionTime: this.calculateAverageCompletionTime(participations),
                completionRate: this.calculateCompletionRate(poll.id),
            },
            responses: this.formatResponseData(responsesByQuestion),
            geographic: {
                distribution: geoDistribution,
                mainZone: poll.geographicZone.name,
                zoneType: poll.geographicZone.type,
            },
            temporal: temporalData,
        };
    },
};
```

#### 5.2 Analytics Dashboard Frontend

```typescript
// apps/frontend/src/components/Analytics/PollAnalytics.tsx
import React from 'react';
import { Card, Row, Col, Statistic, Table } from 'antd';
import { UserOutlined, ClockCircleOutlined, GlobalOutlined } from '@ant-design/icons';
import { BarChart, Bar, LineChart, Line, PieChart, Pie, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';
import { useQuery } from '@tanstack/react-query';
import apiClient from '@/lib/api-client';

interface PollAnalyticsProps {
    pollId: string;
}

export const PollAnalytics: React.FC<PollAnalyticsProps> = ({ pollId }) => {
    const { data: analytics, isLoading } = useQuery({
        queryKey: ['poll-analytics', pollId],
        queryFn: async () => {
            const response = await apiClient.get(`/polls/${pollId}/analytics`);
            return response.data;
        },
        refetchInterval: 30000 // Refresh every 30 seconds
    });

    if (isLoading) return <Spin size="large" />;

    return (
        <div className="p-6">
            <h1 className="text-3xl font-bold mb-6">{analytics.poll.title}</h1>

            {/* Key Statistics */}
            <Row gutter={16} className="mb-6">
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="Participants"
                            value={analytics.statistics.totalParticipants}
                            prefix={<UserOutlined />}
                            suffix={`/ ${analytics.statistics.targetPopulation}`}
                        />
                    </Card>
                </Col>
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="Taux de participation"
                            value={analytics.statistics.participationRate}
                            suffix="%"
                            precision={2}
                        />
                    </Card>
                </Col>
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="Temps moyen"
                            value={analytics.statistics.averageCompletionTime}
                            suffix="min"
                            prefix={<ClockCircleOutlined />}
                        />
                    </Card>
                </Col>
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="Zone géographique"
                            value={analytics.geographic.mainZone}
                            prefix={<GlobalOutlined />}
                        />
                    </Card>
                </Col>
            </Row>

            {/* Temporal Chart */}
            <Card title="Évolution de la participation" className="mb-6">
                <LineChart width={800} height={300} data={analytics.temporal}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line
                        type="monotone"
                        dataKey="daily_participations"
                        stroke="#1890ff"
                        name="Participations"
                    />
                </LineChart>
            </Card>

            {/* Geographic Distribution */}
            <Card title="Répartition géographique" className="mb-6">
                <Table
                    dataSource={analytics.geographic.distribution}
                    columns={[
                        {
                            title: 'Zone',
                            dataIndex: 'zone_name',
                            key: 'zone_name'
                        },
                        {
                            title: 'Type',
                            dataIndex: 'zone_type',
                            key: 'zone_type'
                        },
                        {
                            title: 'Participants',
                            dataIndex: 'participants',
                            key: 'participants',
                            sorter: (a, b) => a.participants - b.participants
                        }
                    ]}
                    pagination={false}
                />
            </Card>

            {/* Response Charts */}
            {analytics.responses.map((question: any) => (
                <Card
                    key={question.questionId}
                    title={question.questionText}
                    className="mb-6"
                >
                    {question.type === 'single_choice' && (
                        <PieChart width={400} height={300}>
                            <Pie
                                data={question.data}
                                dataKey="count"
                                nameKey="label"
                                cx="50%"
                                cy="50%"
                                outerRadius={100}
                                fill="#1890ff"
                                label
                            />
                            <Tooltip />
                        </PieChart>
                    )}

                    {question.type === 'rating' && (
                        <BarChart width={600} height={300} data={question.data}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="rating" />
                            <YAxis />
                            <Tooltip />
                            <Bar dataKey="count" fill="#1890ff" />
                        </BarChart>
                    )}
                </Card>
            ))}
        </div>
    );
};
```

### Phase 6: Testing & Deployment (Week 6)

#### 6.1 Testing Strategy

```typescript
// apps/backend/tests/integration/poll-participation.test.ts
import { describe, it, expect, beforeAll, afterAll } from "@jest/globals";
import request from "supertest";
import { setupStrapi, teardownStrapi } from "../helpers";

describe("Poll Participation Flow", () => {
    let app;
    let authToken;
    let testPoll;
    let testUser;

    beforeAll(async () => {
        app = await setupStrapi();

        // Create test user with validated location
        const userResponse = await request(app.server).post("/api/auth/local/register").send({
            username: "testcitizen",
            email: "<EMAIL>",
            password: "Test123!@#",
        });

        testUser = userResponse.body.user;
        authToken = userResponse.body.jwt;

        // Validate user location
        await request(app.server)
            .post("/api/user-locations/validate")
            .set("Authorization", `Bearer ${authToken}`)
            .send({
                address: "1 rue de la Paix",
                postalCode: "75001",
                city: "Paris",
                latitude: 48.8566,
                longitude: 2.3522,
            });

        // Create test poll
        testPoll = await strapi.db.query("api::poll.poll").create({
            data: {
                title: "Test Poll",
                description: "Test poll for Paris residents",
                category: "opinion",
                geographicZone: 1, // Paris
                startDate: new Date(),
                endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
                status: "active",
                publishedAt: new Date(),
            },
        });
    });

    afterAll(async () => {
        await teardownStrapi();
    });

    describe("Participation Validation", () => {
        it("should allow users in the correct zone to participate", async () => {
            const response = await request(app.server)
                .post(`/api/polls/${testPoll.id}/validate-participation`)
                .set("Authorization", `Bearer ${authToken}`)
                .expect(200);

            expect(response.body.canParticipate).toBe(true);
        });

        it("should prevent users without validated location", async () => {
            // Create user without location
            const newUser = await request(app.server).post("/api/auth/local/register").send({
                username: "noaddress",
                email: "<EMAIL>",
                password: "Test123!@#",
            });

            await request(app.server)
                .post(`/api/polls/${testPoll.id}/validate-participation`)
                .set("Authorization", `Bearer ${newUser.body.jwt}`)
                .expect(403);
        });

        it("should prevent duplicate participation", async () => {
            // First participation
            await request(app.server)
                .post("/api/participations")
                .set("Authorization", `Bearer ${authToken}`)
                .send({
                    poll: testPoll.id,
                    responses: { "1": "option_1" },
                    completed: true,
                })
                .expect(201);

            // Attempt second participation
            await request(app.server)
                .post(`/api/polls/${testPoll.id}/validate-participation`)
                .set("Authorization", `Bearer ${authToken}`)
                .expect(403);
        });
    });

    describe("Geographic Restrictions", () => {
        it("should enforce geographic boundaries", async () => {
            // Create user in different city
            const lyonUser = await request(app.server).post("/api/auth/local/register").send({
                username: "lyoncitizen",
                email: "<EMAIL>",
                password: "Test123!@#",
            });

            // Validate Lyon address
            await request(app.server)
                .post("/api/user-locations/validate")
                .set("Authorization", `Bearer ${lyonUser.body.jwt}`)
                .send({
                    address: "1 place Bellecour",
                    postalCode: "69002",
                    city: "Lyon",
                    latitude: 45.7578,
                    longitude: 4.832,
                });

            // Try to participate in Paris poll
            await request(app.server)
                .post(`/api/polls/${testPoll.id}/validate-participation`)
                .set("Authorization", `Bearer ${lyonUser.body.jwt}`)
                .expect(403);
        });
    });
});
```

#### 6.2 Production Deployment Updates

```yaml
# .gitlab-ci.yml (additions for Phase 1)

# New job for geographic data import
import-geo-data:
    stage: deploy
    image: node:18
    needs: ["deploy:production"]
    script:
        - |
            ssh $DEPLOY_USER@$DEPLOY_HOST << 'EOF'
              cd /opt/civicpoll/apps/backend
              pnpm run strapi script import-geographic-data
            EOF
    only:
        - main
    when: manual

# Email template deployment
deploy-email-templates:
    stage: deploy
    image: node:18
    needs: ["deploy:production"]
    script:
        - |
            ssh $DEPLOY_USER@$DEPLOY_HOST << 'EOF'
              cd /opt/civicpoll
              ./infrastructure/scripts/deploy-email-templates.sh
            EOF
    only:
        - main
```

### Phase 7: Performance Optimization (Week 7)

#### 7.1 Database Indexing

```sql
-- apps/backend/database/migrations/phase1-indexes.sql

-- Geographic queries optimization
CREATE INDEX idx_geographic_zones_type ON geographic_zones(type);
CREATE INDEX idx_geographic_zones_parent ON geographic_zones(parent);
CREATE INDEX idx_geographic_zones_code ON geographic_zones(code);

-- User location queries
CREATE INDEX idx_user_locations_user ON user_locations(user);
CREATE INDEX idx_user_locations_validated ON user_locations(validated);
CREATE INDEX idx_user_locations_geographic_zone ON user_locations(geographic_zone);

-- Poll queries
CREATE INDEX idx_polls_geographic_zone ON polls(geographic_zone);
CREATE INDEX idx_polls_status ON polls(status);
CREATE INDEX idx_polls_dates ON polls(start_date, end_date);
CREATE INDEX idx_polls_organization ON polls(organization);

-- Participation queries
CREATE INDEX idx_participations_user_poll ON participations(user, poll);
CREATE INDEX idx_participations_completed ON participations(completed);
CREATE INDEX idx_participations_created_at ON participations(created_at);

-- Response queries
CREATE INDEX idx_responses_participation ON responses(participation);
CREATE INDEX idx_responses_question ON responses(question);

-- Spatial index for location queries
CREATE INDEX idx_user_locations_coordinates ON user_locations USING GIST (
    ST_MakePoint(longitude, latitude)
);
```

#### 7.2 Redis Caching Strategy

```typescript
// apps/backend/src/services/cache.ts
import Redis from "ioredis";

const redis = new Redis({
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT,
    password: process.env.REDIS_PASSWORD,
});

export const cacheService = {
    // Cache geographic zones (rarely change)
    async getGeographicZones() {
        const cached = await redis.get("geographic:zones:all");
        if (cached) {
            return JSON.parse(cached);
        }

        const zones = await strapi.db.query("api::geographic-zone.geographic-zone").findMany({
            populate: ["parent", "children"],
        });

        await redis.set("geographic:zones:all", JSON.stringify(zones), "EX", 86400); // 24h
        return zones;
    },

    // Cache active polls by zone
    async getActivePollsByZone(zoneId: number) {
        const key = `polls:active:zone:${zoneId}`;
        const cached = await redis.get(key);
        if (cached) {
            return JSON.parse(cached);
        }

        const polls = await strapi.db.query("api::poll.poll").findMany({
            where: {
                geographicZone: zoneId,
                status: "active",
                startDate: { $lte: new Date() },
                endDate: { $gte: new Date() },
            },
        });

        await redis.set(key, JSON.stringify(polls), "EX", 300); // 5 minutes
        return polls;
    },

    // Cache analytics data
    async getPollAnalytics(pollId: number) {
        const key = `analytics:poll:${pollId}`;
        const cached = await redis.get(key);
        if (cached) {
            return JSON.parse(cached);
        }

        // Analytics calculation logic here...
        const analytics = await calculatePollAnalytics(pollId);

        await redis.set(key, JSON.stringify(analytics), "EX", 60); // 1 minute
        return analytics;
    },

    // Invalidate caches
    async invalidatePollCache(pollId: number) {
        const poll = await strapi.db.query("api::poll.poll").findOne({
            where: { id: pollId },
            populate: ["geographicZone"],
        });

        await redis.del(`analytics:poll:${pollId}`);
        await redis.del(`polls:active:zone:${poll.geographicZone.id}`);
    },
};
```

### Phase 8: Monitoring & Analytics Enhancement (Week 8)

#### 8.1 Custom Metrics for Phase 1

```typescript
// apps/backend/src/services/metrics.ts
import { Registry, Counter, Gauge, Histogram } from "prom-client";

const register = new Registry();

// Poll metrics
export const pollsCreated = new Counter({
    name: "civicpoll_polls_created_total",
    help: "Total number of polls created",
    labelNames: ["zone_type", "category"],
    registers: [register],
});

export const activePolls = new Gauge({
    name: "civicpoll_active_polls",
    help: "Number of currently active polls",
    labelNames: ["zone_type"],
    registers: [register],
});

export const participationRate = new Gauge({
    name: "civicpoll_participation_rate",
    help: "Average participation rate across all polls",
    labelNames: ["zone_type"],
    registers: [register],
});

// User metrics
export const userRegistrations = new Counter({
    name: "civicpoll_user_registrations_total",
    help: "Total number of user registrations",
    labelNames: ["provider"],
    registers: [register],
});

export const validatedLocations = new Counter({
    name: "civicpoll_validated_locations_total",
    help: "Total number of validated user locations",
    labelNames: ["zone_type"],
    registers: [register],
});

// Performance metrics
export const apiResponseTime = new Histogram({
    name: "civicpoll_api_response_time_seconds",
    help: "API response time in seconds",
    labelNames: ["method", "route", "status"],
    buckets: [0.1, 0.5, 1, 2, 5],
    registers: [register],
});

export const geocodingResponseTime = new Histogram({
    name: "civicpoll_geocoding_response_time_seconds",
    help: "Geocoding API response time in seconds",
    buckets: [0.1, 0.3, 0.5, 1, 2],
    registers: [register],
});

export { register };
```

## Validation Criteria

### Pre-Production Checklist

1. **Authentication & Authorization**

    - [ ] SMATFLOW SSO integration functional
    - [ ] User location validation working
    - [ ] Geographic restrictions enforced
    - [ ] Organization permissions verified

2. **Core Features**

    - [ ] Poll creation with mandatory geographic targeting
    - [ ] All question types functional
    - [ ] Real-time analytics updating
    - [ ] Email notifications sending

3. **Data Integrity**

    - [ ] Geographic data imported correctly
    - [ ] User responses anonymous but trackable
    - [ ] Participation uniqueness enforced
    - [ ] Data export maintaining privacy

4. **Performance**

    - [ ] Page load times < 2 seconds
    - [ ] API response times < 500ms
    - [ ] Analytics dashboard responsive
    - [ ] Geographic queries optimized

5. **User Experience**
    - [ ] Address validation intuitive
    - [ ] Poll participation smooth
    - [ ] Results immediately visible
    - [ ] Mobile responsive

## Timeline Summary

- **Week 1**: SSO integration and user management
- **Week 2**: Geographic data import and zone management
- **Week 3**: Poll creation and management system
- **Week 4**: Frontend implementation
- **Week 5**: Analytics and monitoring
- **Week 6**: Testing and initial deployment
- **Week 7**: Performance optimization
- **Week 8**: Final validation and production deployment

## Next Steps (Phase 2 Preview)

After Phase 1 validation, Phase 2 will introduce:

- Multi-language support (French, English, Arabic)
- Advanced question types (conditional logic, media)
- Mobile applications (iOS/Android)
- Social sharing capabilities
- Advanced analytics with ML insights
- Bulk invitation system

## Conclusion

This Phase 1 development plan delivers a fully functional geographic polling system that ensures local participation while maintaining user privacy. By building on Phase 0's secure foundation, we can focus on implementing the core business features that make CivicPoll unique: mandatory geographic validation ensuring authentic local participation in democratic processes.

The emphasis on real-time analytics and immediate feedback creates an engaging experience for both citizens and administrators, while the robust testing and monitoring ensure production readiness from day one.
