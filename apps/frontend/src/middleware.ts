import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getDecodedToken } from "./lib/auth/cookies";
import { getAuthFromRequest, hasAdminAccess } from "./lib/auth/middleware";
import { AUTH_COOKIE_NAME, PUBLIC_ROUTES, ROUTES } from "./lib/constants";

// Define protected routes that require authentication
const protectedRoutes = [
    "/polls/*/participate", // Participating in polls
    "/polls/*/analytics", // Viewing poll analytics
    "/location/validation", // Validating location
    "/profile",
    "/dashboard",
];

// Define admin/moderator only routes
const adminRoutes = ["/admin", "/admin/*"];

// Define organization-only routes (deprecated - will use role-based access)
const organizationRoutes = [
    "/polls/create",
    "/polls/*/edit",
    "/polls/*/analytics",
    "/organization",
];

function isPublicRoute(pathname: string): boolean {
    // Check if it's the polls listing page (public)
    if (pathname === ROUTES.POLLS) return true;

    // Check if it's a poll results page (public for closed polls)
    if (pathname.match(/^\/polls\/[^\/]+\/results$/)) return true;

    return PUBLIC_ROUTES.some((route) => pathname === route || pathname.startsWith(route + "/"));
}

function isProtectedRoute(pathname: string): boolean {
    // Check pattern-based routes
    if (pathname.match(/^\/polls\/[^\/]+\/participate$/)) return true;
    if (pathname.match(/^\/polls\/[^\/]+\/analytics$/)) return true;

    return protectedRoutes.some((route) => pathname === route || pathname.startsWith(route + "/"));
}

function isAdminRoute(pathname: string): boolean {
    return pathname === "/admin" || pathname.startsWith("/admin/");
}

export async function middleware(request: NextRequest) {
    const { pathname } = request.nextUrl;

    // Allow public routes
    if (isPublicRoute(pathname)) {
        return NextResponse.next();
    }

    // Static assets and API routes should pass through
    if (
        pathname.startsWith("/_next") ||
        pathname.startsWith("/api/") ||
        pathname.includes(".") // Files with extensions
    ) {
        return NextResponse.next();
    }

    // Check authentication
    const auth = await getAuthFromRequest();

    if (!auth.isAuthenticated) {
        // Not authenticated - redirect to login
        const loginUrl = new URL(ROUTES.AUTH_LOGIN, request.url);
        loginUrl.searchParams.set("from", pathname);

        // Remove invalid cookie if it exists
        const response = NextResponse.redirect(loginUrl);
        if (request.cookies.has(AUTH_COOKIE_NAME)) {
            response.cookies.delete(AUTH_COOKIE_NAME);
        }
        return response;
    }

    // Check if token is expired
    if (auth.isExpired) {
        // Token is expired - redirect to login
        const loginUrl = new URL(ROUTES.AUTH_LOGIN, request.url);
        loginUrl.searchParams.set("from", pathname);

        // Remove expired cookie
        const response = NextResponse.redirect(loginUrl);
        response.cookies.delete(AUTH_COOKIE_NAME);
        return response;
    }

    // Check admin routes
    if (isAdminRoute(pathname)) {
        if (!hasAdminAccess(auth.user?.appRole)) {
            // Not an admin or moderator - redirect to home
            return NextResponse.redirect(new URL("/", request.url));
        }
    }

    // For protected routes, check if user has validated location
    if (pathname.match(/^\/polls\/[^\/]+\/participate$/)) {
        // This check will be done in the page component
        // as we need to fetch user location data
        return NextResponse.next();
    }

    // Check if token is about to expire (less than 1 hour remaining)
    const tokenExp = auth.user!.exp * 1000;
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    if (tokenExp - now < oneHour) {
        // Token is about to expire, add a header to trigger refresh
        const response = NextResponse.next();
        response.headers.set("X-Token-Refresh-Needed", "true");
        return response;
    }

    // For all other authenticated routes
    return NextResponse.next();
}

export const config = {
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         * - public files
         * - api routes (handled separately)
         */
        "/((?!_next/static|_next/image|favicon.ico|.*\\..*|api/).*)",
    ],
};
