import { jwtDecode } from "jwt-decode";
import { cookies } from "next/headers";
import { AUTH_COOKIE_NAME } from "@/lib/constants";

interface JWTPayload {
    id: string;
    username: string;
    email: string;
    appRole?: "user" | "moderator" | "admin";
    iat: number;
    exp: number;
}

export async function getAuthFromRequest(): Promise<{
    isAuthenticated: boolean;
    user?: JWTPayload;
    isExpired: boolean;
}> {
    try {
        const cookieStore = await cookies();
        const token = cookieStore.get(AUTH_COOKIE_NAME);

        if (!token?.value) {
            return { isAuthenticated: false, isExpired: false };
        }

        const decoded = jwtDecode<JWTPayload>(token.value);
        const now = Date.now() / 1000;

        return {
            isAuthenticated: true,
            user: decoded,
            isExpired: decoded.exp < now,
        };
    } catch (error) {
        return { isAuthenticated: false, isExpired: false };
    }
}

export function hasAdminAccess(appRole?: string): boolean {
    return appRole === "admin" || appRole === "moderator";
}

export function hasModeratorAccess(appRole?: string): boolean {
    return appRole === "admin" || appRole === "moderator";
}

export function hasAdminOnlyAccess(appRole?: string): boolean {
    return appRole === "admin";
}