import { cache } from "react";
import { cookies } from "next/headers";
import { getDecodedToken } from "./cookies";
import { API_URL, AUTH_COOKIE_NAME } from "@/lib/constants";
import type { User, UserLocation } from "@/types";

// Get current user from Strapi
export const getCurrentUser = cache(async (): Promise<User | null> => {
    const token = await getDecodedToken();
    if (!token) return null;

    const cookieStore = await cookies();
    const authToken = cookieStore.get(AUTH_COOKIE_NAME)?.value;

    try {
        const response = await fetch(`${API_URL}/users/me?populate=*`, {
            headers: {
                Authorization: `Bearer ${authToken}`,
                "Content-Type": "application/json",
            },
            cache: "no-store", // Always fresh for user data
        });

        if (!response.ok) {
            return null;
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Failed to fetch current user:", error);
        return null;
    }
});

// Validate user session
export async function validateSession(): Promise<boolean> {
    const user = await getCurrentUser();
    return user !== null;
}

// Get user location
export const getUserLocation = cache(async (): Promise<UserLocation | null> => {
    const cookieStore = await cookies();
    const authToken = cookieStore.get(AUTH_COOKIE_NAME)?.value;

    if (!authToken) return null;

    try {
        const response = await fetch(`${API_URL}/user-locations/mine?populate=geographicZone`, {
            headers: {
                Authorization: `Bearer ${authToken}`,
                "Content-Type": "application/json",
            },
            cache: "no-store",
        });

        if (!response.ok) {
            return null;
        }

        const data = await response.json();
        return data.data;
    } catch (error) {
        console.error("Failed to fetch user location:", error);
        return null;
    }
});

// Check if user can participate in polls
export async function canUserParticipate(): Promise<boolean> {
    const location = await getUserLocation();
    return location?.validated === true;
}
