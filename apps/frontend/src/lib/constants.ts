/**
 * Application-wide constants
 */

// API Configuration
export const API_BASE_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || "http://localhost:1337";
export const API_URL = `${API_BASE_URL}/api`;

// Authentication
export const AUTH_COOKIE_NAME = "civicpoll-auth-token";
export const AUTH_COOKIE_MAX_AGE = 60 * 60 * 24 * 7; // 7 days

// SMATFLOW OAuth Configuration
export const SMATFLOW_AUTH_URL =
    process.env.SMATFLOW_AUTH_URL || "https://auth.smatflow.com/oauth/authorize";
export const SMATFLOW_CLIENT_ID = process.env.SMATFLOW_CLIENT_ID;
export const SMATFLOW_REDIRECT_URI = `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/auth/callback`;

// Cache Configuration
export const CACHE_REVALIDATE_TIMES = {
    POLLS: 60, // 1 minute
    LOCATIONS: 3600, // 1 hour
    ORGANIZATIONS: 3600, // 1 hour
    ANALYTICS: 60, // 1 minute
    USER: 0, // No cache (always fresh)
} as const;

// Route Definitions
export const ROUTES = {
    HOME: "/",
    POLLS: "/polls",
    POLL_PARTICIPATE: (documentId: string) => `/polls/${documentId}/participate`,
    POLL_ANALYTICS: (documentId: string) => `/polls/${documentId}/analytics`,
    POLL_RESULTS: (documentId: string) => `/polls/${documentId}/results`,
    AUTH_LOGIN: "/auth/login",
    AUTH_REGISTER: "/auth/register",
    AUTH_CALLBACK: "/auth/callback",
    AUTH_ERROR: "/auth/error",
    LOCATION_VALIDATION: "/location/validation",
    PROFILE: "/profile",
    DASHBOARD: "/dashboard",
    ABOUT: "/about",
    CONTACT: "/contact",
    PRIVACY: "/privacy",
    TERMS: "/terms",
    HELP: "/help",
} as const;

// Public Routes (no auth required)
export const PUBLIC_ROUTES = [
    ROUTES.HOME,
    ROUTES.AUTH_LOGIN,
    ROUTES.AUTH_REGISTER,
    ROUTES.AUTH_CALLBACK,
    ROUTES.AUTH_ERROR,
    "/about",
    "/contact",
    "/privacy",
    "/terms",
    "/help",
] as const;

// Application Info
export const APP_NAME = "CivicPoll";
export const APP_DESCRIPTION = "Plateforme de sondages géolocalisés pour la France";
export const APP_PUBLISHER = "SMATFLOW";

// Pagination
export const DEFAULT_PAGE_SIZE = 20;
export const MAX_PAGE_SIZE = 100;

// Form Validation
export const VALIDATION_RULES = {
    PASSWORD_MIN_LENGTH: 6,
    USERNAME_MIN_LENGTH: 3,
    POLL_TITLE_MAX_LENGTH: 200,
    POLL_DESCRIPTION_MAX_LENGTH: 1000,
    QUESTION_TEXT_MAX_LENGTH: 500,
    TEXT_RESPONSE_MAX_LENGTH: 500,
} as const;

// Question Types
export const QUESTION_TYPES = {
    SINGLE_CHOICE: "single_choice",
    MULTIPLE_CHOICE: "multiple_choice",
    RATING: "rating",
    TEXT: "text",
    RANKING: "ranking",
} as const;

// Poll Categories
export const POLL_CATEGORIES = {
    SATISFACTION: "satisfaction",
    OPINION: "opinion",
    VOTING: "voting",
    CONSULTATION: "consultation",
} as const;

// Geographic Zone Types
export const ZONE_TYPES = {
    COUNTRY: "country",
    REGION: "region",
    DEPARTMENT: "department",
    CITY: "city",
} as const;

// Organization Types
export const ORGANIZATION_TYPES = {
    MUNICIPALITY: "municipality",
    DEPARTMENT: "department",
    REGION: "region",
    ASSOCIATION: "association",
    PUBLIC_SERVICE: "public_service",
} as const;
