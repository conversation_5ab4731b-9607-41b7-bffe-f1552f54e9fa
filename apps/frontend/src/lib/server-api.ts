import { cache } from "react";
import { cookies } from "next/headers";
import { API_URL, AUTH_COOKIE_NAME, CACHE_REVALIDATE_TIMES } from "@/lib/constants";
import type {
    Poll,
    UserLocation,
    GeographicZone,
    User,
    Organization,
    PollAnalytics,
    ApiResponse,
    Participation,
    UserStats,
} from "@/types";

async function serverFetch<T>(
    endpoint: string,
    options: RequestInit = {},
): Promise<ApiResponse<T>> {
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME)?.value;

    const headers: Record<string, string> = {
        "Content-Type": "application/json",
        ...((options.headers as Record<string, string>) || {}),
    };

    // Add authentication header if token exists
    if (token) {
        headers["Authorization"] = `Bearer ${token}`;
    }

    const response = await fetch(`${API_URL}${endpoint}`, {
        ...options,
        headers,
        // Enable caching for GET requests by default
        // next: {
        //     revalidate: options.method === "GET" ? 3600 : 0, // Cache for 1 hour
        //     tags: [endpoint.split("/")[1] || "api"], // Tag based on resource type
        // },
        cache: "no-store",
        next: { revalidate: 0 },
    });

    if (!response.ok) {
        // Handle authentication errors
        if (response.status === 401) {
            throw new Error("Non autorisé : Veuillez vous reconnecter");
        }

        throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
}

// Type-safe server-side mutations (for use in server actions)
export async function serverMutation<T>(
    endpoint: string,
    data: any,
    method: "POST" | "PUT" | "PATCH" | "DELETE" = "POST",
): Promise<ApiResponse<T>> {
    return serverFetch<T>(endpoint, {
        method,
        body: JSON.stringify({ data }),
    });
}

// Cached data fetching functions
// Using React's cache() function to deduplicate requests within a single render

export const fetchPolls = cache(async (zoneId?: number): Promise<Poll[]> => {
    // Use simple populate=* which will be handled by our custom controller
    // const params = zoneId
    //     ? `?filters[geographicZone][id][$eq]=${zoneId}&populate=*`
    //     : "?populate=*";

    const params = "?populate=*";

    const response = await serverFetch<Poll[]>(`/polls${params}`);

    return response.data;
});

export const fetchPollById = cache(async (documentId: string): Promise<Poll> => {
    const response = await serverFetch<Poll>(`/polls/${documentId}?populate=*`, {
        next: { revalidate: CACHE_REVALIDATE_TIMES.POLLS, tags: [`poll-${documentId}`] },
    });
    return response.data;
});

export const fetchUserLocation = cache(async (): Promise<UserLocation | null> => {
    try {
        const response = await serverFetch<UserLocation>(
            "/user-locations/mine?populate=geographicZone",
            {
                cache: "no-store", // Always fresh for user data
            },
        );
        return response.data;
    } catch {
        return null;
    }
});

export const fetchUserParticipations = cache(
    async (userDocumentId: string): Promise<Participation[]> => {
        const response = await serverFetch<Participation[]>(
            `/users/${userDocumentId}/participations`,
            {
                cache: "no-store", // Always fresh for user data
            },
        );

        return response.data;
    },
);

export const fetchUserStats = cache(async (userDocumentId: string): Promise<UserStats> => {
    const response = await serverFetch<UserStats>(`/users/${userDocumentId}/stats`, {
        cache: "no-store", // Always fresh for user data
    });

    return response.data;
});

export const fetchGeographicZones = cache(async (type?: string): Promise<GeographicZone[]> => {
    const params = type ? `?filters[type][$eq]=${type}` : "";
    const response = await serverFetch<GeographicZone[]>(`/geographic-zones${params}&populate=*`, {
        next: { revalidate: CACHE_REVALIDATE_TIMES.LOCATIONS },
    });

    return response.data;
});

export const fetchOrganizations = cache(async (): Promise<Organization[]> => {
    const response = await serverFetch<Organization[]>("/organizations?populate=*", {
        next: { revalidate: CACHE_REVALIDATE_TIMES.ORGANIZATIONS },
    });

    return response.data;
});

export const fetchPollAnalytics = cache(async (pollDocumentId: string): Promise<PollAnalytics> => {
    const response = await serverFetch<PollAnalytics>(`/polls/${pollDocumentId}/analytics`, {
        next: { revalidate: CACHE_REVALIDATE_TIMES.ANALYTICS },
    });

    return response.data;
});

// Validation functions
export async function validatePollParticipation(
    pollDocumentId: string,
): Promise<{ canParticipate: boolean; reason?: string }> {
    try {
        const response = await serverFetch<{ canParticipate: boolean; reason?: string }>(
            `/polls/${pollDocumentId}/validate-participation`,
            { method: "POST", cache: "no-store" },
        );

        return response.data;
    } catch (error) {
        return {
            canParticipate: false,
            reason: error instanceof Error ? error.message : "Erreur inconnue",
        };
    }
}

// Error handling utility for server components
export function handleServerError(error: unknown): never {
    console.error("Server API Error:", error);

    if (error instanceof Error) {
        if (error.message.includes("Non autorisé")) {
            throw new Error("Authentification requise. Veuillez vous connecter.");
        }
        throw new Error(`Erreur Serveur: ${error.message}`);
    }

    throw new Error("Une erreur serveur inattendue s'est produite");
}
