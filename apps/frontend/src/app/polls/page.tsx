import { fetchPolls, fetchUserLocation } from "@/lib/server-api";
import Link from "next/link";

export default async function PollsPage() {
    const userLocation = await fetchUserLocation();
    const polls = await fetchPolls(userLocation?.geographicZone?.id);

    const activePollsFiltered = polls.filter((poll) => poll.pollStatus === "active");
    const upcomingPollsFiltered = polls.filter((poll) => poll.pollStatus === "scheduled");
    const closedPollsFiltered = polls.filter((poll) => poll.pollStatus === "closed");

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-8">Sondages disponibles</h1>

                {userLocation?.validated ? (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                        <p className="text-green-800">
                            Sondages pour la zone :{" "}
                            <strong>{userLocation.geographicZone?.name}</strong>
                        </p>
                    </div>
                ) : (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                        <p className="text-yellow-800">
                            Vous devez valider votre adresse pour voir les sondages de votre zone.
                        </p>
                        <Link
                            href="/location/validation"
                            className="inline-block mt-2 text-yellow-700 hover:underline"
                        >
                            Valider mon adresse →
                        </Link>
                    </div>
                )}

                {/* Active Polls */}
                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Sondages actifs</h2>
                    {activePollsFiltered.length > 0 ? (
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                            {activePollsFiltered.map((poll) => (
                                <div
                                    key={poll.documentId}
                                    className="bg-white rounded-lg shadow p-6"
                                >
                                    <h3 className="text-xl font-semibold mb-2">{poll.title}</h3>
                                    <p className="text-gray-600 mb-4 line-clamp-3">
                                        {poll.description}
                                    </p>
                                    <div className="text-sm text-gray-500 mb-4">
                                        <p>Zone : {poll.geographicZone.name}</p>
                                        <p>
                                            Fin :{" "}
                                            {new Date(poll.endDate).toLocaleDateString("fr-FR")}
                                        </p>
                                    </div>
                                    <Link
                                        href={`/polls/${poll.documentId}/participate`}
                                        className="block w-full text-center bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors"
                                    >
                                        Participer
                                    </Link>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <p className="text-gray-500">Aucun sondage actif pour le moment.</p>
                    )}
                </section>

                {/* Upcoming Polls */}
                <section className="mb-8">
                    <h2 className="text-2xl font-semibold mb-4">Sondages à venir</h2>
                    {upcomingPollsFiltered.length > 0 ? (
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                            {upcomingPollsFiltered.map((poll) => (
                                <div
                                    key={poll.documentId}
                                    className="bg-white rounded-lg shadow p-6 opacity-75"
                                >
                                    <h3 className="text-xl font-semibold mb-2">{poll.title}</h3>
                                    <p className="text-gray-600 mb-4 line-clamp-3">
                                        {poll.description}
                                    </p>
                                    <div className="text-sm text-gray-500">
                                        <p>Zone : {poll.geographicZone.name}</p>
                                        <p>
                                            Début :{" "}
                                            {new Date(poll.startDate).toLocaleDateString("fr-FR")}
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <p className="text-gray-500">Aucun sondage programmé.</p>
                    )}
                </section>

                {/* Closed Polls */}
                <section>
                    <h2 className="text-2xl font-semibold mb-4">Sondages terminés</h2>
                    {closedPollsFiltered.length > 0 ? (
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                            {closedPollsFiltered.map((poll) => (
                                <div
                                    key={poll.documentId}
                                    className="bg-white rounded-lg shadow p-6"
                                >
                                    <h3 className="text-xl font-semibold mb-2">{poll.title}</h3>
                                    <p className="text-gray-600 mb-4 line-clamp-3">
                                        {poll.description}
                                    </p>
                                    <div className="text-sm text-gray-500 mb-4">
                                        <p>Zone : {poll.geographicZone.name}</p>
                                        <p>
                                            Terminé le :{" "}
                                            {new Date(poll.endDate).toLocaleDateString("fr-FR")}
                                        </p>
                                    </div>
                                    <Link
                                        href={`/polls/${poll.documentId}/results`}
                                        className="block w-full text-center bg-gray-600 text-white py-2 px-4 rounded hover:bg-gray-700 transition-colors"
                                    >
                                        Voir les résultats
                                    </Link>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <p className="text-gray-500">Aucun sondage terminé.</p>
                    )}
                </section>
            </div>
        </div>
    );
}
