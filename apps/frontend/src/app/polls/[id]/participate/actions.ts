"use server";

import { z } from "zod";
import { revalidatePath, revalidateTag } from "next/cache";
import { ActionResult } from "@/types/actions";
import { Participation } from "@/types";
import { serverMutation, validatePollParticipation } from "@/lib/server-api";

const participationSchema = z.object({
    pollDocumentId: z.string(),
    responses: z.record(z.any()),
});

export async function submitParticipationAction(
    formData: FormData,
): Promise<ActionResult<Participation>> {
    try {
        const pollDocumentId = formData.get("pollDocumentId") as string;
        const responsesJson = formData.get("responses") as string;
        const responses = JSON.parse(responsesJson);

        // Validate participation eligibility
        const validationResponse = await validatePollParticipation(pollDocumentId);

        if (!validationResponse.canParticipate) {
            return {
                success: false,
                error: validationResponse.reason || "Vous n'êtes pas éligible pour ce sondage",
            };
        }

        // Submit participation (serverMutation already wraps in { data })
        const response = await serverMutation<Participation>(
            "/participations",
            {
                poll: pollDocumentId,
                responses,
                completed: true,
                startedAt: new Date().toISOString(),
                completedAt: new Date().toISOString(),
            },
            "POST",
        );

        // Revalidate caches
        revalidateTag("participations");
        revalidateTag(`poll-${pollDocumentId}`);
        revalidatePath(`/polls/${pollDocumentId}/results`);

        return {
            success: true,
            data: response.data,
        };
    } catch (error) {
        console.error("Participation submission error:", error);
        return {
            success: false,
            error: "Erreur lors de la soumission de votre participation",
        };
    }
}

// Wrapper for useActionState compatibility
export async function submitParticipationStateAction(
    prevState: ActionResult<Participation>,
    formData: FormData,
): Promise<ActionResult<Participation>> {
    return submitParticipationAction(formData);
}
