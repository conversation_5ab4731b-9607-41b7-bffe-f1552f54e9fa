import { notFound, redirect } from "next/navigation";
import { PollParticipation } from "@/components/Poll/PollParticipation";
import { fetchPollById, fetchUserLocation, validatePollParticipation } from "@/lib/server-api";
import { ROUTES } from "@/lib/constants";

interface PageProps {
    params: Promise<{ id: string }>;
}

// Helper function to provide user-friendly error messages
function getDetailedReason(reason?: string): string {
    if (!reason) {
        return "Vous n'êtes pas éligible pour participer à ce sondage. Veuillez vérifier que votre adresse est validée et que vous êtes dans la zone géographique concernée.";
    }

    const reasonMap: Record<string, string> = {
        "User location not validated":
            "Votre adresse n'a pas encore été validée. Vous devez valider votre adresse de résidence pour pouvoir participer aux sondages de votre zone.",
        "User is not in the poll geographic zone":
            "Ce sondage est réservé aux résidents de la zone géographique concernée. Votre adresse validée ne correspond pas à cette zone.",
        "User already participated in this poll":
            "Vous avez déjà participé à ce sondage. Chaque personne ne peut participer qu'une seule fois.",
        "Poll is not active":
            "Ce sondage n'est pas actuellement actif. Il peut être terminé ou pas encore commencé.",
        "Poll not found": "Ce sondage n'existe pas ou a été supprimé.",
        "Authentication required": "Vous devez être connecté pour participer à ce sondage.",
    };

    return reasonMap[reason] || reason;
}

export default async function PollParticipatePage({ params }: PageProps) {
    const { id } = await params;

    // Fetch poll data
    const poll = await fetchPollById(id);
    if (!poll) {
        notFound();
    }

    // Check if user has validated location
    const userLocation = await fetchUserLocation();
    if (!userLocation?.validated) {
        redirect(ROUTES.LOCATION_VALIDATION);
    }

    // Validate participation eligibility
    const validation = await validatePollParticipation(id);

    if (!validation.canParticipate) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
                <div className="max-w-md w-full">
                    <div className="text-center mb-8">
                        {/* Simple icon */}
                        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg
                                className="w-8 h-8 text-red-600"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                                />
                            </svg>
                        </div>

                        <h1 className="text-2xl font-semibold text-gray-900 mb-2">
                            Participation non autorisée
                        </h1>

                        <p className="text-gray-600 mb-8">{getDetailedReason(validation.reason)}</p>
                    </div>

                    {/* Poll info card */}
                    <div className="bg-white rounded-xl p-4 mb-8 border border-gray-200">
                        <div className="flex items-start gap-3">
                            <div className="w-10 h-10 bg-blue-50 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg
                                    className="w-5 h-5 text-blue-600"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                    />
                                </svg>
                            </div>
                            <div className="flex-1">
                                <h3 className="font-medium text-gray-900">{poll.title}</h3>
                                <p className="text-sm text-gray-500 mt-1">
                                    <span className="inline-flex items-center gap-1">
                                        <svg
                                            className="w-3.5 h-3.5"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                                            />
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                                            />
                                        </svg>
                                        {poll.geographicZone.name}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Simple action buttons */}
                    <div className="space-y-3">
                        <a
                            href={ROUTES.LOCATION_VALIDATION}
                            className="block w-full text-center bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                        >
                            Vérifier mon adresse
                        </a>
                        <a
                            href={ROUTES.POLLS}
                            className="block w-full text-center text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-100 transition-colors"
                        >
                            Voir d'autres sondages
                        </a>
                    </div>

                    {/* Simple help link */}
                    <p className="text-center text-sm text-gray-500 mt-8">
                        Besoin d'aide ?{" "}
                        <a href={ROUTES.HELP} className="text-blue-600 hover:underline">
                            Consulter la FAQ
                        </a>
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <PollParticipation poll={poll} />
        </div>
    );
}
