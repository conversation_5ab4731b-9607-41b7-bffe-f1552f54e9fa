import { notFound } from "next/navigation";
import { PollAnalytics } from "@/components/Analytics/PollAnalytics";
import { fetchPollAnalytics } from "@/lib/server-api";

interface PageProps {
    params: Promise<{ id: string }>;
}

export default async function PollAnalyticsPage({ params }: PageProps) {
    const { id } = await params;

    try {
        const analytics = await fetchPollAnalytics(id);

        return (
            <div className="min-h-screen bg-gray-50">
                <PollAnalytics analytics={analytics} />
            </div>
        );
    } catch (error) {
        console.error("Failed to fetch poll analytics:", error);
        notFound();
    }
}

export const revalidate = 60; // Revalidate every minute
