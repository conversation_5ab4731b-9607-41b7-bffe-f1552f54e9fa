import { notFound } from "next/navigation";
import Link from "next/link";
import { fetchPollById } from "@/lib/server-api";
import { ROUTES, API_URL } from "@/lib/constants";
import { PollResultsChart } from "@/components/Poll/PollResultsChart";

interface PageProps {
    params: Promise<{ id: string }>;
}

interface PollResults {
    poll: {
        id: string;
        title: string;
        description: string;
        status: string;
        endDate: string;
    };
    totalParticipants: number;
    questions: Array<{
        id: string;
        text: string;
        type: string;
        results: {
            options?: Array<{
                id: string;
                text: string;
                count: number;
                percentage: number;
            }>;
            textResponses?: string[];
            ratings?: {
                average: number;
                distribution: Record<string, number>;
            };
            rankings?: Array<{
                optionId: string;
                optionText: string;
                averageRank: number;
            }>;
        };
    }>;
}

async function fetchPollResults(pollId: string): Promise<PollResults | null> {
    try {
        const response = await fetch(`${API_URL}/polls/${pollId}/results`, {
            next: { revalidate: 60 }, // Cache for 1 minute
        });

        if (!response.ok) {
            return null;
        }

        const data = await response.json();
        return data.data;
    } catch (error) {
        console.error("Failed to fetch poll results:", error);
        return null;
    }
}

export default async function PollResultsPage({ params }: PageProps) {
    const { id } = await params;

    // Fetch poll data to check if it exists
    const poll = await fetchPollById(id);
    if (!poll) {
        notFound();
    }

    // Fetch poll results
    const results = await fetchPollResults(id);

    // Check if poll is closed or user has participated
    if (!results) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
                    <h2 className="text-xl font-bold text-gray-900 mb-4">
                        Résultats non disponibles
                    </h2>
                    <p className="text-gray-700 mb-4">
                        Les résultats de ce sondage ne sont pas encore disponibles.
                        {poll.pollStatus === "active" && " Le sondage est toujours en cours."}
                    </p>
                    <Link
                        href={ROUTES.POLLS}
                        className="inline-block text-blue-600 hover:underline"
                    >
                        Retour aux sondages
                    </Link>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <div className="flex items-center justify-between mb-4">
                        <Link
                            href={ROUTES.POLLS}
                            className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-900"
                        >
                            <svg
                                className="w-5 h-5"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M15 19l-7-7 7-7"
                                />
                            </svg>
                            Retour aux sondages
                        </Link>
                        {poll.pollStatus === "closed" && (
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                Sondage terminé
                            </span>
                        )}
                    </div>

                    <h1 className="text-3xl font-bold text-gray-900 mb-2">{results.poll.title}</h1>
                    <p className="text-gray-600 mb-4">{results.poll.description}</p>

                    <div className="flex items-center gap-6 text-sm text-gray-500">
                        <div className="flex items-center gap-2">
                            <svg
                                className="w-5 h-5"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                                />
                            </svg>
                            <span>{results.totalParticipants} participants</span>
                        </div>
                        {poll.pollStatus === "closed" && (
                            <div className="flex items-center gap-2">
                                <svg
                                    className="w-5 h-5"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                                    />
                                </svg>
                                <span>
                                    Terminé le{" "}
                                    {new Date(results.poll.endDate).toLocaleDateString("fr-FR")}
                                </span>
                            </div>
                        )}
                    </div>
                </div>

                {/* Results */}
                <div className="space-y-6">
                    {results.questions.map((question, index) => (
                        <div key={question.id} className="bg-white rounded-lg shadow-sm p-6">
                            <h2 className="text-xl font-semibold text-gray-900 mb-4">
                                Question {index + 1}: {question.text}
                            </h2>

                            <PollResultsChart question={question} />
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
}
