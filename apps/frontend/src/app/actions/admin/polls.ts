"use server";

import { revalidatePath, revalidateTag } from "next/cache";
import { redirect } from "next/navigation";
import { z } from "zod";
import { getCurrentUser } from "@/lib/auth/server";
import { ActionResult } from "@/types/actions";
import { serverMutation } from "@/lib/server-api";
import { Poll } from "@/types";

// Validation schemas
const createPollSchema = z.object({
    title: z.string().min(1, "Le titre est requis"),
    description: z.string().min(1, "La description est requise"),
    category: z.enum(["satisfaction", "opinion", "voting", "consultation"]),
    geographicZoneId: z.string().min(1, "La zone géographique est requise"),
    startDate: z.string(),
    endDate: z.string(),
    isAnonymous: z.boolean(),
    requiresValidatedLocation: z.boolean(),
    questions: z.array(
        z.object({
            text: z.string().min(1, "Le texte de la question est requis"),
            type: z.enum(["single_choice", "multiple_choice", "rating", "text", "ranking"]),
            required: z.boolean(),
            order: z.number(),
            options: z.array(
                z.object({
                    text: z.string().min(1, "Le texte de l'option est requis"),
                    order: z.number(),
                })
            ).optional(),
            ratingScale: z.object({
                min: z.number(),
                max: z.number(),
            }).optional(),
        })
    ).min(1, "Au moins une question est requise"),
});

export async function createPollAction(
    data: z.infer<typeof createPollSchema>
): Promise<ActionResult<Poll>> {
    try {
        const user = await getCurrentUser();
        if (!user) {
            return {
                success: false,
                error: "Vous devez être connecté pour créer un sondage",
            };
        }

        // Validate the data
        const validationResult = createPollSchema.safeParse(data);
        if (!validationResult.success) {
            return {
                success: false,
                error: "Données invalides",
                fieldErrors: validationResult.error.flatten().fieldErrors,
            };
        }

        // Transform questions to match API format
        const questionsData = validationResult.data.questions.map((q, index) => ({
            text: q.text,
            type: q.type,
            required: q.required,
            order: index + 1,
            options: q.options?.map((opt, optIndex) => ({
                text: opt.text,
                order: optIndex + 1,
            })),
            ratingScale: q.ratingScale,
        }));

        // Create the poll
        const response = await serverMutation<Poll>("/polls", {
            data: {
                title: validationResult.data.title,
                description: validationResult.data.description,
                category: validationResult.data.category,
                geographicZone: validationResult.data.geographicZoneId,
                startDate: validationResult.data.startDate,
                endDate: validationResult.data.endDate,
                isAnonymous: validationResult.data.isAnonymous,
                requiresValidatedLocation: validationResult.data.requiresValidatedLocation,
                pollStatus: "draft",
                questions: questionsData,
            },
        }, "POST");

        // Revalidate caches
        revalidateTag("polls");
        revalidatePath("/admin/polls");

        return {
            success: true,
            data: response.data,
        };
    } catch (error) {
        console.error("Error creating poll:", error);
        return {
            success: false,
            error: "Erreur lors de la création du sondage",
        };
    }
}

export async function updatePollAction(
    pollId: string,
    data: Partial<z.infer<typeof createPollSchema>>
): Promise<ActionResult<Poll>> {
    try {
        const user = await getCurrentUser();
        if (!user) {
            return {
                success: false,
                error: "Vous devez être connecté pour modifier un sondage",
            };
        }

        // Update the poll
        const response = await serverMutation<Poll>(`/polls/${pollId}`, {
            data: {
                ...data,
                geographicZone: data.geographicZoneId,
            },
        }, "PUT");

        // Revalidate caches
        revalidateTag("polls");
        revalidateTag(`poll-${pollId}`);
        revalidatePath("/admin/polls");
        revalidatePath(`/admin/polls/${pollId}`);

        return {
            success: true,
            data: response.data,
        };
    } catch (error) {
        console.error("Error updating poll:", error);
        return {
            success: false,
            error: "Erreur lors de la modification du sondage",
        };
    }
}

export async function deletePollAction(pollId: string): Promise<ActionResult<void>> {
    try {
        const user = await getCurrentUser();
        if (!user) {
            return {
                success: false,
                error: "Vous devez être connecté pour supprimer un sondage",
            };
        }

        // Delete the poll
        await serverMutation(`/polls/${pollId}`, {}, "DELETE");

        // Revalidate caches
        revalidateTag("polls");
        revalidatePath("/admin/polls");

        return {
            success: true,
        };
    } catch (error) {
        console.error("Error deleting poll:", error);
        return {
            success: false,
            error: "Erreur lors de la suppression du sondage",
        };
    }
}

export async function publishPollAction(pollId: string): Promise<ActionResult<Poll>> {
    try {
        const user = await getCurrentUser();
        if (!user) {
            return {
                success: false,
                error: "Vous devez être connecté pour publier un sondage",
            };
        }

        // Publish the poll
        const response = await serverMutation<Poll>(`/polls/${pollId}`, {
            data: {
                pollStatus: "active",
                publishedAt: new Date().toISOString(),
            },
        }, "PUT");

        // Revalidate caches
        revalidateTag("polls");
        revalidateTag(`poll-${pollId}`);
        revalidatePath("/admin/polls");
        revalidatePath("/polls");

        return {
            success: true,
            data: response.data,
        };
    } catch (error) {
        console.error("Error publishing poll:", error);
        return {
            success: false,
            error: "Erreur lors de la publication du sondage",
        };
    }
}

export async function closePollAction(pollId: string): Promise<ActionResult<Poll>> {
    try {
        const user = await getCurrentUser();
        if (!user) {
            return {
                success: false,
                error: "Vous devez être connecté pour fermer un sondage",
            };
        }

        // Close the poll
        const response = await serverMutation<Poll>(`/polls/${pollId}`, {
            data: {
                pollStatus: "closed",
            },
        }, "PUT");

        // Revalidate caches
        revalidateTag("polls");
        revalidateTag(`poll-${pollId}`);
        revalidatePath("/admin/polls");
        revalidatePath("/polls");

        return {
            success: true,
            data: response.data,
        };
    } catch (error) {
        console.error("Error closing poll:", error);
        return {
            success: false,
            error: "Erreur lors de la fermeture du sondage",
        };
    }
}