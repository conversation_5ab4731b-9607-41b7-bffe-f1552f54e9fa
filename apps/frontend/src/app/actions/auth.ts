"use server";

import { redirect } from "next/navigation";
import { z } from "zod";
import { setAuth<PERSON><PERSON><PERSON>, removeAuth<PERSON><PERSON><PERSON> } from "@/lib/auth/cookies";
import { revalidatePath } from "next/cache";
import {
    API_URL,
    ROUTES,
    SMATFLOW_AUTH_URL,
    SMATFLOW_CLIENT_ID,
    SMATFLOW_REDIRECT_URI,
    VALIDATION_RULES,
} from "@/lib/constants";

// Validation schemas
const loginSchema = z.object({
    identifier: z.string().min(1, "Email ou nom d'utilisateur requis"),
    password: z.string().min(1, "Mot de passe requis"),
});

// For CivicPoll, we'll handle registration through SMATFLOW SSO
// This is just for local development/testing
const registerSchema = z.object({
    username: z
        .string()
        .min(
            VALIDATION_RULES.USERNAME_MIN_LENGTH,
            `Le nom d'utilisateur doit contenir au moins ${VALIDATION_RULES.USERNAME_MIN_LENGTH} caractères`,
        ),
    email: z.string().email("Email invalide"),
    password: z
        .string()
        .min(
            VALIDATION_RULES.PASSWORD_MIN_LENGTH,
            `Le mot de passe doit contenir au moins ${VALIDATION_RULES.PASSWORD_MIN_LENGTH} caractères`,
        )
        .regex(/[a-z]/, "Le mot de passe doit contenir au moins une minuscule")
        .regex(/[0-9]/, "Le mot de passe doit contenir au moins un chiffre"),
});

export async function login(identifier: string, password: string) {
    // Validate input
    const validation = loginSchema.safeParse({ identifier, password });
    if (!validation.success) {
        throw new Error(validation.error.errors[0].message);
    }

    try {
        const response = await fetch(`${API_URL}/auth/local`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                identifier,
                password,
            }),
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error?.message || "Échec de la connexion");
        }

        // Set the JWT token in an HTTP-only cookie
        await setAuthCookie(data.jwt);

        // Revalidate all cached data
        revalidatePath("/", "layout");

        return {
            success: true,
            user: data.user,
        };
    } catch (error) {
        throw new Error(error instanceof Error ? error.message : "Échec de la connexion");
    }
}

export async function register(username: string, email: string, password: string) {
    // Validate input
    const validation = registerSchema.safeParse({ username, email, password });
    if (!validation.success) {
        throw new Error(validation.error.errors[0].message);
    }

    try {
        const response = await fetch(`${API_URL}/auth/local/register`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                username,
                email,
                password,
            }),
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error?.message || "Échec de l'inscription");
        }

        // Set the JWT token in an HTTP-only cookie
        await setAuthCookie(data.jwt);

        // Revalidate all cached data
        revalidatePath("/", "layout");

        return {
            success: true,
            user: data.user,
        };
    } catch (error) {
        throw new Error(error instanceof Error ? error.message : "Échec de l'inscription");
    }
}

export async function logout() {
    // Remove the auth cookie
    await removeAuthCookie();

    // Revalidate all cached data
    revalidatePath("/", "layout");

    // Redirect to home page
    redirect(ROUTES.HOME);
}

// SMATFLOW SSO login action
export async function loginWithSMATFLOW() {
    // In production, this would redirect to SMATFLOW OAuth2 endpoint
    if (!SMATFLOW_CLIENT_ID) {
        throw new Error("SMATFLOW configuration manquante");
    }

    const authUrl = new URL(SMATFLOW_AUTH_URL);
    authUrl.searchParams.append("client_id", SMATFLOW_CLIENT_ID);
    authUrl.searchParams.append("redirect_uri", SMATFLOW_REDIRECT_URI);
    authUrl.searchParams.append("response_type", "code");
    authUrl.searchParams.append("scope", "profile email address");

    redirect(authUrl.toString());
}
