"use client";

import { useState } from "react";
import Link from "next/link";
import { ROUTES, APP_NAME } from "@/lib/constants";

interface FAQItem {
    question: string;
    answer: string;
    category: string;
}

const faqItems: FAQItem[] = [
    {
        category: "Compte et connexion",
        question: "Comment créer un compte sur CivicPoll ?",
        answer: "Pour créer un compte, cliquez sur 'Connexion' puis utilisez votre compte SMATFLOW SSO. Si vous n'avez pas de compte SMATFLOW, vous pouvez en créer un gratuitement. Une fois connecté, vous devrez valider votre adresse de résidence.",
    },
    {
        category: "Compte et connexion",
        question: "J'ai oublié mon mot de passe, que faire ?",
        answer: "Si vous utilisez SMATFLOW SSO, vous devez réinitialiser votre mot de passe sur la plateforme SMATFLOW. Si vous avez un compte local, cliquez sur 'Mot de passe oublié' sur la page de connexion.",
    },
    {
        category: "Validation d'adresse",
        question: "Pourquoi dois-je valider mon adresse ?",
        answer: "La validation d'adresse garantit que seuls les résidents d'une zone géographique peuvent participer aux sondages qui les concernent. C'est essentiel pour la représentativité et la légitimité des résultats.",
    },
    {
        category: "Validation d'adresse",
        question: "Comment valider mon adresse ?",
        answer: "Après votre première connexion, vous serez automatiquement redirigé vers la page de validation. Entrez votre adresse complète et le système la vérifiera via l'API gouvernementale française. La validation est instantanée.",
    },
    {
        category: "Participation aux sondages",
        question: "Comment participer à un sondage ?",
        answer: "Une fois votre adresse validée, accédez à la liste des sondages depuis le menu. Vous verrez uniquement les sondages de votre zone géographique. Cliquez sur 'Participer' pour commencer.",
    },
    {
        category: "Participation aux sondages",
        question: "Puis-je modifier mes réponses après avoir participé ?",
        answer: "Non, une fois vos réponses soumises, elles ne peuvent pas être modifiées. Cela garantit l'intégrité des résultats. Prenez le temps de bien réfléchir avant de valider vos réponses.",
    },
    {
        category: "Participation aux sondages",
        question: "Mes réponses sont-elles anonymes ?",
        answer: "Oui, toutes vos réponses sont complètement anonymisées. Personne, pas même les administrateurs, ne peut lier vos réponses à votre identité.",
    },
    {
        category: "Résultats",
        question: "Quand puis-je voir les résultats d'un sondage ?",
        answer: "Les résultats sont disponibles dès la clôture du sondage. Si vous avez participé, vous pouvez également voir les résultats des sondages terminés.",
    },
    {
        category: "Problèmes techniques",
        question: "Le site ne fonctionne pas correctement, que faire ?",
        answer: "Essayez de rafraîchir la page (Ctrl+F5 ou Cmd+Shift+R). Si le problème persiste, videz le cache de votre navigateur. Vous pouvez aussi essayer avec un autre navigateur ou nous contacter via la page de contact.",
    },
    {
        category: "Confidentialité",
        question: "Comment mes données sont-elles protégées ?",
        answer: "Nous utilisons des protocoles de sécurité avancés pour protéger vos données. Toutes les connexions sont chiffrées, et nous respectons strictement le RGPD. Consultez notre politique de confidentialité pour plus de détails.",
    },
];

export default function HelpPage() {
    const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
    const [expandedItems, setExpandedItems] = useState<string[]>([]);

    const categories = Array.from(new Set(faqItems.map((item) => item.category)));

    const filteredItems = selectedCategory
        ? faqItems.filter((item) => item.category === selectedCategory)
        : faqItems;

    const toggleExpanded = (question: string) => {
        setExpandedItems((prev) =>
            prev.includes(question) ? prev.filter((q) => q !== question) : [...prev, question],
        );
    };

    return (
        <div className="min-h-screen bg-gray-50 py-12">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h1 className="text-4xl font-bold text-gray-900 mb-8">Centre d'aide</h1>

                {/* Quick Links */}
                <div className="bg-white rounded-lg shadow-sm p-8 mb-8">
                    <h2 className="text-2xl font-semibold text-gray-900 mb-6">Liens rapides</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Link
                            href={ROUTES.LOCATION_VALIDATION}
                            className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                            <svg
                                className="w-6 h-6 text-blue-600"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                                />
                            </svg>
                            <span className="font-medium">Valider mon adresse</span>
                        </Link>

                        <Link
                            href="/contact"
                            className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                            <svg
                                className="w-6 h-6 text-blue-600"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                />
                            </svg>
                            <span className="font-medium">Nous contacter</span>
                        </Link>

                        <Link
                            href="/privacy"
                            className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                            <svg
                                className="w-6 h-6 text-blue-600"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                                />
                            </svg>
                            <span className="font-medium">Confidentialité</span>
                        </Link>
                    </div>
                </div>

                {/* FAQ Section */}
                <div className="bg-white rounded-lg shadow-sm p-8">
                    <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                        Questions fréquentes
                    </h2>

                    {/* Category Filter */}
                    <div className="flex flex-wrap gap-2 mb-6">
                        <button
                            onClick={() => setSelectedCategory(null)}
                            className={`px-4 py-2 rounded-lg transition-colors ${
                                selectedCategory === null
                                    ? "bg-blue-600 text-white"
                                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                            }`}
                        >
                            Toutes les catégories
                        </button>
                        {categories.map((category) => (
                            <button
                                key={category}
                                onClick={() => setSelectedCategory(category)}
                                className={`px-4 py-2 rounded-lg transition-colors ${
                                    selectedCategory === category
                                        ? "bg-blue-600 text-white"
                                        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                                }`}
                            >
                                {category}
                            </button>
                        ))}
                    </div>

                    {/* FAQ Items */}
                    <div className="space-y-4">
                        {filteredItems.map((item, index) => (
                            <div key={index} className="border border-gray-200 rounded-lg">
                                <button
                                    onClick={() => toggleExpanded(item.question)}
                                    className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                                >
                                    <div>
                                        <span className="text-xs font-medium text-blue-600 uppercase tracking-wider">
                                            {item.category}
                                        </span>
                                        <h3 className="font-semibold text-gray-900 mt-1">
                                            {item.question}
                                        </h3>
                                    </div>
                                    <svg
                                        className={`w-5 h-5 text-gray-400 transition-transform ${
                                            expandedItems.includes(item.question)
                                                ? "rotate-180"
                                                : ""
                                        }`}
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M19 9l-7 7-7-7"
                                        />
                                    </svg>
                                </button>
                                {expandedItems.includes(item.question) && (
                                    <div className="px-6 pb-4">
                                        <p className="text-gray-600">{item.answer}</p>
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                </div>

                {/* Still need help? */}
                <div className="mt-8 bg-blue-50 rounded-lg p-8 text-center">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        Vous ne trouvez pas la réponse à votre question ?
                    </h3>
                    <p className="text-gray-600 mb-4">Notre équipe est là pour vous aider</p>
                    <Link
                        href="/contact"
                        className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-colors"
                    >
                        Contactez-nous
                        <svg
                            className="w-5 h-5"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 7l5 5m0 0l-5 5m5-5H6"
                            />
                        </svg>
                    </Link>
                </div>
            </div>
        </div>
    );
}
