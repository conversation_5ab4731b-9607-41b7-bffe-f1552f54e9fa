import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";
import { Header } from "@/components/navigation/Header";
import { Footer } from "@/components/navigation/Footer";
import "./globals.css";

const inter = Inter({
    subsets: ["latin"],
    variable: "--font-inter",
    display: "swap",
});

export const metadata: Metadata = {
    title: "CivicPoll - Plateforme de Sondages SMATFLOW",
    description:
        "CivicPoll est la plateforme de sondages géolocalisés de SMATFLOW pour la France. Participez aux sondages de votre région et exprimez votre opinion.",
    keywords: ["sondages", "opinion", "france", "géolocalisation", "participation", "citoyenne"],
    authors: [{ name: "SMATFLOW" }],
    creator: "SMATFLOW",
    publisher: "SMATFLOW",
    robots: "index, follow",
    openGraph: {
        title: "CivicPoll - Plateforme de Sondages SMATFLOW",
        description: "Participez aux sondages géolocalisés de votre région avec CivicPoll",
        url: "https://civicpoll.fr.smatflow.xyz",
        siteName: "CivicPoll",
        locale: "fr_FR",
        type: "website",
    },
    twitter: {
        card: "summary_large_image",
        title: "CivicPoll - Plateforme de Sondages SMATFLOW",
        description: "Participez aux sondages géolocalisés de votre région",
    },
};

export const viewport: Viewport = {
    width: "device-width",
    initialScale: 1,
    themeColor: "#ffffff",
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="fr" className={`${inter.variable} light`} style={{ colorScheme: "light" }}>
            <head>
                <link rel="icon" href="/favicon.ico" />
                <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
                <meta name="msapplication-TileColor" content="#2563eb" />
                <meta name="theme-color" content="#ffffff" />
            </head>
            <body className="antialiased bg-background text-foreground">
                <div className="min-h-screen flex flex-col">
                    <Header />

                    <main className="flex-1">{children}</main>

                    <Footer />
                </div>
            </body>
        </html>
    );
}
