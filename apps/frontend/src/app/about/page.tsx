import Link from "next/link";
import { ROUTES, APP_NAME, APP_PUBLISHER } from "@/lib/constants";

export default function AboutPage() {
    return (
        <div className="min-h-screen bg-gray-50">
            {/* Hero Section */}
            <section className="bg-white border-b border-gray-200">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
                    <h1 className="text-4xl font-bold text-gray-900 mb-4">
                        À propos de {APP_NAME}
                    </h1>
                    <p className="text-xl text-gray-600">
                        La plateforme de sondages géolocalisés qui donne une voix authentique aux
                        citoyens français
                    </p>
                </div>
            </section>

            {/* Mission Section */}
            <section className="py-16">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="max-w-3xl mx-auto">
                        <h2 className="text-3xl font-bold text-gray-900 mb-8">Notre Mission</h2>
                        <div className="prose prose-lg text-gray-600">
                            <p>
                                {APP_NAME} révolutionne la participation citoyenne en garantissant
                                que chaque voix compte dans les décisions locales. Développé par{" "}
                                {APP_PUBLISHER}, notre plateforme utilise la géolocalisation pour
                                s'assurer que seuls les résidents d'une zone donnée peuvent
                                participer aux sondages qui les concernent directement.
                            </p>
                            <p>
                                Nous croyons en une démocratie participative transparente et
                                inclusive, où les décisions sont prises en écoutant véritablement
                                les personnes concernées.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Features Section */}
            <section className="py-16 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">
                        Pourquoi choisir {APP_NAME} ?
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div className="text-center">
                            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg
                                    className="w-8 h-8 text-blue-600"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                                    />
                                </svg>
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                Géolocalisation Vérifiée
                            </h3>
                            <p className="text-gray-600">
                                Validation automatique de votre adresse via l'API gouvernementale
                                française pour garantir l'authenticité des participants.
                            </p>
                        </div>

                        <div className="text-center">
                            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg
                                    className="w-8 h-8 text-green-600"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                                    />
                                </svg>
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                Sécurité & Confidentialité
                            </h3>
                            <p className="text-gray-600">
                                Authentification sécurisée via SMATFLOW SSO et respect total du
                                RGPD. Vos données personnelles sont protégées et anonymisées.
                            </p>
                        </div>

                        <div className="text-center">
                            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg
                                    className="w-8 h-8 text-purple-600"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                                    />
                                </svg>
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                Résultats Transparents
                            </h3>
                            <p className="text-gray-600">
                                Accès en temps réel aux résultats des sondages terminés.
                                Visualisations claires et analyses détaillées pour tous.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* How it Works Section */}
            <section className="py-16">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">
                        Comment ça fonctionne ?
                    </h2>

                    <div className="max-w-4xl mx-auto">
                        <div className="space-y-8">
                            <div className="flex gap-4">
                                <div className="flex-shrink-0">
                                    <div className="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                                        1
                                    </div>
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                        Inscription et validation
                                    </h3>
                                    <p className="text-gray-600">
                                        Créez votre compte via SMATFLOW SSO et validez votre adresse
                                        de résidence. Cette étape unique garantit que vous
                                        participez aux sondages de votre zone.
                                    </p>
                                </div>
                            </div>

                            <div className="flex gap-4">
                                <div className="flex-shrink-0">
                                    <div className="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                                        2
                                    </div>
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                        Participation aux sondages
                                    </h3>
                                    <p className="text-gray-600">
                                        Accédez aux sondages de votre commune, département ou
                                        région. Répondez aux questions en toute confidentialité,
                                        votre vote est anonyme.
                                    </p>
                                </div>
                            </div>

                            <div className="flex gap-4">
                                <div className="flex-shrink-0">
                                    <div className="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                                        3
                                    </div>
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                        Consultation des résultats
                                    </h3>
                                    <p className="text-gray-600">
                                        Une fois le sondage terminé, consultez les résultats
                                        détaillés. Découvrez comment votre communauté a répondu et
                                        suivez l'impact de votre participation.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-16 bg-blue-600">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h2 className="text-3xl font-bold text-white mb-4">
                        Prêt à faire entendre votre voix ?
                    </h2>
                    <p className="text-xl text-blue-100 mb-8">
                        Rejoignez des milliers de citoyens qui participent déjà aux décisions
                        locales
                    </p>
                    <Link
                        href={ROUTES.AUTH_LOGIN}
                        className="inline-flex items-center gap-2 bg-white text-blue-600 font-semibold py-3 px-6 rounded-lg hover:bg-blue-50 transition-colors"
                    >
                        Commencer maintenant
                        <svg
                            className="w-5 h-5"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 7l5 5m0 0l-5 5m5-5H6"
                            />
                        </svg>
                    </Link>
                </div>
            </section>
        </div>
    );
}
