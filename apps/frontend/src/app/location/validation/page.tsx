import { AddressForm } from "@/components/LocationValidation/AddressForm";
import { fetchUserLocation } from "@/lib/server-api";
import { MapPin, Shield, Users } from "lucide-react";
import { redirect } from "next/navigation";

export default async function LocationValidationPage() {
    // Get user's current location
    const userLocation = await fetchUserLocation();

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
            <div className="container mx-auto px-4 py-8">
                {/* Header Section */}
                <div className="text-center mb-12">
                    <div className="inline-flex items-center justify-center w-20 h-20 bg-blue-100 rounded-full mb-6">
                        <MapPin className="w-10 h-10 text-blue-600" />
                    </div>
                    <h1 className="text-4xl font-bold text-gray-900 mb-4">
                        {userLocation?.validated
                            ? "Gérez votre localisation 📍"
                            : "Bienvenue sur CivicPoll ! 🎉"}
                    </h1>
                    <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                        {userLocation?.validated
                            ? "Votre adresse est validée. Vous pouvez la consulter ou la modifier ci-dessous."
                            : "Pour commencer à participer aux sondages de votre communauté, nous avons besoin de vérifier votre localisation."}
                    </p>
                </div>

                {/* Benefits Section - Only show if not validated */}
                {!userLocation?.validated && (
                    <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-12">
                        <div className="bg-white rounded-xl shadow-sm p-6 text-center">
                            <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-4">
                                <Shield className="w-6 h-6 text-green-600" />
                            </div>
                            <h3 className="font-semibold text-gray-900 mb-2">100% Sécurisé</h3>
                            <p className="text-sm text-gray-600">
                                Vos données sont protégées et utilisées uniquement pour déterminer
                                votre zone géographique
                            </p>
                        </div>
                        <div className="bg-white rounded-xl shadow-sm p-6 text-center">
                            <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mb-4">
                                <Users className="w-6 h-6 text-purple-600" />
                            </div>
                            <h3 className="font-semibold text-gray-900 mb-2">Sondages Locaux</h3>
                            <p className="text-sm text-gray-600">
                                Participez aux décisions qui concernent directement votre quartier
                                et votre ville
                            </p>
                        </div>
                        <div className="bg-white rounded-xl shadow-sm p-6 text-center">
                            <div className="inline-flex items-center justify-center w-12 h-12 bg-orange-100 rounded-full mb-4">
                                <span className="text-2xl">🗳️</span>
                            </div>
                            <h3 className="font-semibold text-gray-900 mb-2">Impact Direct</h3>
                            <p className="text-sm text-gray-600">
                                Votre voix compte pour améliorer la vie dans votre communauté
                            </p>
                        </div>
                    </div>
                )}

                {/* Current Location Display */}
                {userLocation && (
                    <div className="max-w-2xl mx-auto mb-8">
                        <div
                            className={`rounded-xl p-6 ${
                                userLocation.validated
                                    ? "bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200"
                                    : "bg-yellow-50 border border-yellow-200"
                            }`}
                        >
                            <h3
                                className={`font-semibold mb-2 flex items-center ${
                                    userLocation.validated ? "text-green-900" : "text-yellow-900"
                                }`}
                            >
                                <MapPin className="w-5 h-5 mr-2" />
                                {userLocation.validated
                                    ? "Votre adresse actuelle"
                                    : "Localisation en attente de validation"}
                            </h3>
                            {userLocation.address && (
                                <p
                                    className={
                                        userLocation.validated
                                            ? "text-green-800"
                                            : "text-yellow-800"
                                    }
                                >
                                    <span className="font-medium">{userLocation.address}</span>
                                </p>
                            )}
                            {(userLocation.city || userLocation.postalCode) && (
                                <p
                                    className={`text-sm mt-1 ${userLocation.validated ? "text-green-700" : "text-yellow-700"}`}
                                >
                                    {userLocation.postalCode} {userLocation.city}
                                </p>
                            )}
                            {userLocation.validated && userLocation.geographicZone && (
                                <p className="text-sm text-green-600 mt-2">
                                    Zone :{" "}
                                    <span className="font-medium">
                                        {userLocation.geographicZone.name}
                                    </span>
                                </p>
                            )}
                            {userLocation.validated && userLocation.validatedAt && (
                                <p className="text-sm text-green-600 mt-3">
                                    ✅ Validée le{" "}
                                    {new Date(userLocation.validatedAt).toLocaleDateString("fr-FR")}
                                </p>
                            )}
                        </div>
                    </div>
                )}

                {/* Address Form */}
                <div className="bg-white rounded-2xl shadow-lg p-8 max-w-2xl mx-auto">
                    <AddressForm currentLocation={userLocation} />
                </div>

                {/* Privacy Note */}
                <div className="text-center mt-8 text-sm text-gray-500 max-w-2xl mx-auto">
                    <p>
                        En validant votre adresse, vous acceptez que votre localisation soit
                        utilisée pour vous proposer des sondages pertinents. Consultez notre{" "}
                        <a href="/privacy" className="text-blue-600 hover:underline">
                            politique de confidentialité
                        </a>
                        .
                    </p>
                </div>
            </div>
        </div>
    );
}
