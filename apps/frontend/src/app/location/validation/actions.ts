"use server";

import { z } from "zod";
import { revalidatePath, revalidateTag } from "next/cache";
import { ActionResult } from "@/types/actions";
import { UserLocation } from "@/types";
import { serverMutation } from "@/lib/server-api";

const locationValidationSchema = z.object({
    address: z.string().min(1, "L'adresse est requise"),
    postalCode: z.string().regex(/^\d{5}$/, "Code postal invalide"),
    city: z.string().min(1, "La ville est requise"),
    latitude: z.number(),
    longitude: z.number(),
});

export async function validateLocationAction(
    formData: FormData,
): Promise<ActionResult<UserLocation>> {
    try {
        // Parse form data
        const data = {
            address: formData.get("address") as string,
            postalCode: formData.get("postalCode") as string,
            city: formData.get("city") as string,
            latitude: parseFloat(formData.get("latitude") as string),
            longitude: parseFloat(formData.get("longitude") as string),
        };

        // Validate data
        const validationResult = locationValidationSchema.safeParse(data);
        if (!validationResult.success) {
            const fieldErrors: Record<string, string[]> = {};
            validationResult.error.errors.forEach((err) => {
                const path = err.path[0] as string;
                if (!fieldErrors[path]) {
                    fieldErrors[path] = [];
                }
                fieldErrors[path].push(err.message);
            });
            return {
                success: false,
                fieldErrors,
            };
        }

        // Call API
        const response = await serverMutation<UserLocation>(
            "/user-locations/validate",
            validationResult.data,
            "POST",
        );

        // Revalidate caches
        revalidateTag("user-location");
        revalidatePath("/dashboard");

        return {
            success: true,
            data: response.data,
        };
    } catch (error) {
        console.error("Location validation error:", error);
        return {
            success: false,
            error: "Erreur lors de la validation de l'adresse",
        };
    }
}

// Wrapper for useActionState compatibility
export async function validateLocationStateAction(
    prevState: ActionResult<UserLocation>,
    formData: FormData,
): Promise<ActionResult<UserLocation>> {
    return validateLocationAction(formData);
}
