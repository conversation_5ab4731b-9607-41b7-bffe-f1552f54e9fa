"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/hooks/useAuth";
import { ROUTES } from "@/lib/constants";
import { Input } from "@/components/ui/Input";

export default function RegisterPage() {
    const router = useRouter();
    const { register } = useAuth();
    const [formData, setFormData] = useState({
        username: "",
        email: "",
        password: "",
        confirmPassword: "",
        acceptTerms: false,
    });
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [isLoading, setIsLoading] = useState(false);

    const validateForm = () => {
        const newErrors: Record<string, string> = {};

        // Username validation
        if (!formData.username) {
            newErrors.username = "Le nom d'utilisateur est requis";
        } else if (formData.username.length < 3) {
            newErrors.username = "Le nom d'utilisateur doit contenir au moins 3 caractères";
        }

        // Email validation
        if (!formData.email) {
            newErrors.email = "L'email est requis";
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = "Email invalide";
        }

        // Password validation
        if (!formData.password) {
            newErrors.password = "Le mot de passe est requis";
        } else if (formData.password.length < 8) {
            newErrors.password = "Le mot de passe doit contenir au moins 8 caractères";
        } else if (!/[a-z]/.test(formData.password)) {
            newErrors.password = "Le mot de passe doit contenir au moins une minuscule";
        } else if (!/[0-9]/.test(formData.password)) {
            newErrors.password = "Le mot de passe doit contenir au moins un chiffre";
        }

        // Confirm password validation
        if (!formData.confirmPassword) {
            newErrors.confirmPassword = "Veuillez confirmer votre mot de passe";
        } else if (formData.password !== formData.confirmPassword) {
            newErrors.confirmPassword = "Les mots de passe ne correspondent pas";
        }

        // Terms acceptance
        if (!formData.acceptTerms) {
            newErrors.acceptTerms = "Vous devez accepter les conditions d'utilisation";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        setIsLoading(true);
        try {
            await register(formData.username, formData.email, formData.password);
            router.push(ROUTES.LOCATION_VALIDATION);
        } catch (error) {
            setErrors({
                general: error instanceof Error ? error.message : "Erreur lors de l'inscription",
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleInputChange = (field: string, value: string | boolean) => {
        setFormData((prev) => ({ ...prev, [field]: value }));
        // Clear error for this field when user starts typing
        if (errors[field]) {
            setErrors((prev) => {
                const newErrors = { ...prev };
                delete newErrors[field];
                return newErrors;
            });
        }
    };

    return (
        <div className="min-h-screen flex items-center justify-center p-4">
            <div className="w-full max-w-md">
                {/* Header */}
                <div className="text-center mb-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-2">Créer un compte</h2>
                    <p className="text-gray-600">
                        Inscrivez-vous pour participer aux sondages de votre région
                    </p>
                </div>

                {/* Main Card */}
                <div className="bg-white rounded-2xl shadow-xl p-8">
                    <form onSubmit={handleSubmit} className="space-y-5">
                        {errors.general && (
                            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                                <p className="text-sm text-red-600">{errors.general}</p>
                            </div>
                        )}

                        <Input
                            id="username"
                            name="username"
                            type="text"
                            label="Nom d'utilisateur"
                            autoComplete="username"
                            required
                            value={formData.username}
                            onChange={(e) => handleInputChange("username", e.target.value)}
                            placeholder="johndoe"
                            error={errors.username}
                            autoFocus
                        />

                        <Input
                            id="email"
                            name="email"
                            type="email"
                            label="Adresse email"
                            autoComplete="email"
                            required
                            value={formData.email}
                            onChange={(e) => handleInputChange("email", e.target.value)}
                            placeholder="<EMAIL>"
                            error={errors.email}
                        />

                        <Input
                            id="password"
                            name="password"
                            type="password"
                            label="Mot de passe"
                            autoComplete="new-password"
                            required
                            value={formData.password}
                            onChange={(e) => handleInputChange("password", e.target.value)}
                            placeholder="••••••••"
                            error={errors.password}
                            helperText="Au moins 8 caractères, avec majuscule, minuscule et chiffre"
                        />

                        <Input
                            id="confirmPassword"
                            name="confirmPassword"
                            type="password"
                            label="Confirmer le mot de passe"
                            autoComplete="new-password"
                            required
                            value={formData.confirmPassword}
                            onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
                            placeholder="••••••••"
                            error={errors.confirmPassword}
                        />

                        <div className="pt-2">
                            <div className="flex items-start">
                                <input
                                    id="acceptTerms"
                                    name="acceptTerms"
                                    type="checkbox"
                                    checked={formData.acceptTerms}
                                    onChange={(e) =>
                                        handleInputChange("acceptTerms", e.target.checked)
                                    }
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5"
                                />
                                <label
                                    htmlFor="acceptTerms"
                                    className="ml-2 block text-sm text-gray-700"
                                >
                                    J'accepte les{" "}
                                    <Link
                                        href={ROUTES.TERMS}
                                        className="text-blue-600 hover:text-blue-500"
                                    >
                                        conditions d'utilisation
                                    </Link>{" "}
                                    et la{" "}
                                    <Link
                                        href={ROUTES.PRIVACY}
                                        className="text-blue-600 hover:text-blue-500"
                                    >
                                        politique de confidentialité
                                    </Link>
                                </label>
                            </div>
                            {errors.acceptTerms && (
                                <p className="mt-1 text-sm text-red-600">{errors.acceptTerms}</p>
                            )}
                        </div>

                        <button
                            type="submit"
                            disabled={isLoading}
                            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {isLoading ? "Inscription en cours..." : "S'inscrire"}
                        </button>
                    </form>
                </div>

                {/* Features Info */}
                <div className="mt-8 bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                    <h3 className="font-semibold text-gray-900 mb-3">Pourquoi créer un compte ?</h3>
                    <ul className="space-y-2 text-sm text-gray-600">
                        <li className="flex items-start">
                            <svg
                                className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                            </svg>
                            <span>Validez votre adresse pour les sondages locaux</span>
                        </li>
                        <li className="flex items-start">
                            <svg
                                className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                            </svg>
                            <span>Accédez à tous les sondages de votre région</span>
                        </li>
                        <li className="flex items-start">
                            <svg
                                className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                            </svg>
                            <span>Suivez vos participations et vos statistiques</span>
                        </li>
                    </ul>
                </div>

                {/* Login Link */}
                <div className="mt-6 text-center">
                    <p className="text-sm text-gray-600">
                        Déjà inscrit ?{" "}
                        <Link
                            href={ROUTES.AUTH_LOGIN}
                            className="font-medium text-blue-600 hover:text-blue-500"
                        >
                            Se connecter
                        </Link>
                    </p>
                </div>

                {/* Footer Links */}
                <div className="mt-6 text-center text-sm text-gray-600">
                    <Link href="/privacy" className="hover:text-gray-800">
                        Politique de confidentialité
                    </Link>
                    <span className="mx-2">•</span>
                    <Link href="/terms" className="hover:text-gray-800">
                        Conditions d'utilisation
                    </Link>
                </div>
            </div>
        </div>
    );
}
