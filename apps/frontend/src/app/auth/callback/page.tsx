import { Suspense } from "react";
import { redirect } from "next/navigation";
import { setAuthCookie } from "@/lib/auth/cookies";
import { API_URL, ROUTES } from "@/lib/constants";

async function handleCallback(searchParams: { [key: string]: string | string[] | undefined }) {
    const code = searchParams.code as string;
    const error = searchParams.error as string;

    if (error) {
        // Handle OAuth errors
        redirect(`${ROUTES.AUTH_ERROR}?message=${encodeURIComponent(error)}`);
    }

    if (!code) {
        redirect(`${ROUTES.AUTH_ERROR}?message=No authorization code received`);
    }

    try {
        // Exchange authorization code for JWT token
        const response = await fetch(`${API_URL}/auth/smatflow/callback?code=${code}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error?.message || "Authentication failed");
        }

        const data = await response.json();

        // Set the JWT token in cookie
        await setAuthCookie(data.jwt);

        // Check if user needs location validation
        if (!data.user.userLocation?.validated) {
            redirect(ROUTES.LOCATION_VALIDATION);
        }

        // Redirect to dashboard or intended destination
        redirect(ROUTES.POLLS);
    } catch (error) {
        console.error("OAuth callback error:", error);
        redirect(
            `${ROUTES.AUTH_ERROR}?message=${encodeURIComponent("Authentication failed. Please try again.")}`,
        );
    }
}

function CallbackContent({
    searchParams,
}: {
    searchParams: { [key: string]: string | string[] | undefined };
}) {
    // Server Component - handle the callback
    handleCallback(searchParams);

    return (
        <div className="min-h-screen flex items-center justify-center">
            <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-4 text-gray-600">Authentification en cours...</p>
            </div>
        </div>
    );
}

export default async function CallbackPage({
    searchParams,
}: {
    searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
    const params = await searchParams;

    return (
        <Suspense
            fallback={
                <div className="min-h-screen flex items-center justify-center">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="mt-4 text-gray-600">Chargement...</p>
                    </div>
                </div>
            }
        >
            <CallbackContent searchParams={params} />
        </Suspense>
    );
}
