"use client";

import React, { useState, Suspense } from "react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { ROUTES } from "@/lib/constants";
import { Input } from "@/components/ui/Input";

function LoginForm() {
    const { login, isLoading } = useAuth();
    const searchParams = useSearchParams();
    const [error, setError] = useState<string | null>(null);

    const [formData, setFormData] = useState({
        identifier: "",
        password: "",
    });

    const from = searchParams.get("from");

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError(null);

        try {
            await login(formData.identifier, formData.password);
        } catch (error) {
            setError(error instanceof Error ? error.message : "Échec de la connexion");
        }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setFormData((prev) => ({
            ...prev,
            [e.target.name]: e.target.value,
        }));
    };

    return (
        <div className="min-h-screen flex items-center justify-center p-4">
            <div className="w-full max-w-md">
                {/* Header */}
                <div className="text-center mb-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-2">Connexion</h2>
                    <p className="text-gray-600">
                        Connectez-vous pour participer aux sondages de votre région
                    </p>
                </div>

                {/* Main Card */}
                <div className="bg-white rounded-2xl shadow-xl p-8">
                    {from && (
                        <div className="mb-6 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                            <p className="text-sm text-blue-800">
                                Vous devez vous connecter pour accéder à cette page
                            </p>
                        </div>
                    )}

                    {/* Login Form */}
                    <form onSubmit={handleSubmit} className="space-y-5">
                        <Input
                            id="identifier"
                            name="identifier"
                            type="text"
                            label="Email ou nom d'utilisateur"
                            required
                            value={formData.identifier}
                            onChange={handleChange}
                            placeholder="<EMAIL>"
                            autoFocus
                        />

                        <Input
                            id="password"
                            name="password"
                            type="password"
                            label="Mot de passe"
                            required
                            value={formData.password}
                            onChange={handleChange}
                            placeholder="••••••••"
                            autoComplete="current-password"
                        />

                        {error && (
                            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                                <p className="text-sm text-red-600">{error}</p>
                            </div>
                        )}

                        <button
                            type="submit"
                            disabled={isLoading}
                            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {isLoading ? "Connexion..." : "Se connecter"}
                        </button>
                    </form>
                </div>

                {/* Features Info */}
                <div className="mt-8 bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                    <h3 className="font-semibold text-gray-900 mb-3">Pourquoi se connecter ?</h3>
                    <ul className="space-y-2 text-sm text-gray-600">
                        <li className="flex items-start">
                            <svg
                                className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                            </svg>
                            <span>Participez aux sondages de votre zone géographique</span>
                        </li>
                        <li className="flex items-start">
                            <svg
                                className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                            </svg>
                            <span>Consultez les résultats en temps réel</span>
                        </li>
                        <li className="flex items-start">
                            <svg
                                className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                            </svg>
                            <span>Vos données sont protégées et anonymisées</span>
                        </li>
                    </ul>
                </div>

                {/* Register Link */}
                <div className="mt-6 text-center">
                    <p className="text-sm text-gray-600">
                        Pas encore de compte ?{" "}
                        <Link
                            href={ROUTES.AUTH_REGISTER}
                            className="font-medium text-blue-600 hover:text-blue-500"
                        >
                            Créer un compte
                        </Link>
                    </p>
                </div>

                {/* Footer Links */}
                <div className="mt-6 text-center text-sm text-gray-600">
                    <Link href="/privacy" className="hover:text-gray-800">
                        Politique de confidentialité
                    </Link>
                    <span className="mx-2">•</span>
                    <Link href="/terms" className="hover:text-gray-800">
                        Conditions d'utilisation
                    </Link>
                </div>
            </div>
        </div>
    );
}

export default function LoginPage() {
    return (
        <Suspense
            fallback={
                <div className="min-h-screen flex items-center justify-center">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="mt-4 text-gray-600">Chargement...</p>
                    </div>
                </div>
            }
        >
            <LoginForm />
        </Suspense>
    );
}
