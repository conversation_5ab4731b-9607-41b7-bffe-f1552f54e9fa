import Link from "next/link";

export default async function AuthErrorPage({
    searchParams,
}: {
    searchParams: Promise<{ message?: string }>;
}) {
    const params = await searchParams;
    const message = params.message || "Une erreur s'est produite lors de l'authentification";

    return (
        <div className="min-h-screen flex items-center justify-center p-4">
            <div className="w-full max-w-md">
                <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
                    {/* Error Icon */}
                    <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg
                            className="w-8 h-8 text-red-600"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                    </div>

                    <h1 className="text-2xl font-bold text-gray-900 mb-2">
                        Erreur d'authentification
                    </h1>

                    <p className="text-gray-600 mb-6">{message}</p>

                    <div className="space-y-3">
                        <Link
                            href="/auth/login"
                            className="block w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
                        >
                            Réessayer
                        </Link>

                        <Link
                            href="/"
                            className="block w-full border border-gray-300 hover:border-gray-400 text-gray-700 font-semibold py-3 px-6 rounded-lg transition-colors"
                        >
                            Retour à l'accueil
                        </Link>
                    </div>

                    <div className="mt-6 text-sm text-gray-500">
                        Si le problème persiste, veuillez{" "}
                        <Link href="/contact" className="text-blue-600 hover:text-blue-700">
                            nous contacter
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    );
}
