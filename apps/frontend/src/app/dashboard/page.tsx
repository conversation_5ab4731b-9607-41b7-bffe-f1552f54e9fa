import { redirect } from "next/navigation";
import Link from "next/link";
import { ROUTES } from "@/lib/constants";
import { UserParticipationStats } from "@/components/Dashboard/UserParticipationStats";
import { RecentParticipations } from "@/components/Dashboard/RecentParticipations";
import { getCurrentUser } from "@/lib/auth/server";
import { fetchUserParticipations, fetchUserStats } from "@/lib/server-api";

export default async function DashboardPage() {
    const user = await getCurrentUser();

    if (!user) {
        redirect(ROUTES.AUTH_LOGIN);
    }

    const [stats, participations] = await Promise.all([
        fetchUserStats(user.documentId),
        fetchUserParticipations(user.documentId),
    ]);

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* <PERSON>er */}
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-gray-900">
                        Bonjour {user.username || user.email.split("@")[0]} 👋
                    </h1>
                    <p className="mt-2 text-gray-600">
                        Voici un aperçu de vos participations aux sondages
                    </p>
                </div>

                {/* Quick Actions */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                    <Link
                        href={ROUTES.POLLS}
                        className="bg-blue-600 hover:bg-blue-700 text-white rounded-lg p-6 transition-colors group"
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <h3 className="text-lg font-semibold mb-1">Voir les sondages</h3>
                                <p className="text-blue-100">
                                    Découvrez les sondages dans votre zone
                                </p>
                            </div>
                            <svg
                                className="w-8 h-8 text-blue-200 group-hover:translate-x-1 transition-transform"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M13 7l5 5m0 0l-5 5m5-5H6"
                                />
                            </svg>
                        </div>
                    </Link>

                    <Link
                        href={ROUTES.PROFILE}
                        className="bg-white hover:shadow-lg border border-gray-200 rounded-lg p-6 transition-shadow group"
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-1">
                                    Mon profil
                                </h3>
                                <p className="text-gray-600">Gérez vos informations personnelles</p>
                            </div>
                            <svg
                                className="w-8 h-8 text-gray-400 group-hover:text-gray-600 transition-colors"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                                />
                            </svg>
                        </div>
                    </Link>
                </div>

                {/* Statistics */}
                <UserParticipationStats stats={stats} />

                {/* Recent Participations */}
                <div className="mt-8">
                    <h2 className="text-xl font-semibold text-gray-900 mb-4">
                        Participations récentes
                    </h2>
                    <RecentParticipations participations={participations} />
                </div>

                {/* Empty State */}
                {participations.length === 0 && (
                    <div className="bg-white rounded-lg shadow-sm p-12 text-center">
                        <svg
                            className="w-16 h-16 text-gray-400 mx-auto mb-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
                            />
                        </svg>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                            Aucune participation pour le moment
                        </h3>
                        <p className="text-gray-600 mb-6">
                            Commencez à participer aux sondages de votre zone pour voir vos
                            statistiques ici
                        </p>
                        <Link
                            href={ROUTES.POLLS}
                            className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                        >
                            Découvrir les sondages
                            <svg
                                className="w-5 h-5"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M13 7l5 5m0 0l-5 5m5-5H6"
                                />
                            </svg>
                        </Link>
                    </div>
                )}
            </div>
        </div>
    );
}
