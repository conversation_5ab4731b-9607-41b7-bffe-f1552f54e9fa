import Link from "next/link";
import { fetchPolls } from "@/lib/server-api";
import { ROUTES } from "@/lib/constants";
import { getCurrentUser } from "@/lib/auth/server";

export default async function Home() {
    const user = await getCurrentUser();

    const polls = await fetchPolls();
    const activePolls = polls.filter((p) => p.pollStatus === "active").slice(0, 3);

    return (
        <div className="min-h-screen">
            {/* Hero Section */}
            <section className="relative overflow-hidden bg-gradient-to-br from-blue-500 via-blue-400 to-sky-400">
                {/* Warm animated background shapes */}
                <div className="absolute inset-0">
                    <div className="absolute top-20 left-10 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
                    <div className="absolute top-40 right-20 w-72 h-72 bg-orange-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
                    <div className="absolute -bottom-8 left-40 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
                </div>

                <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 sm:py-32">
                    <div className="text-center">
                        <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-md text-white text-sm font-medium px-4 py-2 rounded-full mb-6 animate-fade-in-down">
                            <span className="relative flex h-3 w-3">
                                <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-white opacity-75"></span>
                                <span className="relative inline-flex rounded-full h-3 w-3 bg-white"></span>
                            </span>
                            Sondages géolocalisés pour la France
                        </div>

                        <h1 className="text-5xl sm:text-7xl font-bold text-white mb-6 tracking-tight animate-fade-in-up">
                            Votre voix compte
                            <span className="block text-white/90 mt-2">dans votre région</span>
                        </h1>

                        <p className="text-xl sm:text-2xl text-white/80 mb-10 max-w-3xl mx-auto leading-relaxed animate-fade-in-up animation-delay-200">
                            Participez aux décisions locales de manière simple et sécurisée
                        </p>

                        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-fade-in-up animation-delay-400">
                            <Link
                                href={ROUTES.POLLS}
                                className="inline-flex items-center gap-2 bg-white text-blue-600 font-semibold py-4 px-8 rounded-full transition-all hover:scale-105 shadow-lg"
                            >
                                Explorer les sondages
                                <svg
                                    className="w-5 h-5"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M13 7l5 5m0 0l-5 5m5-5H6"
                                    />
                                </svg>
                            </Link>
                            <Link
                                href="#comment-ca-marche"
                                className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm text-white font-semibold py-4 px-8 rounded-full transition-all hover:bg-white/30"
                            >
                                <svg
                                    className="w-5 h-5"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                </svg>
                                Comment ça marche
                            </Link>
                        </div>
                    </div>
                </div>

                {/* Wave separator */}
                <div className="absolute bottom-0 left-0 right-0">
                    <svg
                        className="w-full h-24 fill-white"
                        viewBox="0 0 1440 100"
                        preserveAspectRatio="none"
                    >
                        <path d="M0,50 C150,90 350,10 600,50 C850,90 1050,10 1300,50 C1400,60 1440,50 1440,50 L1440,100 L0,100 Z"></path>
                    </svg>
                </div>
            </section>

            {/* Active Polls Preview */}
            {activePolls.length > 0 && (
                <section className="py-20 bg-gray-50">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
                                Sondages en cours
                            </h2>
                            <p className="text-lg text-gray-600">
                                Participez aux décisions de votre communauté
                            </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                            {activePolls.map((poll, index) => (
                                <div
                                    key={poll.documentId}
                                    className="bg-white rounded-2xl p-6 shadow-sm hover:shadow-lg transition-all hover:-translate-y-1 border border-gray-100"
                                >
                                    <div className="flex items-start justify-between mb-4">
                                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-green-100 to-emerald-100 text-green-800">
                                            <span className="inline-block w-2 h-2 bg-green-500 rounded-full mr-1.5 animate-pulse"></span>
                                            Actif
                                        </span>
                                        <span className="text-sm text-gray-500">
                                            🏠 {poll.geographicZone.name}
                                        </span>
                                    </div>

                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                        {poll.title}
                                    </h3>

                                    <p className="text-gray-600 mb-4 line-clamp-2">
                                        {poll.description}
                                    </p>

                                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                                        <span>
                                            📅 {new Date(poll.endDate).toLocaleDateString("fr-FR")}
                                        </span>
                                        <span>📋 {poll.questions?.length || 0} questions</span>
                                    </div>

                                    <Link
                                        href={ROUTES.POLL_PARTICIPATE(poll.documentId)}
                                        className="block w-full text-center bg-blue-600 text-white py-3 px-4 rounded-xl hover:bg-blue-700 transition-colors font-medium"
                                    >
                                        Participer
                                    </Link>
                                </div>
                            ))}
                        </div>

                        <div className="text-center">
                            <Link
                                href={ROUTES.POLLS}
                                className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium group"
                            >
                                Voir tous les sondages
                                <svg
                                    className="w-5 h-5 group-hover:translate-x-1 transition-transform"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M13 7l5 5m0 0l-5 5m5-5H6"
                                    />
                                </svg>
                            </Link>
                        </div>
                    </div>
                </section>
            )}

            {/* How it Works */}
            <section id="comment-ca-marche" className="py-20 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
                            Comment ça marche ?
                        </h2>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            Participez en 3 étapes simples
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        {/* Step 1 */}
                        <div className="text-center group">
                            <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-sky-100 text-blue-600 rounded-full flex items-center justify-center text-2xl font-bold mb-4 mx-auto transform transition-transform group-hover:scale-110">
                                <span className="relative">
                                    1<span className="absolute -top-1 -right-3 text-2xl">✨</span>
                                </span>
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                Créez votre compte
                            </h3>
                            <p className="text-gray-600">
                                Inscrivez-vous en quelques secondes avec votre email
                            </p>
                        </div>

                        {/* Step 2 */}
                        <div className="text-center group">
                            <div className="w-20 h-20 bg-gradient-to-br from-teal-100 to-emerald-100 text-teal-600 rounded-full flex items-center justify-center text-2xl font-bold mb-4 mx-auto transform transition-transform group-hover:scale-110">
                                <span className="relative">
                                    2<span className="absolute -top-1 -right-3 text-2xl">🎯</span>
                                </span>
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                Validez votre adresse
                            </h3>
                            <p className="text-gray-600">
                                Confirmez votre lieu de résidence pour accéder aux sondages locaux
                            </p>
                        </div>

                        {/* Step 3 */}
                        <div className="text-center group">
                            <div className="w-20 h-20 bg-gradient-to-br from-amber-100 to-yellow-100 text-amber-600 rounded-full flex items-center justify-center text-2xl font-bold mb-4 mx-auto transform transition-transform group-hover:scale-110">
                                <span className="relative">
                                    3<span className="absolute -top-1 -right-3 text-2xl">🎉</span>
                                </span>
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                Votez et consultez
                            </h3>
                            <p className="text-gray-600">
                                Participez aux sondages et découvrez les résultats en temps réel
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Features */}
            <section className="py-20 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
                            Pourquoi CivicPoll ?
                        </h2>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            Une plateforme simple, sécurisée et transparente
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                        {/* Feature 1 */}
                        <div className="text-center group">
                            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-100 to-sky-100 rounded-2xl mb-4 transform transition-all group-hover:scale-110 group-hover:rotate-3">
                                <span className="text-3xl">🔒</span>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">Sécurisé</h3>
                            <p className="text-gray-600 text-sm">
                                Vos données sont protégées et chiffrées
                            </p>
                        </div>

                        {/* Feature 2 */}
                        <div className="text-center group">
                            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-emerald-100 to-green-100 rounded-2xl mb-4 transform transition-all group-hover:scale-110 group-hover:-rotate-3">
                                <span className="text-3xl">✅</span>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                Conforme RGPD
                            </h3>
                            <p className="text-gray-600 text-sm">
                                Respect total de votre vie privée
                            </p>
                        </div>

                        {/* Feature 3 */}
                        <div className="text-center group">
                            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-100 to-pink-100 rounded-2xl mb-4 transform transition-all group-hover:scale-110 group-hover:rotate-3">
                                <span className="text-3xl">🎭</span>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">Anonyme</h3>
                            <p className="text-gray-600 text-sm">Vos votes restent confidentiels</p>
                        </div>

                        {/* Feature 4 */}
                        <div className="text-center group">
                            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-orange-100 to-amber-100 rounded-2xl mb-4 transform transition-all group-hover:scale-110 group-hover:-rotate-3">
                                <span className="text-3xl">📊</span>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                Transparent
                            </h3>
                            <p className="text-gray-600 text-sm">Résultats accessibles à tous</p>
                        </div>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-20 bg-gradient-to-r from-blue-500 via-sky-500 to-blue-500">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
                        Prêt à participer ?
                    </h2>
                    <p className="text-xl text-white/80 mb-8">
                        Rejoignez votre communauté et faites entendre votre voix
                    </p>
                    <div className="flex flex-col items-center gap-4">
                        {user ? (
                            <Link
                                href="/polls"
                                className="inline-flex items-center gap-2 bg-white text-blue-600 font-semibold py-4 px-8 rounded-full hover:scale-105 transition-transform shadow-lg"
                            >
                                Voir les sondages
                                <svg
                                    className="w-5 h-5"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M13 7l5 5m0 0l-5 5m5-5H6"
                                    />
                                </svg>
                            </Link>
                        ) : (
                            <>
                                <Link
                                    href={ROUTES.AUTH_REGISTER}
                                    className="inline-flex items-center gap-2 bg-white text-blue-600 font-semibold py-4 px-8 rounded-full hover:scale-105 transition-transform shadow-lg"
                                >
                                    Commencer gratuitement
                                    <svg
                                        className="w-5 h-5"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M13 7l5 5m0 0l-5 5m5-5H6"
                                        />
                                    </svg>
                                </Link>
                                <div className="text-white/80">
                                    <span>Déjà un compte ?</span>
                                    <Link
                                        href={ROUTES.AUTH_LOGIN}
                                        className="ml-1 font-semibold underline hover:no-underline"
                                    >
                                        Se connecter
                                    </Link>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </section>
        </div>
    );
}
