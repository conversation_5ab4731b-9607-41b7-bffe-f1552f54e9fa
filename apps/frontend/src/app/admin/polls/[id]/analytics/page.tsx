import { fetchPollById, fetchPollAnalytics } from "@/lib/server-api";
import PollAnalyticsView from "@/components/admin/PollAnalyticsView";
import Link from "next/link";

interface PageProps {
    params: Promise<{
        id: string;
    }>;
}

export default async function PollAnalyticsPage({ params }: PageProps) {
    const { id } = await params;
    const [poll, analytics] = await Promise.all([
        fetchPollById(id),
        fetchPollAnalytics(id),
    ]);

    return (
        <div>
            {/* Page Header */}
            <div className="mb-8">
                <Link
                    href="/admin/polls"
                    className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mb-4"
                >
                    <svg
                        className="w-4 h-4 mr-1"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 19l-7-7 7-7"
                        />
                    </svg>
                    Retour aux sondages
                </Link>
                <div className="flex justify-between items-start">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">{poll.title}</h1>
                        <p className="text-gray-600 mt-2">
                            Analyse détaillée des résultats et des participations
                        </p>
                    </div>
                    <div className="flex items-center gap-3">
                        <Link
                            href={`/admin/polls/${id}/edit`}
                            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-xl font-medium hover:bg-gray-300 transition-all"
                        >
                            Modifier
                        </Link>
                        <button className="px-4 py-2 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-all">
                            Exporter les données
                        </button>
                    </div>
                </div>
            </div>

            {/* Analytics View */}
            <PollAnalyticsView poll={poll} analytics={analytics} />
        </div>
    );
}