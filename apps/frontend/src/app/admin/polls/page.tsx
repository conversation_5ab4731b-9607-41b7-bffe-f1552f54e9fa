import { fetchPolls } from "@/lib/server-api";
import PollsManagementTable from "@/components/admin/PollsManagementTable";
import Link from "next/link";

export default async function PollsManagementPage() {
    const polls = await fetchPolls();

    return (
        <div>
            {/* Page Header */}
            <div className="flex justify-between items-center mb-8">
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">Gestion des sondages</h1>
                    <p className="text-gray-600 mt-2">
                        Gérez tous vos sondages, modifiez-les et suivez leur performance.
                    </p>
                </div>
                <Link
                    href="/admin/polls/create"
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-all"
                >
                    <svg
                        className="w-5 h-5 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 4v16m8-8H4"
                        />
                    </svg>
                    Nouveau sondage
                </Link>
            </div>

            {/* Filters */}
            <div className="bg-white rounded-xl p-4 mb-6 shadow-sm">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Statut
                        </label>
                        <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Tous les statuts</option>
                            <option value="draft">Brouillon</option>
                            <option value="scheduled">Programmé</option>
                            <option value="active">Actif</option>
                            <option value="closed">Terminé</option>
                            <option value="archived">Archivé</option>
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Catégorie
                        </label>
                        <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Toutes les catégories</option>
                            <option value="satisfaction">Satisfaction</option>
                            <option value="opinion">Opinion</option>
                            <option value="voting">Vote</option>
                            <option value="consultation">Consultation</option>
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Zone géographique
                        </label>
                        <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Toutes les zones</option>
                            <option value="city">Ville</option>
                            <option value="department">Département</option>
                            <option value="region">Région</option>
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Rechercher
                        </label>
                        <input
                            type="search"
                            placeholder="Titre du sondage..."
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                    </div>
                </div>
            </div>

            {/* Polls Table */}
            <PollsManagementTable polls={polls} />
        </div>
    );
}