import { fetchOrganizations, fetchGeographicZones } from "@/lib/server-api";
import { getCurrentUser } from "@/lib/auth/server";
import OrganizationManagement from "@/components/admin/OrganizationManagement";

export default async function OrganizationsPage() {
    const [user, organizations, zones] = await Promise.all([
        getCurrentUser(),
        fetchOrganizations(),
        fetchGeographicZones(),
    ]);

    // Find user's organization
    const userOrganization = organizations.find(
        (org) => org.smatflowId === user?.smatflowOrganizationId
    );

    return (
        <div>
            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900">Gestion de l'organisation</h1>
                <p className="text-gray-600 mt-2">
                    <PERSON><PERSON>rez les informations de votre organisation et ses administrateurs.
                </p>
            </div>

            <OrganizationManagement
                organization={userOrganization}
                geographicZones={zones}
                currentUser={user!}
            />
        </div>
    );
}