import { fetchPolls } from "@/lib/server-api";
import { getCurrentUser } from "@/lib/auth/server";
import AdminDashboardStats from "@/components/admin/AdminDashboardStats";
import RecentPollsTable from "@/components/admin/RecentPollsTable";
import ParticipationChart from "@/components/admin/ParticipationChart";
import GeographicDistribution from "@/components/admin/GeographicDistribution";
import Link from "next/link";

export default async function AdminDashboard() {
    const user = await getCurrentUser();
    const polls = await fetchPolls();

    // Calculate stats
    const stats = {
        totalPolls: polls.length,
        activePolls: polls.filter((p) => p.pollStatus === "active").length,
        totalParticipations: polls.reduce((acc, poll) => acc + (poll.participations?.length || 0), 0),
        avgParticipationRate: polls.length > 0
            ? (polls.reduce((acc, poll) => {
                  const rate = poll.participations?.length || 0;
                  const population = poll.geographicZone?.population || 1000;
                  return acc + (rate / population) * 100;
              }, 0) / polls.length).toFixed(1)
            : "0",
    };

    const recentPolls = polls.slice(0, 5);

    return (
        <div>
            {/* Page Header */}
            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900">Tableau de bord</h1>
                <p className="text-gray-600 mt-2">
                    Bienvenue, {user?.username}. Voici un aperçu de vos activités.
                </p>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                <Link
                    href="/admin/polls/create"
                    className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-2xl hover:shadow-lg transition-all hover:-translate-y-1 group"
                >
                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="font-semibold text-lg">Créer un sondage</h3>
                            <p className="text-blue-100 text-sm mt-1">
                                Lancez un nouveau sondage
                            </p>
                        </div>
                        <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <svg
                                className="w-6 h-6"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 4v16m8-8H4"
                                />
                            </svg>
                        </div>
                    </div>
                </Link>

                <Link
                    href="/admin/polls"
                    className="bg-white border-2 border-gray-200 p-6 rounded-2xl hover:border-gray-300 hover:shadow-lg transition-all hover:-translate-y-1 group"
                >
                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="font-semibold text-lg text-gray-900">
                                Gérer les sondages
                            </h3>
                            <p className="text-gray-600 text-sm mt-1">
                                Voir tous vos sondages
                            </p>
                        </div>
                        <div className="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <svg
                                className="w-6 h-6 text-gray-600"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M4 6h16M4 12h16M4 18h16"
                                />
                            </svg>
                        </div>
                    </div>
                </Link>

                <Link
                    href="/admin/organizations"
                    className="bg-white border-2 border-gray-200 p-6 rounded-2xl hover:border-gray-300 hover:shadow-lg transition-all hover:-translate-y-1 group"
                >
                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="font-semibold text-lg text-gray-900">
                                Organisation
                            </h3>
                            <p className="text-gray-600 text-sm mt-1">
                                Gérer votre organisation
                            </p>
                        </div>
                        <div className="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <svg
                                className="w-6 h-6 text-gray-600"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                                />
                            </svg>
                        </div>
                    </div>
                </Link>
            </div>

            {/* Stats Cards */}
            <AdminDashboardStats stats={stats} />

            {/* Charts Row */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <ParticipationChart polls={polls} />
                <GeographicDistribution polls={polls} />
            </div>

            {/* Recent Polls */}
            <RecentPollsTable polls={recentPolls} />
        </div>
    );
}