import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth/server";
import AdminSidebar from "@/components/admin/AdminSidebar";
import AdminHeader from "@/components/admin/AdminHeader";

export default async function AdminLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    const user = await getCurrentUser();

    // Check if user is authenticated and has admin privileges
    if (!user) {
        redirect("/auth/login?redirect=/admin");
    }

    // Check if user has admin or moderator appRole
    const isAdmin = user.appRole === "admin" || user.appRole === "moderator";

    if (!isAdmin) {
        redirect("/");
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="flex">
                {/* Sidebar */}
                <AdminSidebar />

                {/* Main Content */}
                <div className="flex-1 flex flex-col">
                    {/* Header */}
                    <AdminHeader user={user} />

                    {/* Page Content */}
                    <main className="flex-1 p-6">
                        <div className="max-w-7xl mx-auto">{children}</div>
                    </main>
                </div>
            </div>
        </div>
    );
}