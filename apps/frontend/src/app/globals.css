@import "tailwindcss";

/* CivicPoll Design System - Inspired by French Civic Design */
:root {
    /* Core Colors */
    --background: #ffffff;
    --foreground: #1e293b; /* Deep Navy - Professional text color */
    
    /* Primary Blue Palette */
    --primary: #2563eb; /* Blue-600 - Main brand color */
    --primary-dark: #1d4ed8; /* Blue-700 */
    --primary-light: #3b82f6; /* Blue-500 */
    --primary-foreground: #ffffff;
    
    /* Secondary Colors */
    --secondary: #f8fafc; /* Slate-50 - Light backgrounds */
    --secondary-foreground: #1e293b;
    
    /* Accent Colors */
    --accent: #0ea5e9; /* Sky-500 */
    --accent-light: #38bdf8; /* Sky-400 */
    --accent-dark: #0284c7; /* Sky-600 */
    
    /* Neutral Palette */
    --muted: #f1f5f9; /* Slate-100 */
    --muted-foreground: #64748b; /* Slate-500 */
    
    /* Semantic Colors */
    --success: #059669; /* Emerald-600 */
    --success-light: #10b981; /* Emerald-500 */
    --warning: #d97706; /* Amber-600 */
    --warning-light: #f59e0b; /* Amber-500 */
    --error: #dc2626; /* Red-600 */
    --error-light: #ef4444; /* Red-500 */
    --info: #0284c7; /* Sky-600 */
    
    /* UI Elements */
    --border: #e2e8f0; /* Slate-200 */
    --input: #e2e8f0;
    --ring: #2563eb; /* Primary color for focus states */
    --radius: 0.75rem;
}



/* Base Styles */
@layer base {
    * {
        border-color: var(--border);
    }
    
    body {
        background: var(--background);
        color: var(--foreground);
        font-feature-settings: "rlig" 1, "calt" 1;
    }
}

/* Typography System */
body {
    font-family: var(--font-inter, "Inter"), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    letter-spacing: -0.01em;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Selection colors */
::selection {
    background-color: var(--primary);
    color: var(--primary-foreground);
}

::-moz-selection {
    background-color: var(--primary);
    color: var(--primary-foreground);
}

/* Modern Animations */
@keyframes blob {
    0% {
        transform: translate(0px, 0px) scale(1);
    }
    33% {
        transform: translate(30px, -50px) scale(1.1);
    }
    66% {
        transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
        transform: translate(0px, 0px) scale(1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes shimmer {
    0% {
        background-position: -1000px 0;
    }
    100% {
        background-position: 1000px 0;
    }
}

@keyframes fade-in-down {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fade-in-up {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fade-in {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

/* Animation utilities */
.animate-blob {
    animation: blob 7s infinite;
}

.animate-fade-in-down {
    animation: fade-in-down 0.5s ease-out;
}

.animate-fade-in-up {
    animation: fade-in-up 0.5s ease-out;
}

.animate-fade-in {
    animation: fade-in 0.5s ease-out;
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-shimmer {
    background: linear-gradient(90deg, #f0f0f0 0px, #f8f8f8 40px, #f0f0f0 80px);
    background-size: 1000px 100%;
    animation: shimmer 2s infinite;
}

.animation-delay-200 {
    animation-delay: 200ms;
}

.animation-delay-400 {
    animation-delay: 400ms;
}

.animation-delay-600 {
    animation-delay: 600ms;
}

.animation-delay-2000 {
    animation-delay: 2000ms;
}

.animation-delay-4000 {
    animation-delay: 4000ms;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Better focus styles */
/* Focus States */
*:focus {
    outline: none;
}

*:focus-visible {
    outline: none;
    box-shadow: 0 0 0 2px var(--background), 0 0 0 4px var(--primary);
}

.focus-ring {
    outline: none;
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.2);
    border-color: var(--primary);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--muted);
}

::-webkit-scrollbar-thumb {
    background: var(--muted-foreground);
    border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary);
}

/* Gradient Utilities */
.gradient-text {
    background: linear-gradient(to right, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.gradient-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
}

.gradient-mesh {
    background-color: var(--primary-light);
    background-image:
        radial-gradient(at 40% 20%, var(--primary) 0px, transparent 50%),
        radial-gradient(at 80% 0%, var(--accent) 0px, transparent 50%),
        radial-gradient(at 0% 50%, var(--success) 0px, transparent 50%);
}

/* Component Styles */
.civic-card {
    background: var(--background);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.05);
    transition: all 0.2s ease;
}

.civic-card:hover {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    transform: translateY(-2px);
}

/* Button Styles */
.btn-primary {
    background-color: var(--primary);
    color: var(--primary-foreground);
    font-weight: 500;
    padding: 0.5rem 1.25rem;
    border-radius: calc(var(--radius) - 2px);
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.btn-secondary {
    background-color: var(--secondary);
    color: var(--secondary-foreground);
    border: 1px solid var(--border);
    font-weight: 500;
    padding: 0.5rem 1.25rem;
    border-radius: calc(var(--radius) - 2px);
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background-color: var(--muted);
    transform: translateY(-1px);
}

/* Pulse animation for live indicators */
@keyframes pulse-ring {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

.pulse-ring {
    animation: pulse-ring 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Badge Styles */
.civic-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
    border: 1px solid;
}

.civic-badge-primary {
    background-color: rgba(37, 99, 235, 0.1);
    color: var(--primary);
    border-color: rgba(37, 99, 235, 0.2);
}

.civic-badge-success {
    background-color: rgba(5, 150, 105, 0.1);
    color: var(--success);
    border-color: rgba(5, 150, 105, 0.2);
}

.civic-badge-warning {
    background-color: rgba(217, 119, 6, 0.1);
    color: var(--warning);
    border-color: rgba(217, 119, 6, 0.2);
}

.civic-badge-error {
    background-color: rgba(220, 38, 38, 0.1);
    color: var(--error);
    border-color: rgba(220, 38, 38, 0.2);
}

/* Glass Morphism Effects */
.glass {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Input Styles */
.input-modern {
    background: var(--background);
    border: 2px solid var(--border);
    border-radius: calc(var(--radius) - 2px);
    padding: 0.5rem 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-modern:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.input-modern:hover:not(:focus) {
    border-color: var(--muted-foreground);
}

/* Container Utilities */
.civic-container {
    max-width: 80rem;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 640px) {
    .civic-container {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .civic-container {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

/* Section Spacing */
.civic-section {
    padding-top: 3rem;
    padding-bottom: 3rem;
}

@media (min-width: 640px) {
    .civic-section {
        padding-top: 4rem;
        padding-bottom: 4rem;
    }
}

@media (min-width: 1024px) {
    .civic-section {
        padding-top: 5rem;
        padding-bottom: 5rem;
    }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Utility Classes for CSS Variables */
.bg-background {
    background-color: var(--background);
}

.text-foreground {
    color: var(--foreground);
}

.bg-primary {
    background-color: var(--primary);
}

.text-primary {
    color: var(--primary);
}

.bg-secondary {
    background-color: var(--secondary);
}

.text-secondary-foreground {
    color: var(--secondary-foreground);
}

.bg-muted {
    background-color: var(--muted);
}

.text-muted-foreground {
    color: var(--muted-foreground);
}

.border-border {
    border-color: var(--border);
}

.text-success {
    color: var(--success);
}

.text-warning {
    color: var(--warning);
}

.text-error {
    color: var(--error);
}
