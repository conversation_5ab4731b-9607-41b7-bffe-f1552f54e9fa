"use client";

import { useState } from "react";
import { APP_NAME, APP_PUBLISHER } from "@/lib/constants";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/Textarea";
import { Select } from "@/components/ui/Select";

export default function ContactPage() {
    const [formData, setFormData] = useState({
        name: "",
        email: "",
        subject: "",
        message: "",
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitStatus, setSubmitStatus] = useState<"idle" | "success" | "error">("idle");

    const handleChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
    ) => {
        setFormData((prev) => ({
            ...prev,
            [e.target.name]: e.target.value,
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        // Simulate form submission
        try {
            await new Promise((resolve) => setTimeout(resolve, 1000));
            setSubmitStatus("success");
            setFormData({ name: "", email: "", subject: "", message: "" });
        } catch (error) {
            setSubmitStatus("error");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="min-h-screen bg-gray-50 py-12">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="max-w-3xl mx-auto">
                    <h1 className="text-3xl font-bold text-gray-900 mb-8">Contactez-nous</h1>

                    <div className="bg-white rounded-lg shadow-sm p-8 mb-8">
                        <p className="text-gray-600 mb-8">
                            Vous avez une question, une suggestion ou besoin d'assistance ? L'équipe{" "}
                            {APP_NAME} est là pour vous aider.
                        </p>

                        {submitStatus === "success" && (
                            <div className="mb-6 bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg">
                                Votre message a été envoyé avec succès. Nous vous répondrons dans
                                les plus brefs délais.
                            </div>
                        )}

                        {submitStatus === "error" && (
                            <div className="mb-6 bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg">
                                Une erreur s'est produite. Veuillez réessayer plus tard.
                            </div>
                        )}

                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <Input
                                    type="text"
                                    id="name"
                                    name="name"
                                    label="Nom complet"
                                    required
                                    value={formData.name}
                                    onChange={handleChange}
                                    placeholder="Jean Dupont"
                                />

                                <Input
                                    type="email"
                                    id="email"
                                    name="email"
                                    label="Email"
                                    required
                                    value={formData.email}
                                    onChange={handleChange}
                                    placeholder="<EMAIL>"
                                />
                            </div>

                            <Select
                                id="subject"
                                name="subject"
                                label="Sujet"
                                required
                                value={formData.subject}
                                onChange={handleChange}
                                placeholder="Sélectionnez un sujet"
                                options={[
                                    { value: "general", label: "Question générale" },
                                    { value: "technical", label: "Support technique" },
                                    { value: "account", label: "Problème de compte" },
                                    { value: "poll", label: "Question sur un sondage" },
                                    { value: "suggestion", label: "Suggestion d'amélioration" },
                                    { value: "other", label: "Autre" },
                                ]}
                            />

                            <Textarea
                                id="message"
                                name="message"
                                label="Message"
                                required
                                rows={6}
                                value={formData.message}
                                onChange={handleChange}
                                placeholder="Décrivez votre demande en détail..."
                            />

                            <button
                                type="submit"
                                disabled={isSubmitting}
                                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {isSubmitting ? "Envoi en cours..." : "Envoyer le message"}
                            </button>
                        </form>
                    </div>

                    {/* Contact Information */}
                    <div className="bg-white rounded-lg shadow-sm p-8">
                        <h2 className="text-xl font-semibold text-gray-900 mb-6">
                            Autres moyens de nous contacter
                        </h2>

                        <div className="space-y-4">
                            <div className="flex items-start gap-4">
                                <svg
                                    className="w-6 h-6 text-gray-400 flex-shrink-0 mt-0.5"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                    />
                                </svg>
                                <div>
                                    <p className="font-medium text-gray-900">Email</p>
                                    <a
                                        href="mailto:<EMAIL>"
                                        className="text-blue-600 hover:text-blue-700"
                                    >
                                        <EMAIL>
                                    </a>
                                </div>
                            </div>

                            <div className="flex items-start gap-4">
                                <svg
                                    className="w-6 h-6 text-gray-400 flex-shrink-0 mt-0.5"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                                    />
                                </svg>
                                <div>
                                    <p className="font-medium text-gray-900">{APP_PUBLISHER}</p>
                                    <p className="text-gray-600">Paris, France</p>
                                </div>
                            </div>

                            <div className="flex items-start gap-4">
                                <svg
                                    className="w-6 h-6 text-gray-400 flex-shrink-0 mt-0.5"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                </svg>
                                <div>
                                    <p className="font-medium text-gray-900">Heures d'ouverture</p>
                                    <p className="text-gray-600">Lundi - Vendredi : 9h00 - 18h00</p>
                                    <p className="text-gray-600">
                                        Support technique disponible 24/7
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
