"use client";

import { useRouter } from "next/navigation";
import { useTransition } from "react";
import {
    login as loginAction,
    logout as logoutAction,
    register as registerAction,
    loginWithSMATFLOW as smatflowAction,
} from "@/app/actions/auth";

export function useAuth() {
    const router = useRouter();
    const [isPending, startTransition] = useTransition();

    const login = async (identifier: string, password: string) => {
        try {
            const result = await loginAction(identifier, password);
            if (result.success) {
                // Check if user needs to validate location
                const needsLocationValidation = !result.user.userLocation?.validated;

                startTransition(() => {
                    if (needsLocationValidation) {
                        router.push("/location/validation");
                    } else {
                        // Get the redirect URL from query params or default to polls
                        const params = new URLSearchParams(window.location.search);
                        const from = params.get("from") || "/polls";
                        router.push(from);
                    }
                });
            }
            return result;
        } catch (error) {
            throw error;
        }
    };

    const register = async (username: string, email: string, password: string) => {
        try {
            const result = await registerAction(username, email, password);
            if (result.success) {
                startTransition(() => {
                    // After registration, redirect to location validation
                    router.push("/location/validation");
                });
            }
            return result;
        } catch (error) {
            throw error;
        }
    };

    const logout = async () => {
        startTransition(async () => {
            await logoutAction();
        });
    };

    const loginWithSMATFLOW = async () => {
        startTransition(async () => {
            await smatflowAction();
        });
    };

    return {
        login,
        register,
        logout,
        loginWithSMATFLOW,
        isLoading: isPending,
    };
}
