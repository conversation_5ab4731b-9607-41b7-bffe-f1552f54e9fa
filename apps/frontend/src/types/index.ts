// API Response Types
export interface ApiResponse<T> {
    data: T;
    meta?: {
        pagination?: {
            page: number;
            pageSize: number;
            total: number;
            pageCount: number;
        };
    };
}

// Media Type
export interface StrapiMedia {
    documentId: string;
    name: string;
    alternativeText?: string;
    caption?: string;
    url: string;
    formats?: {
        thumbnail?: MediaFormat;
        small?: MediaFormat;
        medium?: MediaFormat;
        large?: MediaFormat;
    };
    mime: string;
    size: number;
    width?: number;
    height?: number;
}

export interface MediaFormat {
    url: string;
    width: number;
    height: number;
    size: number;
}

// User & Authentication Types
export interface UserRole {
    documentId: string;
    name: string;
    description: string;
    type: "public" | "authenticated";
    createdAt: string;
    updatedAt: string;
}

export interface User {
    documentId: string;
    username: string;
    email: string;
    provider: string;
    confirmed: boolean;
    blocked: boolean;
    role?: UserRole;
    smatflowOrganizationId?: string;
    appRole: "user" | "moderator" | "admin";
    createdAt: string;
    updatedAt: string;
}

export interface UserLocation {
    documentId: string;
    address: string;
    postalCode: string;
    city: string;
    latitude: number;
    longitude: number;
    validated: boolean;
    validatedAt?: string;
    validationRequired: boolean;
    geographicZone?: GeographicZone;
    user?: User;
    createdAt: string;
    updatedAt: string;
}

// Geographic Types
export interface GeographicZone {
    id: number;
    documentId: string;
    name: string;
    code: string;
    type: "country" | "region" | "department" | "city";
    population?: number;
    center?: {
        lat: number;
        lng: number;
    };
    boundary?: any; // GeoJSON
    parent?: GeographicZone;
    children?: GeographicZone[];
    polls?: Poll[];
    createdAt: string;
    updatedAt: string;
}

export interface UserStats {
    totalParticipations: number;
    completedPolls: number;
    pendingPolls: number;
    lastParticipationDate: string | null;
}

// Organization Types
export interface Organization {
    documentId: string;
    name: string;
    type: "municipality" | "department" | "region" | "association" | "public_service";
    smatflowId?: string;
    description?: string;
    contactEmail: string;
    active: boolean;
    logo?: StrapiMedia;
    geographicZone?: GeographicZone;
    administrators?: User[];
    polls?: Poll[];
    createdAt: string;
    updatedAt: string;
}

// Poll Types
export interface Poll {
    documentId: string;
    title: string;
    description: string;
    category: "satisfaction" | "opinion" | "voting" | "consultation";
    organization?: Organization;
    geographicZone: GeographicZone;
    startDate: string;
    endDate: string;
    pollStatus: "draft" | "scheduled" | "active" | "closed" | "archived";
    isAnonymous: boolean;
    requiresValidatedLocation: boolean;
    questions?: Question[];
    participations?: Participation[];
    template?: PollTemplate;
    publishedAt?: string;
    createdAt: string;
    updatedAt: string;
}

export interface Question {
    documentId: string;
    text: string;
    type: "single_choice" | "multiple_choice" | "rating" | "text" | "ranking";
    required: boolean;
    order: number;
    options?: Option[];
    ratingScale?: {
        min: number;
        max: number;
        labels?: Record<string, string>;
    };
    maxSelections?: number;
    minSelections?: number;
    poll?: Poll;
    createdAt: string;
    updatedAt: string;
}

export interface Option {
    documentId: string;
    text: string;
    value?: string;
    order: number;
    image?: StrapiMedia;
    question?: Question;
    createdAt: string;
    updatedAt: string;
}

export interface Participation {
    documentId: string;
    user: User;
    poll: Poll;
    responses?: Response[];
    completed: boolean;
    startedAt: string;
    completedAt?: string;
    userGeographicZone?: GeographicZone;
    createdAt: string;
    updatedAt: string;
    questionsAnswered: number;
    totalQuestions: number;
}

export interface Response {
    documentId: string;
    participation: Participation;
    question: Question;
    selectedOptions?: Option[];
    textValue?: string;
    ratingValue?: number;
    rankingOrder?: Array<{ optionDocumentId: string; rank: number }>;
    answeredAt: string;
    createdAt: string;
    updatedAt: string;
}

export interface PollTemplate {
    documentId: string;
    name: string;
    description?: string;
    category: "satisfaction" | "opinion" | "voting" | "consultation";
    questions: any; // JSON structure
    isPublic: boolean;
    organization?: Organization;
    createdAt: string;
    updatedAt: string;
}

// Analytics Types
export interface PollAnalytics {
    poll: {
        documentId: string;
        title: string;
        startDate: string;
        endDate: string;
        status: string;
    };
    statistics: {
        totalParticipants: number;
        targetPopulation: number;
        participationRate: string;
        averageCompletionTime: number;
        completionRate: number;
    };
    responses: QuestionAnalytics[];
    geographic: {
        distribution: GeoDistribution[];
        mainZone: string;
        zoneType: string;
    };
    temporal: TemporalData[];
}

export interface QuestionAnalytics {
    questionDocumentId: string;
    questionText: string;
    type: string;
    data: any[];
}

export interface GeoDistribution {
    zone_name: string;
    zone_type: string;
    participants: number;
}

export interface TemporalData {
    date: string;
    daily_participations: number;
}
