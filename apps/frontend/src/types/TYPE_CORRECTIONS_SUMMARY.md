# Frontend Type Corrections Summary

## Date: 2025-07-07

This document summarizes the type corrections made to align frontend TypeScript types with backend Strapi schemas.

## Key Changes Made:

### 1. Organization Type

- Fixed `type` enum to match backend: `'municipality' | 'department' | 'region' | 'association' | 'public_service'`
- Added missing required fields: `contactEmail`, `active`, `geographicZone`
- Added optional `logo` field with proper `StrapiMedia` type

### 2. Geographic Zone

- Added `polls` relation array

### 3. Question

- Added `maxSelections` and `minSelections` fields for multiple choice questions

### 4. Option

- Made `value` field optional (matching backend schema)
- Changed `image` type from `string` to `StrapiMedia`

### 5. Participation

- Made `user` field required (not optional)
- Added required `startedAt` field
- Renamed `geographicZoneAtParticipation` to `userGeographicZone` (matching backend)

### 6. Response

- Added required `answeredAt` field
- Changed `rankingOrder` type from `Record<string, number>` to `Array<{ optionId: number; rank: number }>` to match backend JSON structure

### 7. Poll Template

- Renamed `questionStructure` to `questions` (matching backend field name)

### 8. New Media Type

Created proper `StrapiMedia` interface to handle Strapi media fields:

```typescript
export interface StrapiMedia {
    id: number;
    documentId: string;
    name: string;
    alternativeText?: string;
    caption?: string;
    url: string;
    formats?: {
        thumbnail?: MediaFormat;
        small?: MediaFormat;
        medium?: MediaFormat;
        large?: MediaFormat;
    };
    mime: string;
    size: number;
    width?: number;
    height?: number;
}
```

## Impact on Components:

- Updated `PollParticipation` component to handle ranking order array format
- Updated participation action to include `startedAt` field
- Fixed type comparison issue in multiple choice handling

## Notes:

- Private fields from backend schemas (like `validationToken`, `ipAddress`, `userAgent`) are not included in frontend types as they're not exposed by the API
- System fields (`id`, `documentId`, `createdAt`, `updatedAt`) are included as they're returned by Strapi API
- Rich text fields are typed as `string` in frontend as they'll be rendered as HTML
