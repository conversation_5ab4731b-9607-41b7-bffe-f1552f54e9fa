import Link from "next/link";
import { ROUTES, APP_NAME, APP_PUBLISHER, APP_DESCRIPTION } from "@/lib/constants";

export function Footer() {
    const currentYear = new Date().getFullYear();

    return (
        <footer className="bg-gradient-to-b from-gray-50 to-white border-t border-gray-200">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                    {/* Brand */}
                    <div className="col-span-1 md:col-span-2">
                        <div className="mb-4">
                            <h3 className="text-2xl font-bold flex items-center">
                                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-sky-500">
                                    Civic
                                </span>
                                <span className="text-gray-900">Poll</span>
                                <span className="ml-2 text-lg">🗳️</span>
                            </h3>
                            <p className="text-sm text-gray-500 mt-1">by {APP_PUBLISHER}</p>
                        </div>
                        <p className="text-gray-600 text-sm">{APP_DESCRIPTION}</p>
                    </div>

                    {/* Quick Links */}
                    <div>
                        <h4 className="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4 flex items-center">
                            <span className="mr-2">🧭</span>
                            Navigation
                        </h4>
                        <ul className="space-y-3">
                            <li>
                                <Link
                                    href={ROUTES.POLLS}
                                    className="text-gray-600 hover:text-blue-600 hover:translate-x-1 transition-all text-sm inline-block"
                                >
                                    Sondages
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href={ROUTES.DASHBOARD}
                                    className="text-gray-600 hover:text-blue-600 hover:translate-x-1 transition-all text-sm inline-block"
                                >
                                    Tableau de bord
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href={ROUTES.PROFILE}
                                    className="text-gray-600 hover:text-blue-600 hover:translate-x-1 transition-all text-sm inline-block"
                                >
                                    Mon profil
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href={ROUTES.LOCATION_VALIDATION}
                                    className="text-gray-600 hover:text-blue-600 hover:translate-x-1 transition-all text-sm inline-block"
                                >
                                    Valider mon adresse
                                </Link>
                            </li>
                        </ul>
                    </div>

                    {/* Support */}
                    <div>
                        <h4 className="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4 flex items-center">
                            <span className="mr-2">💬</span>
                            Support
                        </h4>
                        <ul className="space-y-3">
                            <li>
                                <Link
                                    href={ROUTES.HELP}
                                    className="text-gray-600 hover:text-blue-600 hover:translate-x-1 transition-all text-sm inline-block"
                                >
                                    Centre d'aide
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href={ROUTES.CONTACT}
                                    className="text-gray-600 hover:text-blue-600 hover:translate-x-1 transition-all text-sm inline-block"
                                >
                                    Contact
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href={ROUTES.ABOUT}
                                    className="text-gray-600 hover:text-blue-600 hover:translate-x-1 transition-all text-sm inline-block"
                                >
                                    À propos
                                </Link>
                            </li>
                        </ul>
                    </div>
                </div>

                {/* Bottom Bar */}
                <div className="mt-8 pt-8 border-t border-gray-200">
                    <div className="flex flex-col md:flex-row justify-between items-center gap-4">
                        <p className="text-sm text-gray-500">
                            © {currentYear} {APP_PUBLISHER}. Tous droits réservés.
                        </p>
                        <div className="flex gap-6">
                            <Link
                                href={ROUTES.PRIVACY}
                                className="text-sm text-gray-500 hover:text-blue-600"
                            >
                                Confidentialité
                            </Link>
                            <Link
                                href={ROUTES.TERMS}
                                className="text-sm text-gray-500 hover:text-blue-600"
                            >
                                Conditions d'utilisation
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    );
}
