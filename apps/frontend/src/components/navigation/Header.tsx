import Link from "next/link";
import { getCurrentUser } from "@/lib/auth/server";
import { UserMenu } from "@/components/auth/UserMenu";
import { ROUTES } from "@/lib/constants";

export async function Header() {
    const user = await getCurrentUser();

    return (
        <header className="bg-white shadow-sm border-b border-gray-200">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center h-16">
                    <div className="flex items-center">
                        <Link href={ROUTES.HOME} className="flex-shrink-0">
                            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-sky-500">
                                    Civic
                                </span>
                                <span className="text-gray-800">Poll</span>
                                <span className="ml-2 text-lg">🗳️</span>
                            </h1>
                            <p className="text-xs text-gray-500">by SMATFLOW</p>
                        </Link>
                    </div>

                    <nav className="flex items-center gap-6">
                        <div className="hidden md:flex items-baseline space-x-4">
                            <Link
                                href={ROUTES.HOME}
                                className="text-gray-700 hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-md text-sm font-medium transition-all"
                            >
                                Accueil
                            </Link>
                            <Link
                                href={ROUTES.POLLS}
                                className="text-gray-700 hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-md text-sm font-medium transition-all"
                            >
                                Sondages
                            </Link>
                        </div>

                        <UserMenu user={user} />
                    </nav>
                </div>
            </div>
        </header>
    );
}
