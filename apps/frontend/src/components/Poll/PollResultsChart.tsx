"use client";

import {
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON><PERSON>hart,
    Bar,
    <PERSON>Axis,
    <PERSON><PERSON><PERSON><PERSON>,
    CartesianGrid,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    ResponsiveContainer,
} from "recharts";
import { QUESTION_TYPES } from "@/lib/constants";

interface PollResultsChartProps {
    question: {
        id: string;
        text: string;
        type: string;
        results: {
            options?: Array<{
                id: string;
                text: string;
                count: number;
                percentage: number;
            }>;
            textResponses?: string[];
            ratings?: {
                average: number;
                distribution: Record<string, number>;
            };
            rankings?: Array<{
                optionId: string;
                optionText: string;
                averageRank: number;
            }>;
        };
    };
}

const COLORS = [
    "#3B82F6",
    "#10B981",
    "#F59E0B",
    "#EF4444",
    "#8B5CF6",
    "#EC4899",
    "#6366F1",
    "#14B8A6",
];

export function PollResultsChart({ question }: PollResultsChartProps) {
    switch (question.type) {
        case QUESTION_TYPES.SINGLE_CHOICE:
        case QUESTION_TYPES.MULTIPLE_CHOICE:
            if (!question.results.options) return null;

            const pieData = question.results.options.map((option) => ({
                name: option.text,
                value: option.count,
                percentage: option.percentage,
            }));

            return (
                <div className="space-y-4">
                    {/* Pie Chart */}
                    <div className="h-64">
                        <ResponsiveContainer width="100%" height="100%">
                            <PieChart>
                                <Pie
                                    data={pieData}
                                    cx="50%"
                                    cy="50%"
                                    labelLine={false}
                                    label={({ percentage }) => `${percentage.toFixed(1)}%`}
                                    outerRadius={80}
                                    fill="#8884d8"
                                    dataKey="value"
                                >
                                    {pieData.map((entry, index) => (
                                        <Cell
                                            key={`cell-${index}`}
                                            fill={COLORS[index % COLORS.length]}
                                        />
                                    ))}
                                </Pie>
                                <Tooltip />
                            </PieChart>
                        </ResponsiveContainer>
                    </div>

                    {/* Legend with counts */}
                    <div className="space-y-2">
                        {question.results.options.map((option, index) => (
                            <div key={option.id} className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <div
                                        className="w-4 h-4 rounded"
                                        style={{ backgroundColor: COLORS[index % COLORS.length] }}
                                    />
                                    <span className="text-sm text-gray-700">{option.text}</span>
                                </div>
                                <div className="text-sm text-gray-600">
                                    {option.count} votes ({option.percentage.toFixed(1)}%)
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            );

        case QUESTION_TYPES.RATING:
            if (!question.results.ratings) return null;

            const ratingData = Object.entries(question.results.ratings.distribution).map(
                ([rating, count]) => ({
                    rating: `${rating} étoiles`,
                    count,
                }),
            );

            return (
                <div className="space-y-4">
                    <div className="bg-blue-50 rounded-lg p-4">
                        <div className="flex items-center gap-2">
                            <span className="text-lg font-medium text-gray-900">Note moyenne:</span>
                            <div className="flex items-center gap-1">
                                {[1, 2, 3, 4, 5].map((star) => (
                                    <svg
                                        key={star}
                                        className={`w-5 h-5 ${
                                            star <= Math.round(question.results.ratings!.average)
                                                ? "text-yellow-400 fill-current"
                                                : "text-gray-300"
                                        }`}
                                        viewBox="0 0 20 20"
                                    >
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                ))}
                                <span className="ml-2 text-gray-600">
                                    {question.results.ratings.average.toFixed(1)}/5
                                </span>
                            </div>
                        </div>
                    </div>

                    <div className="h-64">
                        <ResponsiveContainer width="100%" height="100%">
                            <BarChart data={ratingData}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="rating" />
                                <YAxis />
                                <Tooltip />
                                <Bar dataKey="count" fill="#3B82F6" />
                            </BarChart>
                        </ResponsiveContainer>
                    </div>
                </div>
            );

        case QUESTION_TYPES.TEXT:
            if (!question.results.textResponses) return null;

            return (
                <div className="space-y-3">
                    <p className="text-sm text-gray-600">
                        {question.results.textResponses.length} réponses
                    </p>
                    <div className="max-h-96 overflow-y-auto space-y-2">
                        {question.results.textResponses.map((response, index) => (
                            <div key={index} className="bg-gray-50 rounded-lg p-3">
                                <p className="text-sm text-gray-700">{response}</p>
                            </div>
                        ))}
                    </div>
                </div>
            );

        case QUESTION_TYPES.RANKING:
            if (!question.results.rankings) return null;

            const rankingData = question.results.rankings
                .sort((a, b) => a.averageRank - b.averageRank)
                .map((item, index) => ({
                    ...item,
                    rank: index + 1,
                }));

            return (
                <div className="space-y-3">
                    {rankingData.map((item) => (
                        <div
                            key={item.optionId}
                            className="flex items-center gap-4 bg-gray-50 rounded-lg p-4"
                        >
                            <div className="flex-shrink-0 w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                                {item.rank}
                            </div>
                            <div className="flex-grow">
                                <p className="font-medium text-gray-900">{item.optionText}</p>
                                <p className="text-sm text-gray-600">
                                    Position moyenne: {item.averageRank.toFixed(1)}
                                </p>
                            </div>
                        </div>
                    ))}
                </div>
            );

        default:
            return <p className="text-gray-500">Type de question non supporté</p>;
    }
}
