"use client";

import { useState } from "react";
import { useActionState } from "react";
import { submitParticipationStateAction } from "@/app/polls/[id]/participate/actions";
import { Poll, Question } from "@/types";
import { Textarea } from "@/components/ui/Textarea";

interface PollParticipationProps {
    poll: Poll;
}

export function PollParticipation({ poll }: PollParticipationProps) {
    const [currentQuestion, setCurrentQuestion] = useState(0);
    const [responses, setResponses] = useState<Record<string, any>>({});
    const [state, formAction, isPending] = useActionState(submitParticipationStateAction, {
        success: false,
    });

    const questions = poll.questions || [];
    const question = questions[currentQuestion];
    const progress = ((currentQuestion + 1) / questions.length) * 100;

    const handleNext = () => {
        if (currentQuestion < questions.length - 1) {
            setCurrentQuestion(currentQuestion + 1);
        }
    };

    const handlePrevious = () => {
        if (currentQuestion > 0) {
            setCurrentQuestion(currentQuestion - 1);
        }
    };

    const isCurrentQuestionAnswered = () => {
        return !question?.required || !!responses[question.documentId];
    };

    const renderQuestion = () => {
        if (!question) return null;

        switch (question.type) {
            case "single_choice":
                return (
                    <div className="space-y-3">
                        {question.options?.map((option) => (
                            <label
                                key={option.documentId}
                                className="flex items-center p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                            >
                                <input
                                    type="radio"
                                    name={`question-${question.documentId}`}
                                    value={option.documentId}
                                    checked={responses[question.documentId] === option.documentId}
                                    onChange={(e) =>
                                        setResponses({
                                            ...responses,
                                            [question.documentId]: e.target.value,
                                        })
                                    }
                                    className="mr-3"
                                />
                                <span className="text-lg">{option.text}</span>
                            </label>
                        ))}
                    </div>
                );

            case "multiple_choice":
                return (
                    <div className="space-y-3">
                        {question.options?.map((option) => (
                            <label
                                key={option.documentId}
                                className="flex items-center p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                            >
                                <input
                                    type="checkbox"
                                    value={option.documentId}
                                    checked={(responses[question.documentId] || []).includes(
                                        option.documentId,
                                    )}
                                    onChange={(e) => {
                                        const current = responses[question.documentId] || [];
                                        const updated = e.target.checked
                                            ? [...current, option.documentId]
                                            : current.filter(
                                                  (id: string) => id !== option.documentId,
                                              );
                                        setResponses({
                                            ...responses,
                                            [question.documentId]: updated,
                                        });
                                    }}
                                    className="mr-3"
                                />
                                <span className="text-lg">{option.text}</span>
                            </label>
                        ))}
                    </div>
                );

            case "rating":
                const maxRating = question.ratingScale?.max || 5;
                const minRating = question.ratingScale?.min || 1;
                const ratingValues = Array.from(
                    { length: maxRating - minRating + 1 },
                    (_, i) => i + minRating,
                );

                return (
                    <div className="flex justify-center space-x-2 py-4">
                        {ratingValues.map((value) => (
                            <button
                                key={value}
                                type="button"
                                onClick={() =>
                                    setResponses({
                                        ...responses,
                                        [question.documentId]: value,
                                    })
                                }
                                className={`w-12 h-12 rounded-full text-2xl transition-colors ${
                                    responses[question.documentId] >= value
                                        ? "bg-yellow-400 text-white"
                                        : "bg-gray-200 text-gray-500 hover:bg-gray-300"
                                }`}
                            >
                                ★
                            </button>
                        ))}
                    </div>
                );

            case "text":
                return (
                    <Textarea
                        value={responses[question.documentId] || ""}
                        onChange={(e) =>
                            setResponses({
                                ...responses,
                                [question.documentId]: e.target.value,
                            })
                        }
                        rows={4}
                        maxLength={500}
                        placeholder="Votre réponse..."
                    />
                );

            case "ranking":
                // Ranking implementation with proper format
                return (
                    <div className="space-y-2">
                        {question.options?.map((option, index) => {
                            const currentRankings = responses[question.documentId] || [];
                            const optionRanking = currentRankings.find(
                                (r: any) => r.optionId === option.documentId,
                            );

                            return (
                                <div
                                    key={option.documentId}
                                    className="flex items-center space-x-2"
                                >
                                    <input
                                        type="number"
                                        min="1"
                                        max={question.options?.length}
                                        value={optionRanking?.rank || ""}
                                        onChange={(e) => {
                                            const rank = parseInt(e.target.value);
                                            let newRankings = [...currentRankings];

                                            // Remove existing ranking for this option
                                            newRankings = newRankings.filter(
                                                (r: any) => r.optionId !== option.documentId,
                                            );

                                            // Add new ranking if valid
                                            if (!isNaN(rank) && rank > 0) {
                                                newRankings.push({
                                                    optionId: option.documentId,
                                                    rank,
                                                });
                                            }

                                            setResponses({
                                                ...responses,
                                                [question.documentId]: newRankings,
                                            });
                                        }}
                                        className="w-16 px-2 py-1 border rounded"
                                        placeholder={`${index + 1}`}
                                    />
                                    <span>{option.text}</span>
                                </div>
                            );
                        })}
                    </div>
                );

            default:
                return null;
        }
    };


    if (state.success) {
        return (
            <div className="max-w-3xl mx-auto p-6">
                <div className="bg-green-50 border border-green-200 rounded-lg p-8 text-center">
                    <h2 className="text-2xl font-bold text-green-800 mb-4">
                        Merci pour votre participation !
                    </h2>
                    <p className="text-green-700">Vos réponses ont été enregistrées avec succès.</p>
                    <a
                        href={`/polls/${poll.documentId}/results`}
                        className="mt-6 inline-block bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
                    >
                        Voir les résultats
                    </a>
                </div>
            </div>
        );
    }

    return (
        <div className="max-w-3xl mx-auto p-6">
            <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="w-full bg-gray-200 rounded-full h-2 mb-6">
                    <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progress}%` }}
                    />
                </div>

                <h2 className="text-2xl font-bold mb-2">{poll.title}</h2>
                <p className="text-gray-600 mb-6">
                    Question {currentQuestion + 1} sur {questions.length}
                </p>

                <form action={formAction}>
                    <input type="hidden" name="pollDocumentId" value={poll.documentId} />
                    <input type="hidden" name="responses" value={JSON.stringify(responses)} />
                    
                    <div className="mb-8">
                        {question && (
                            <>
                                <h3 className="text-xl mb-4">{question.text}</h3>
                                {question.required && (
                                    <p className="text-sm text-red-500 mb-2">
                                        * Question obligatoire
                                    </p>
                                )}
                                {renderQuestion()}
                            </>
                        )}
                    </div>

                    {state.error && (
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                            <p className="text-red-800">{state.error}</p>
                        </div>
                    )}

                    <div className="flex justify-between">
                        <button
                            type="button"
                            onClick={handlePrevious}
                            disabled={currentQuestion === 0}
                            className="px-4 py-2 border rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors"
                        >
                            Précédent
                        </button>

                        {currentQuestion === questions.length - 1 ? (
                            <button
                                type="submit"
                                disabled={!isCurrentQuestionAnswered() || isPending}
                                className="px-6 py-2 bg-blue-600 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-700 transition-colors"
                            >
                                {isPending ? "Envoi en cours..." : "Terminer"}
                            </button>
                        ) : (
                            <button
                                type="button"
                                onClick={handleNext}
                                disabled={!isCurrentQuestionAnswered()}
                                className="px-6 py-2 bg-blue-600 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-700 transition-colors"
                            >
                                Suivant
                            </button>
                        )}
                    </div>
                </form>
            </div>
        </div>
    );
}
