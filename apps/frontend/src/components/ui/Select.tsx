"use client";

import React, { forwardRef, SelectHTMLAttributes } from "react";
import { cn } from "@/lib/utils";

export interface SelectProps extends SelectHTMLAttributes<HTMLSelectElement> {
    label?: string;
    error?: string;
    helperText?: string;
    options: Array<{ value: string; label: string }>;
    placeholder?: string;
}

const Select = forwardRef<HTMLSelectElement, SelectProps>(
    ({ className, label, error, helperText, id, options, placeholder, ...props }, ref) => {
        const selectId = id || `select-${Math.random().toString(36).substring(2, 11)}`;

        return (
            <div className="w-full">
                {label && (
                    <label
                        htmlFor={selectId}
                        className="block text-sm font-medium text-gray-700 mb-1.5"
                    >
                        {label}
                    </label>
                )}
                <select
                    id={selectId}
                    className={cn(
                        "w-full px-4 py-2.5",
                        "text-gray-900",
                        "bg-white",
                        "border border-gray-300",
                        "rounded-lg",
                        "transition-all duration-200",
                        "outline-none",
                        "focus:border-blue-500",
                        "focus:ring-2 focus:ring-blue-500/20",
                        "hover:border-gray-400",
                        "disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed",
                        "appearance-none",
                        "bg-[url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2212%22%20height%3D%228%22%20viewBox%3D%220%200%2012%208%22%3E%3Cpath%20fill%3D%22%23636363%22%20d%3D%22M10.293%200.293L6%204.586%201.707.293A1%201%200%200%200%20.293%201.707l5%205a1%201%200%200%200%201.414%200l5-5A1%201%200%200%200%2010.293.293z%22%2F%3E%3C%2Fsvg%3E')] bg-[length:12px] bg-[right_16px_center] bg-no-repeat pr-10",
                        error && "border-red-300 focus:border-red-500 focus:ring-red-500/20",
                        className,
                    )}
                    ref={ref}
                    {...props}
                >
                    {placeholder && (
                        <option value="" disabled>
                            {placeholder}
                        </option>
                    )}
                    {options.map((option) => (
                        <option key={option.value} value={option.value}>
                            {option.label}
                        </option>
                    ))}
                </select>
                {error && <p className="mt-1.5 text-sm text-red-600">{error}</p>}
                {helperText && !error && (
                    <p className="mt-1.5 text-xs text-gray-500">{helperText}</p>
                )}
            </div>
        );
    },
);

Select.displayName = "Select";

export { Select };
