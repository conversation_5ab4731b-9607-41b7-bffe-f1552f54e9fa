"use client";

import React, { forwardRef, InputHTMLAttributes } from "react";
import { cn } from "@/lib/utils";

export interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
    label?: string;
    error?: string;
    helperText?: string;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
    ({ className, type, label, error, helperText, id, ...props }, ref) => {
        const inputId = id || `input-${Math.random().toString(36).substring(2, 11)}`;

        return (
            <div className="w-full">
                {label && (
                    <label
                        htmlFor={inputId}
                        className="block text-sm font-medium text-gray-700 mb-1.5"
                    >
                        {label}
                    </label>
                )}
                <input
                    type={type}
                    id={inputId}
                    className={cn(
                        "w-full px-4 py-2.5",
                        "text-gray-900 placeholder-gray-400",
                        "bg-white",
                        "border border-gray-300",
                        "rounded-lg",
                        "transition-all duration-200",
                        "outline-none",
                        "focus:border-blue-500",
                        "focus:ring-2 focus:ring-blue-500/20",
                        "hover:border-gray-400",
                        "disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed",
                        error && "border-red-300 focus:border-red-500 focus:ring-red-500/20",
                        className,
                    )}
                    ref={ref}
                    {...props}
                />
                {error && <p className="mt-1.5 text-sm text-red-600">{error}</p>}
                {helperText && !error && (
                    <p className="mt-1.5 text-xs text-gray-500">{helperText}</p>
                )}
            </div>
        );
    },
);

Input.displayName = "Input";

export { Input };
