"use client";

import React from "react";
import {
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    Pie,
    Cell,
    XAxis,
    YAxis,
    CartesianGrid,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    ResponsiveContainer,
} from "recharts";
import { PollAnalytics as PollAnalyticsType } from "@/types";

interface PollAnalyticsProps {
    analytics: PollAnalyticsType;
}

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

export function PollAnalytics({ analytics }: PollAnalyticsProps) {
    return (
        <div className="p-6 space-y-6">
            <h1 className="text-3xl font-bold mb-6">{analytics.poll.title}</h1>

            {/* Key Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center">
                        <svg
                            className="w-8 h-8 text-blue-500 mr-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                            />
                        </svg>
                        <div>
                            <p className="text-2xl font-bold">
                                {analytics.statistics.totalParticipants}
                            </p>
                            <p className="text-gray-600 text-sm">Participants</p>
                        </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-2">
                        sur {analytics.statistics.targetPopulation} habitants
                    </p>
                </div>

                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center">
                        <svg
                            className="w-8 h-8 text-green-500 mr-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                            />
                        </svg>
                        <div>
                            <p className="text-2xl font-bold">
                                {analytics.statistics.participationRate}%
                            </p>
                            <p className="text-gray-600 text-sm">Taux de participation</p>
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center">
                        <svg
                            className="w-8 h-8 text-yellow-500 mr-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                        <div>
                            <p className="text-2xl font-bold">
                                {analytics.statistics.averageCompletionTime}
                            </p>
                            <p className="text-gray-600 text-sm">Temps moyen (min)</p>
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center">
                        <svg
                            className="w-8 h-8 text-purple-500 mr-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                            />
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                        </svg>
                        <div>
                            <p className="text-lg font-bold">{analytics.geographic.mainZone}</p>
                            <p className="text-gray-600 text-sm">{analytics.geographic.zoneType}</p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Temporal Chart */}
            <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold mb-4">Évolution de la participation</h3>
                <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={analytics.temporal}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis
                            dataKey="date"
                            tickFormatter={(value) => new Date(value).toLocaleDateString("fr-FR")}
                        />
                        <YAxis />
                        <Tooltip
                            labelFormatter={(value) => new Date(value).toLocaleDateString("fr-FR")}
                        />
                        <Legend />
                        <Line
                            type="monotone"
                            dataKey="daily_participations"
                            stroke="#3B82F6"
                            name="Participations journalières"
                            strokeWidth={2}
                        />
                    </LineChart>
                </ResponsiveContainer>
            </div>

            {/* Geographic Distribution */}
            <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold mb-4">Répartition géographique</h3>
                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Zone
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Type
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Participants
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {analytics.geographic.distribution.map((zone, index) => (
                                <tr key={index}>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {zone.zone_name}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {zone.zone_type}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {zone.participants}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Response Charts */}
            {analytics.responses.map((question) => (
                <div key={question.questionDocumentId} className="bg-white rounded-lg shadow p-6">
                    <h3 className="text-lg font-semibold mb-4">{question.questionText}</h3>

                    {question.type === "single_choice" && (
                        <ResponsiveContainer width="100%" height={300}>
                            <PieChart>
                                <Pie
                                    data={question.data}
                                    dataKey="count"
                                    nameKey="label"
                                    cx="50%"
                                    cy="50%"
                                    outerRadius={100}
                                    label={(entry) => `${entry.label}: ${entry.count}`}
                                >
                                    {question.data.map((entry: any, index: number) => (
                                        <Cell
                                            key={`cell-${index}`}
                                            fill={COLORS[index % COLORS.length]}
                                        />
                                    ))}
                                </Pie>
                                <Tooltip />
                            </PieChart>
                        </ResponsiveContainer>
                    )}

                    {question.type === "rating" && (
                        <ResponsiveContainer width="100%" height={300}>
                            <BarChart data={question.data}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="rating" />
                                <YAxis />
                                <Tooltip />
                                <Bar dataKey="count" fill="#3B82F6" />
                            </BarChart>
                        </ResponsiveContainer>
                    )}

                    {question.type === "multiple_choice" && (
                        <ResponsiveContainer width="100%" height={300}>
                            <BarChart data={question.data} layout="horizontal">
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis type="number" />
                                <YAxis dataKey="label" type="category" />
                                <Tooltip />
                                <Bar dataKey="count" fill="#10B981" />
                            </BarChart>
                        </ResponsiveContainer>
                    )}
                </div>
            ))}
        </div>
    );
}
