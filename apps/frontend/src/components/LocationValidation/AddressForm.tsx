"use client";

import { useState } from "react";
import { useActionState } from "react";
import { validateLocationStateAction } from "@/app/location/validation/actions";
import { MapPin, Search, CheckCircle } from "lucide-react";
import type { UserLocation } from "@/types";

interface AddressSuggestion {
    properties: {
        id: string;
        label: string;
        postcode: string;
        city: string;
        x: number;
        y: number;
    };
    geometry: {
        coordinates: [number, number];
    };
}

interface AddressFormProps {
    currentLocation?: UserLocation | null;
}

export function AddressForm({ currentLocation }: AddressFormProps) {
    const [searchQuery, setSearchQuery] = useState("");
    const [selectedAddress, setSelectedAddress] = useState<AddressSuggestion | null>(null);
    const [suggestions, setSuggestions] = useState<AddressSuggestion[]>([]);
    const [isSearching, setIsSearching] = useState(false);
    const [isChangingAddress, setIsChangingAddress] = useState(!currentLocation?.validated);
    const [state, formAction, isPending] = useActionState(validateLocationStateAction, {
        success: false,
    });

    const searchAddresses = async (query: string) => {
        if (query.length < 3) {
            setSuggestions([]);
            return;
        }

        setIsSearching(true);
        try {
            const response = await fetch(
                `https://api-adresse.data.gouv.fr/search/?q=${encodeURIComponent(query)}&limit=5`,
            );
            const data = await response.json();
            setSuggestions(data.features || []);
        } catch (error) {
            console.error("Address search error:", error);
            setSuggestions([]);
        } finally {
            setIsSearching(false);
        }
    };

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setSearchQuery(value);
        if (value !== selectedAddress?.properties.label) {
            setSelectedAddress(null);
        }
        searchAddresses(value);
    };

    return (
        <div>
            <div className="flex items-center mb-6">
                <div className="flex-1">
                    <h2 className="text-2xl font-bold text-gray-900">
                        {currentLocation?.validated && !isChangingAddress
                            ? "Votre adresse validée"
                            : "Validez votre adresse"}
                    </h2>
                    <p className="text-gray-600 mt-1">
                        {currentLocation?.validated && !isChangingAddress
                            ? "Votre localisation actuelle pour les sondages"
                            : "Une étape simple pour rejoindre votre communauté locale"}
                    </p>
                </div>
                <div className="ml-4">
                    <span className="text-4xl">📍</span>
                </div>
            </div>

            {/* Show current address if validated and not changing */}
            {currentLocation?.validated && !isChangingAddress && (
                <div className="mb-6">
                    <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
                        <div className="flex items-start justify-between">
                            <div className="flex-1">
                                <p className="font-medium text-gray-900">
                                    {currentLocation.address}
                                </p>
                                <p className="text-sm text-gray-600 mt-1">
                                    {currentLocation.postalCode} {currentLocation.city}
                                </p>
                                {currentLocation.geographicZone && (
                                    <p className="text-sm text-gray-500 mt-2">
                                        Zone : {currentLocation.geographicZone.name}
                                    </p>
                                )}
                            </div>
                            <button
                                type="button"
                                onClick={() => setIsChangingAddress(true)}
                                className="ml-4 text-sm text-blue-600 hover:text-blue-700 font-medium"
                            >
                                Modifier
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {(!currentLocation?.validated || isChangingAddress) && (
                <form action={formAction}>
                    <div className="space-y-6">
                        <div className="relative">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <Search className="h-5 w-5 text-gray-400" />
                            </div>
                            <input
                                id="address-search"
                                type="text"
                                value={searchQuery}
                                onChange={handleSearchChange}
                                placeholder="Tapez votre adresse complète (ex: 12 rue de la Paix, Paris)..."
                                autoComplete="off"
                                className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition duration-150 ease-in-out"
                            />
                        </div>

                        {isSearching && (
                            <div className="flex items-center justify-center py-4">
                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                                <span className="ml-2 text-gray-500">Recherche en cours...</span>
                            </div>
                        )}

                        {suggestions.length > 0 && !selectedAddress && (
                            <div className="border border-gray-200 rounded-lg divide-y divide-gray-100 bg-white shadow-lg overflow-hidden">
                                <div className="px-4 py-2 bg-gray-50 text-sm text-gray-600">
                                    {suggestions.length} adresse{suggestions.length > 1 ? "s" : ""}{" "}
                                    trouvée{suggestions.length > 1 ? "s" : ""}
                                </div>
                                {suggestions.map((suggestion) => (
                                    <button
                                        key={suggestion.properties.id}
                                        type="button"
                                        onClick={() => {
                                            setSelectedAddress(suggestion);
                                            setSearchQuery(suggestion.properties.label);
                                            setSuggestions([]);
                                        }}
                                        className="w-full text-left p-4 hover:bg-blue-50 transition-all duration-150 group"
                                    >
                                        <div className="flex items-start">
                                            <MapPin className="w-5 h-5 text-gray-400 mt-0.5 mr-3 group-hover:text-blue-600" />
                                            <div className="flex-1">
                                                <div className="font-medium text-gray-900 group-hover:text-blue-600">
                                                    {suggestion.properties.label}
                                                </div>
                                                <div className="text-sm text-gray-500 mt-1">
                                                    {suggestion.properties.postcode}{" "}
                                                    {suggestion.properties.city}
                                                </div>
                                            </div>
                                        </div>
                                    </button>
                                ))}
                            </div>
                        )}

                        {selectedAddress && (
                            <>
                                <input
                                    type="hidden"
                                    name="address"
                                    value={selectedAddress.properties.label}
                                />
                                <input
                                    type="hidden"
                                    name="postalCode"
                                    value={selectedAddress.properties.postcode}
                                />
                                <input
                                    type="hidden"
                                    name="city"
                                    value={selectedAddress.properties.city}
                                />
                                <input
                                    type="hidden"
                                    name="latitude"
                                    value={selectedAddress.geometry.coordinates[1]}
                                />
                                <input
                                    type="hidden"
                                    name="longitude"
                                    value={selectedAddress.geometry.coordinates[0]}
                                />

                                <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
                                    <div className="flex items-start">
                                        <div className="flex-shrink-0">
                                            <CheckCircle className="w-6 h-6 text-green-600" />
                                        </div>
                                        <div className="ml-3 flex-1">
                                            <h4 className="font-semibold text-green-900 mb-1">
                                                Adresse sélectionnée
                                            </h4>
                                            <p className="text-green-800">
                                                {selectedAddress.properties.label}
                                            </p>
                                            <p className="text-sm text-green-600 mt-1">
                                                {selectedAddress.properties.postcode}{" "}
                                                {selectedAddress.properties.city}
                                            </p>
                                            <button
                                                type="button"
                                                onClick={() => {
                                                    setSelectedAddress(null);
                                                    setSearchQuery("");
                                                }}
                                                className="text-sm text-green-700 hover:text-green-900 mt-2 underline"
                                            >
                                                Changer d'adresse
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </>
                        )}

                        {state.fieldErrors && (
                            <div className="bg-red-50 border border-red-200 rounded-xl p-5">
                                <div className="flex items-start">
                                    <div className="flex-shrink-0">
                                        <span className="text-2xl">⚠️</span>
                                    </div>
                                    <div className="ml-3">
                                        <h3 className="text-sm font-medium text-red-800">
                                            Erreur de validation
                                        </h3>
                                        {Object.entries(state.fieldErrors).map(
                                            ([field, errors]) => (
                                                <div key={field} className="mt-2">
                                                    {(Array.isArray(errors)
                                                        ? errors
                                                        : [errors]
                                                    ).map((error, idx) => (
                                                        <p
                                                            key={idx}
                                                            className="text-sm text-red-700"
                                                        >
                                                            {error}
                                                        </p>
                                                    ))}
                                                </div>
                                            ),
                                        )}
                                    </div>
                                </div>
                            </div>
                        )}

                        {state.error && (
                            <div className="bg-red-50 border border-red-200 rounded-xl p-5">
                                <div className="flex items-start">
                                    <div className="flex-shrink-0">
                                        <span className="text-2xl">❌</span>
                                    </div>
                                    <div className="ml-3">
                                        <h3 className="text-sm font-medium text-red-800">Erreur</h3>
                                        <p className="text-sm text-red-700 mt-1">{state.error}</p>
                                    </div>
                                </div>
                            </div>
                        )}

                        {state.success && (
                            <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0">
                                        <span className="text-3xl">🎉</span>
                                    </div>
                                    <div className="ml-4">
                                        <h3 className="text-lg font-semibold text-green-900">
                                            Bravo ! Votre adresse est validée
                                        </h3>
                                        <p className="text-green-700 mt-1">
                                            Vous pouvez maintenant participer aux sondages de votre
                                            zone !
                                        </p>
                                    </div>
                                </div>
                            </div>
                        )}

                        <button
                            type="submit"
                            disabled={!selectedAddress || isPending}
                            className={`
                            w-full py-4 px-6 rounded-xl font-semibold text-white transition-all duration-200
                            ${
                                !selectedAddress || isPending
                                    ? "bg-gray-300 cursor-not-allowed"
                                    : "bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl"
                            }
                        `}
                        >
                            {isPending ? (
                                <span className="flex items-center justify-center">
                                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                                    Validation en cours...
                                </span>
                            ) : (
                                "Valider mon adresse"
                            )}
                        </button>
                        {/* Add cancel button if changing validated address */}
                        {currentLocation?.validated && isChangingAddress && (
                            <button
                                type="button"
                                onClick={() => {
                                    setIsChangingAddress(false);
                                    setSearchQuery("");
                                    setSelectedAddress(null);
                                }}
                                className="w-full py-3 px-6 rounded-xl font-semibold text-gray-700 bg-gray-200 hover:bg-gray-300 transition-all duration-200"
                            >
                                Annuler
                            </button>
                        )}
                    </div>
                </form>
            )}
        </div>
    );
}
