import Link from "next/link";
import { ROUTES } from "@/lib/constants";
import { Participation } from "@/types";

interface RecentParticipationsProps {
    participations: Participation[];
}

export function RecentParticipations({ participations }: RecentParticipationsProps) {
    if (participations.length === 0) {
        return null;
    }

    return (
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="divide-y divide-gray-200">
                {participations.map((participation) => {
                    const progress =
                        participation.totalQuestions > 0
                            ? (participation.questionsAnswered / participation.totalQuestions) * 100
                            : 0;

                    const isCompleted = participation.completedAt !== null;

                    return (
                        <div
                            key={participation.documentId}
                            className="p-6 hover:bg-gray-50 transition-colors"
                        >
                            <div className="flex items-start justify-between">
                                <div className="flex-1">
                                    <h3 className="text-lg font-semibold text-gray-900 mb-1">
                                        {participation.poll.title}
                                    </h3>
                                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                                        {participation.poll.description}
                                    </p>

                                    <div className="flex items-center gap-4 text-sm text-gray-500">
                                        <span>
                                            Commencé le{" "}
                                            {new Date(participation.startedAt).toLocaleDateString(
                                                "fr-FR",
                                            )}
                                        </span>
                                        {isCompleted && (
                                            <span className="flex items-center gap-1 text-green-600">
                                                <svg
                                                    className="w-4 h-4"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    viewBox="0 0 24 24"
                                                >
                                                    <path
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth={2}
                                                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                                    />
                                                </svg>
                                                Complété
                                            </span>
                                        )}
                                    </div>

                                    {!isCompleted && (
                                        <div className="mt-3">
                                            <div className="flex items-center justify-between text-sm mb-1">
                                                <span className="text-gray-600">
                                                    {participation.questionsAnswered} /{" "}
                                                    {participation.totalQuestions} questions
                                                </span>
                                                <span className="font-medium text-gray-900">
                                                    {Math.round(progress)}%
                                                </span>
                                            </div>
                                            <div className="w-full bg-gray-200 rounded-full h-2">
                                                <div
                                                    className="bg-blue-600 h-2 rounded-full transition-all"
                                                    style={{ width: `${progress}%` }}
                                                />
                                            </div>
                                        </div>
                                    )}
                                </div>

                                <div className="ml-4 flex-shrink-0 flex flex-col gap-2">
                                    {participation.poll.pollStatus === "closed" && (
                                        <Link
                                            href={ROUTES.POLL_RESULTS(
                                                participation.poll.documentId,
                                            )}
                                            className="inline-flex items-center gap-1 text-sm text-blue-600 hover:text-blue-700"
                                        >
                                            Voir les résultats
                                            <svg
                                                className="w-4 h-4"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={2}
                                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                                                />
                                            </svg>
                                        </Link>
                                    )}

                                    {!isCompleted && participation.poll.pollStatus === "active" && (
                                        <Link
                                            href={ROUTES.POLL_PARTICIPATE(
                                                participation.poll.documentId,
                                            )}
                                            className="inline-flex items-center gap-1 text-sm bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-lg transition-colors"
                                        >
                                            Continuer
                                            <svg
                                                className="w-4 h-4"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={2}
                                                    d="M13 7l5 5m0 0l-5 5m5-5H6"
                                                />
                                            </svg>
                                        </Link>
                                    )}
                                </div>
                            </div>
                        </div>
                    );
                })}
            </div>
        </div>
    );
}
