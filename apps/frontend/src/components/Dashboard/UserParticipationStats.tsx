interface UserParticipationStatsProps {
    stats: {
        totalParticipations: number;
        completedPolls: number;
        pendingPolls: number;
        lastParticipationDate: string | null;
    };
}

export function UserParticipationStats({ stats }: UserParticipationStatsProps) {
    const statCards = [
        {
            title: "Total des participations",
            value: stats.totalParticipations,
            icon: (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                    />
                </svg>
            ),
            color: "bg-blue-500",
        },
        {
            title: "Sondages complétés",
            value: stats.completedPolls,
            icon: (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                </svg>
            ),
            color: "bg-green-500",
        },
        {
            title: "En cours",
            value: stats.pendingPolls,
            icon: (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                </svg>
            ),
            color: "bg-yellow-500",
        },
    ];

    return (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {statCards.map((stat, index) => (
                <div key={index} className="bg-white rounded-lg shadow-sm p-6">
                    <div className="flex items-center justify-between mb-4">
                        <div className={`${stat.color} text-white p-3 rounded-lg`}>{stat.icon}</div>
                        <span className="text-3xl font-bold text-gray-900">{stat.value}</span>
                    </div>
                    <h3 className="text-sm font-medium text-gray-600">{stat.title}</h3>
                </div>
            ))}

            {stats.lastParticipationDate && (
                <div className="md:col-span-3 bg-gray-50 rounded-lg p-4 mt-4">
                    <p className="text-sm text-gray-600">
                        Dernière participation le{" "}
                        <span className="font-medium text-gray-900">
                            {new Date(stats.lastParticipationDate).toLocaleDateString("fr-FR", {
                                year: "numeric",
                                month: "long",
                                day: "numeric",
                            })}
                        </span>
                    </p>
                </div>
            )}
        </div>
    );
}
