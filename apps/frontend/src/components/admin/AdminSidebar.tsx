"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";

const menuItems = [
    {
        title: "Tableau de bord",
        href: "/admin",
        icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                />
            </svg>
        ),
    },
    {
        title: "Sondages",
        href: "/admin/polls",
        icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
                />
            </svg>
        ),
        subItems: [
            { title: "Tous les sondages", href: "/admin/polls" },
            { title: "Créer un sondage", href: "/admin/polls/create" },
            { title: "Modèles", href: "/admin/polls/templates" },
        ],
    },
    {
        title: "Organisations",
        href: "/admin/organizations",
        icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                />
            </svg>
        ),
    },
    {
        title: "Utilisateurs",
        href: "/admin/users",
        icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                />
            </svg>
        ),
    },
    {
        title: "Zones géographiques",
        href: "/admin/zones",
        icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
            </svg>
        ),
    },
    {
        title: "Paramètres",
        href: "/admin/settings",
        icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                />
                <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
            </svg>
        ),
    },
];

export default function AdminSidebar() {
    const pathname = usePathname();
    const [isCollapsed, setIsCollapsed] = useState(false);
    const [expandedItems, setExpandedItems] = useState<string[]>([]);

    const toggleExpanded = (href: string) => {
        setExpandedItems((prev) =>
            prev.includes(href) ? prev.filter((item) => item !== href) : [...prev, href]
        );
    };

    const isActive = (href: string) => {
        if (href === "/admin") {
            return pathname === "/admin";
        }
        return pathname.startsWith(href);
    };

    return (
        <aside
            className={`${
                isCollapsed ? "w-16" : "w-64"
            } bg-white border-r border-gray-200 transition-all duration-300 flex flex-col h-screen sticky top-0`}
        >
            {/* Logo */}
            <div className="p-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                    <Link
                        href="/admin"
                        className={`flex items-center gap-3 ${isCollapsed ? "justify-center" : ""}`}
                    >
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                            <span className="text-white font-bold text-lg">CP</span>
                        </div>
                        {!isCollapsed && (
                            <div>
                                <h1 className="font-bold text-gray-900">CivicPoll</h1>
                                <p className="text-xs text-gray-500">Administration</p>
                            </div>
                        )}
                    </Link>
                    <button
                        onClick={() => setIsCollapsed(!isCollapsed)}
                        className={`p-1.5 rounded-lg hover:bg-gray-100 transition-colors ${
                            isCollapsed ? "mx-auto mt-2" : ""
                        }`}
                    >
                        <svg
                            className={`w-5 h-5 text-gray-500 transition-transform ${
                                isCollapsed ? "rotate-180" : ""
                            }`}
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M11 19l-7-7 7-7m8 14l-7-7 7-7"
                            />
                        </svg>
                    </button>
                </div>
            </div>

            {/* Navigation */}
            <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
                {menuItems.map((item) => (
                    <div key={item.href}>
                        <Link
                            href={item.href}
                            onClick={(e) => {
                                if (item.subItems) {
                                    e.preventDefault();
                                    toggleExpanded(item.href);
                                }
                            }}
                            className={`flex items-center gap-3 px-3 py-2.5 rounded-xl transition-all ${
                                isActive(item.href)
                                    ? "bg-blue-50 text-blue-600"
                                    : "text-gray-700 hover:bg-gray-50"
                            } ${isCollapsed ? "justify-center" : ""}`}
                            title={isCollapsed ? item.title : undefined}
                        >
                            <span
                                className={`${
                                    isActive(item.href) ? "text-blue-600" : "text-gray-400"
                                }`}
                            >
                                {item.icon}
                            </span>
                            {!isCollapsed && (
                                <>
                                    <span className="font-medium flex-1">{item.title}</span>
                                    {item.subItems && (
                                        <svg
                                            className={`w-4 h-4 transition-transform ${
                                                expandedItems.includes(item.href)
                                                    ? "rotate-90"
                                                    : ""
                                            }`}
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M9 5l7 7-7 7"
                                            />
                                        </svg>
                                    )}
                                </>
                            )}
                        </Link>

                        {/* Sub-items */}
                        {!isCollapsed &&
                            item.subItems &&
                            expandedItems.includes(item.href) && (
                                <div className="ml-11 mt-1 space-y-1">
                                    {item.subItems.map((subItem) => (
                                        <Link
                                            key={subItem.href}
                                            href={subItem.href}
                                            className={`block px-3 py-2 text-sm rounded-lg transition-colors ${
                                                pathname === subItem.href
                                                    ? "bg-blue-50 text-blue-600 font-medium"
                                                    : "text-gray-600 hover:bg-gray-50"
                                            }`}
                                        >
                                            {subItem.title}
                                        </Link>
                                    ))}
                                </div>
                            )}
                    </div>
                ))}
            </nav>

            {/* Footer */}
            {!isCollapsed && (
                <div className="p-4 border-t border-gray-200">
                    <div className="px-3 py-2 bg-blue-50 rounded-lg">
                        <p className="text-sm font-medium text-blue-900">Besoin d'aide ?</p>
                        <p className="text-xs text-blue-700 mt-1">
                            Consultez notre documentation
                        </p>
                    </div>
                </div>
            )}
        </aside>
    );
}