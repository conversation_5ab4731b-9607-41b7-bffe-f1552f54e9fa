"use client";

import { useState } from "react";
import { GeographicZone } from "@/types";
import QuestionBuilder from "./QuestionBuilder";
import { useRouter } from "next/navigation";
import { createPollAction } from "@/app/actions/admin/polls";

interface PollCreationFormProps {
    geographicZones: GeographicZone[];
}

interface PollFormData {
    title: string;
    description: string;
    category: "satisfaction" | "opinion" | "voting" | "consultation";
    geographicZoneId: string;
    startDate: string;
    endDate: string;
    isAnonymous: boolean;
    requiresValidatedLocation: boolean;
    questions: any[];
}

export default function PollCreationForm({ geographicZones }: PollCreationFormProps) {
    const router = useRouter();
    const [currentStep, setCurrentStep] = useState(1);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [formData, setFormData] = useState<PollFormData>({
        title: "",
        description: "",
        category: "opinion",
        geographicZoneId: "",
        startDate: "",
        endDate: "",
        isAnonymous: true,
        requiresValidatedLocation: true,
        questions: [],
    });

    const steps = [
        { id: 1, name: "Informations générales", icon: "📝" },
        { id: 2, name: "Zone géographique", icon: "🗺️" },
        { id: 3, name: "Questions", icon: "❓" },
        { id: 4, name: "Paramètres", icon: "⚙️" },
    ];

    const handleNext = () => {
        if (currentStep < steps.length) {
            setCurrentStep(currentStep + 1);
        }
    };

    const handlePrevious = () => {
        if (currentStep > 1) {
            setCurrentStep(currentStep - 1);
        }
    };

    const handleSubmit = async () => {
        setIsSubmitting(true);
        try {
            // Transform questions for the API
            const questionsWithOrder = formData.questions.map((q, index) => ({
                ...q,
                order: index + 1,
                options: q.options?.map((opt: any, optIndex: number) => ({
                    ...opt,
                    order: optIndex + 1,
                })),
            }));

            const result = await createPollAction({
                ...formData,
                questions: questionsWithOrder,
            });

            if (result.success) {
                router.push("/admin/polls");
            } else {
                alert(result.error || "Erreur lors de la création du sondage");
            }
        } catch (error) {
            console.error("Error creating poll:", error);
            alert("Erreur lors de la création du sondage");
        } finally {
            setIsSubmitting(false);
        }
    };

    const updateFormData = (updates: Partial<PollFormData>) => {
        setFormData((prev) => ({ ...prev, ...updates }));
    };

    return (
        <div className="max-w-4xl mx-auto">
            {/* Progress Steps */}
            <div className="mb-8">
                <div className="flex items-center justify-between">
                    {steps.map((step, index) => (
                        <div key={step.id} className="flex items-center flex-1">
                            <div
                                className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all ${
                                    currentStep > step.id
                                        ? "bg-green-500 border-green-500 text-white"
                                        : currentStep === step.id
                                        ? "bg-blue-500 border-blue-500 text-white"
                                        : "bg-white border-gray-300 text-gray-400"
                                }`}
                            >
                                {currentStep > step.id ? (
                                    <svg
                                        className="w-6 h-6"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M5 13l4 4L19 7"
                                        />
                                    </svg>
                                ) : (
                                    <span className="text-lg">{step.icon}</span>
                                )}
                            </div>
                            <div className="ml-3 flex-1">
                                <p
                                    className={`text-sm font-medium ${
                                        currentStep >= step.id
                                            ? "text-gray-900"
                                            : "text-gray-400"
                                    }`}
                                >
                                    {step.name}
                                </p>
                            </div>
                            {index < steps.length - 1 && (
                                <div
                                    className={`flex-1 h-0.5 mx-4 ${
                                        currentStep > step.id
                                            ? "bg-green-500"
                                            : "bg-gray-300"
                                    }`}
                                />
                            )}
                        </div>
                    ))}
                </div>
            </div>

            {/* Form Content */}
            <div className="bg-white rounded-2xl shadow-sm p-8">
                {currentStep === 1 && (
                    <div className="space-y-6">
                        <h2 className="text-xl font-semibold text-gray-900 mb-6">
                            Informations générales
                        </h2>

                        <div>
                            <label
                                htmlFor="title"
                                className="block text-sm font-medium text-gray-700 mb-2"
                            >
                                Titre du sondage
                            </label>
                            <input
                                type="text"
                                id="title"
                                value={formData.title}
                                onChange={(e) => updateFormData({ title: e.target.value })}
                                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                placeholder="Ex: Consultation sur le nouveau parc municipal"
                            />
                        </div>

                        <div>
                            <label
                                htmlFor="description"
                                className="block text-sm font-medium text-gray-700 mb-2"
                            >
                                Description
                            </label>
                            <textarea
                                id="description"
                                value={formData.description}
                                onChange={(e) => updateFormData({ description: e.target.value })}
                                rows={4}
                                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                                placeholder="Décrivez l'objectif de ce sondage..."
                            />
                        </div>

                        <div>
                            <label
                                htmlFor="category"
                                className="block text-sm font-medium text-gray-700 mb-2"
                            >
                                Catégorie
                            </label>
                            <select
                                id="category"
                                value={formData.category}
                                onChange={(e) =>
                                    updateFormData({
                                        category: e.target.value as any,
                                    })
                                }
                                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            >
                                <option value="satisfaction">Satisfaction</option>
                                <option value="opinion">Opinion</option>
                                <option value="voting">Vote</option>
                                <option value="consultation">Consultation</option>
                            </select>
                        </div>

                        <div className="grid grid-cols-2 gap-6">
                            <div>
                                <label
                                    htmlFor="startDate"
                                    className="block text-sm font-medium text-gray-700 mb-2"
                                >
                                    Date de début
                                </label>
                                <input
                                    type="datetime-local"
                                    id="startDate"
                                    value={formData.startDate}
                                    onChange={(e) =>
                                        updateFormData({ startDate: e.target.value })
                                    }
                                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                />
                            </div>
                            <div>
                                <label
                                    htmlFor="endDate"
                                    className="block text-sm font-medium text-gray-700 mb-2"
                                >
                                    Date de fin
                                </label>
                                <input
                                    type="datetime-local"
                                    id="endDate"
                                    value={formData.endDate}
                                    onChange={(e) =>
                                        updateFormData({ endDate: e.target.value })
                                    }
                                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                />
                            </div>
                        </div>
                    </div>
                )}

                {currentStep === 2 && (
                    <div className="space-y-6">
                        <h2 className="text-xl font-semibold text-gray-900 mb-6">
                            Zone géographique
                        </h2>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-4">
                                Sélectionnez la zone géographique ciblée
                            </label>

                            <div className="space-y-3 max-h-96 overflow-y-auto pr-2">
                                {geographicZones.map((zone) => (
                                    <label
                                        key={zone.documentId}
                                        className={`flex items-center p-4 rounded-xl border-2 cursor-pointer transition-all ${
                                            formData.geographicZoneId === zone.documentId
                                                ? "border-blue-500 bg-blue-50"
                                                : "border-gray-200 hover:border-gray-300"
                                        }`}
                                    >
                                        <input
                                            type="radio"
                                            name="geographicZone"
                                            value={zone.documentId}
                                            checked={formData.geographicZoneId === zone.documentId}
                                            onChange={(e) =>
                                                updateFormData({
                                                    geographicZoneId: e.target.value,
                                                })
                                            }
                                            className="sr-only"
                                        />
                                        <div className="flex-1">
                                            <p className="font-medium text-gray-900">
                                                {zone.name}
                                            </p>
                                            <p className="text-sm text-gray-500">
                                                {zone.type === "city"
                                                    ? "Ville"
                                                    : zone.type === "department"
                                                    ? "Département"
                                                    : zone.type === "region"
                                                    ? "Région"
                                                    : zone.type}
                                                {zone.population &&
                                                    ` • ${zone.population.toLocaleString()} habitants`}
                                            </p>
                                        </div>
                                        {formData.geographicZoneId === zone.documentId && (
                                            <svg
                                                className="w-6 h-6 text-blue-500"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={2}
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                                />
                                            </svg>
                                        )}
                                    </label>
                                ))}
                            </div>
                        </div>
                    </div>
                )}

                {currentStep === 3 && (
                    <div>
                        <h2 className="text-xl font-semibold text-gray-900 mb-6">Questions</h2>
                        <QuestionBuilder
                            questions={formData.questions}
                            onChange={(questions) => updateFormData({ questions })}
                        />
                    </div>
                )}

                {currentStep === 4 && (
                    <div className="space-y-6">
                        <h2 className="text-xl font-semibold text-gray-900 mb-6">Paramètres</h2>

                        <div className="space-y-4">
                            <label className="flex items-start">
                                <input
                                    type="checkbox"
                                    checked={formData.isAnonymous}
                                    onChange={(e) =>
                                        updateFormData({ isAnonymous: e.target.checked })
                                    }
                                    className="mt-1 w-5 h-5 text-blue-600 rounded focus:ring-blue-500"
                                />
                                <div className="ml-3">
                                    <p className="font-medium text-gray-900">
                                        Sondage anonyme
                                    </p>
                                    <p className="text-sm text-gray-500">
                                        Les réponses ne seront pas liées aux identités des participants
                                    </p>
                                </div>
                            </label>

                            <label className="flex items-start">
                                <input
                                    type="checkbox"
                                    checked={formData.requiresValidatedLocation}
                                    onChange={(e) =>
                                        updateFormData({
                                            requiresValidatedLocation: e.target.checked,
                                        })
                                    }
                                    className="mt-1 w-5 h-5 text-blue-600 rounded focus:ring-blue-500"
                                />
                                <div className="ml-3">
                                    <p className="font-medium text-gray-900">
                                        Validation de localisation requise
                                    </p>
                                    <p className="text-sm text-gray-500">
                                        Seuls les utilisateurs ayant validé leur adresse peuvent participer
                                    </p>
                                </div>
                            </label>
                        </div>

                        {/* Preview Summary */}
                        <div className="mt-8 p-6 bg-gray-50 rounded-xl">
                            <h3 className="font-semibold text-gray-900 mb-4">
                                Résumé du sondage
                            </h3>
                            <dl className="space-y-2">
                                <div className="flex justify-between">
                                    <dt className="text-sm text-gray-600">Titre :</dt>
                                    <dd className="text-sm font-medium text-gray-900">
                                        {formData.title || "Non défini"}
                                    </dd>
                                </div>
                                <div className="flex justify-between">
                                    <dt className="text-sm text-gray-600">Catégorie :</dt>
                                    <dd className="text-sm font-medium text-gray-900">
                                        {formData.category}
                                    </dd>
                                </div>
                                <div className="flex justify-between">
                                    <dt className="text-sm text-gray-600">Zone :</dt>
                                    <dd className="text-sm font-medium text-gray-900">
                                        {geographicZones.find(
                                            (z) => z.documentId === formData.geographicZoneId
                                        )?.name || "Non définie"}
                                    </dd>
                                </div>
                                <div className="flex justify-between">
                                    <dt className="text-sm text-gray-600">Questions :</dt>
                                    <dd className="text-sm font-medium text-gray-900">
                                        {formData.questions.length}
                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                )}

                {/* Navigation Buttons */}
                <div className="flex justify-between mt-8">
                    <button
                        type="button"
                        onClick={handlePrevious}
                        disabled={currentStep === 1}
                        className={`px-6 py-3 rounded-xl font-medium transition-all ${
                            currentStep === 1
                                ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                        }`}
                    >
                        Précédent
                    </button>

                    {currentStep < steps.length ? (
                        <button
                            type="button"
                            onClick={handleNext}
                            className="px-6 py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-all"
                        >
                            Suivant
                        </button>
                    ) : (
                        <button
                            type="button"
                            onClick={handleSubmit}
                            disabled={isSubmitting}
                            className="px-6 py-3 bg-green-600 text-white rounded-xl font-medium hover:bg-green-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {isSubmitting ? "Création en cours..." : "Créer le sondage"}
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
}