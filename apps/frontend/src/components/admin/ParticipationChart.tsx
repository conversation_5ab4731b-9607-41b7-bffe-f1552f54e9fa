"use client";

import { Poll } from "@/types";
import { useMemo } from "react";
import {
    LineChart,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    <PERSON>lt<PERSON>,
    Legend,
    ResponsiveContainer,
} from "recharts";

interface ParticipationChartProps {
    polls: Poll[];
}

export default function ParticipationChart({ polls }: ParticipationChartProps) {
    const chartData = useMemo(() => {
        // Group participations by date for the last 30 days
        const last30Days = Array.from({ length: 30 }, (_, i) => {
            const date = new Date();
            date.setDate(date.getDate() - (29 - i));
            return date.toISOString().split("T")[0];
        });

        const participationsByDate = last30Days.map((date) => {
            const participations = polls.reduce((acc, poll) => {
                const count = poll.participations?.filter((p) => {
                    const pDate = new Date(p.createdAt).toISOString().split("T")[0];
                    return pDate === date;
                }).length || 0;
                return acc + count;
            }, 0);

            return {
                date: new Date(date).toLocaleDateString("fr-FR", {
                    day: "numeric",
                    month: "short",
                }),
                participations,
            };
        });

        return participationsByDate;
    }, [polls]);

    return (
        <div className="bg-white rounded-2xl p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">
                Évolution des participations
            </h3>
            <ResponsiveContainer width="100%" height={300}>
                <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis
                        dataKey="date"
                        tick={{ fontSize: 12 }}
                        stroke="#94a3b8"
                        axisLine={{ stroke: "#e2e8f0" }}
                    />
                    <YAxis
                        tick={{ fontSize: 12 }}
                        stroke="#94a3b8"
                        axisLine={{ stroke: "#e2e8f0" }}
                    />
                    <Tooltip
                        contentStyle={{
                            backgroundColor: "#fff",
                            border: "1px solid #e2e8f0",
                            borderRadius: "8px",
                            boxShadow: "0 4px 6px -1px rgb(0 0 0 / 0.1)",
                        }}
                        labelStyle={{ color: "#1e293b", fontWeight: 600 }}
                    />
                    <Legend
                        wrapperStyle={{ paddingTop: "20px" }}
                        iconType="line"
                        iconSize={18}
                    />
                    <Line
                        type="monotone"
                        dataKey="participations"
                        stroke="#2563eb"
                        strokeWidth={3}
                        dot={{ fill: "#2563eb", strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6 }}
                        name="Participations"
                    />
                </LineChart>
            </ResponsiveContainer>
        </div>
    );
}