"use client";

import { Poll } from "@/types";
import Link from "next/link";
import { useState } from "react";
import { deletePollAction, publishPollAction, closePollAction } from "@/app/actions/admin/polls";
import { useRouter } from "next/navigation";

interface PollsManagementTableProps {
    polls: Poll[];
}

export default function PollsManagementTable({ polls: initialPolls }: PollsManagementTableProps) {
    const router = useRouter();
    const [polls, setPolls] = useState(initialPolls);
    const [selectedPolls, setSelectedPolls] = useState<string[]>([]);
    const [isDeleting, setIsDeleting] = useState(false);

    const getStatusBadge = (status: string) => {
        const badges = {
            active: {
                bg: "bg-green-100",
                text: "text-green-800",
                dot: "bg-green-500",
                label: "Actif",
            },
            scheduled: {
                bg: "bg-blue-100",
                text: "text-blue-800",
                dot: "bg-blue-500",
                label: "Programmé",
            },
            closed: {
                bg: "bg-gray-100",
                text: "text-gray-800",
                dot: "bg-gray-500",
                label: "Terminé",
            },
            draft: {
                bg: "bg-yellow-100",
                text: "text-yellow-800",
                dot: "bg-yellow-500",
                label: "Brouillon",
            },
            archived: {
                bg: "bg-red-100",
                text: "text-red-800",
                dot: "bg-red-500",
                label: "Archivé",
            },
        };

        const badge = badges[status as keyof typeof badges] || badges.draft;

        return (
            <span
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badge.bg} ${badge.text}`}
            >
                <span className={`w-2 h-2 rounded-full ${badge.dot} mr-1.5`}></span>
                {badge.label}
            </span>
        );
    };

    const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.checked) {
            setSelectedPolls(polls.map((p) => p.documentId));
        } else {
            setSelectedPolls([]);
        }
    };

    const handleSelectPoll = (pollId: string) => {
        setSelectedPolls((prev) =>
            prev.includes(pollId) ? prev.filter((id) => id !== pollId) : [...prev, pollId]
        );
    };

    const handleDeletePoll = async (pollId: string) => {
        if (!confirm("Êtes-vous sûr de vouloir supprimer ce sondage ?")) return;

        setIsDeleting(true);
        const result = await deletePollAction(pollId);
        if (result.success) {
            setPolls((prev) => prev.filter((p) => p.documentId !== pollId));
            router.refresh();
        } else {
            alert(result.error || "Erreur lors de la suppression");
        }
        setIsDeleting(false);
    };

    const handlePublishPoll = async (pollId: string) => {
        const result = await publishPollAction(pollId);
        if (result.success) {
            router.refresh();
        } else {
            alert(result.error || "Erreur lors de la publication");
        }
    };

    const handleClosePoll = async (pollId: string) => {
        const result = await closePollAction(pollId);
        if (result.success) {
            router.refresh();
        } else {
            alert(result.error || "Erreur lors de la fermeture");
        }
    };

    return (
        <div className="bg-white rounded-2xl shadow-sm overflow-hidden">
            {selectedPolls.length > 0 && (
                <div className="bg-blue-50 border-b border-blue-200 px-6 py-3">
                    <div className="flex items-center justify-between">
                        <span className="text-sm text-blue-800">
                            {selectedPolls.length} sondage(s) sélectionné(s)
                        </span>
                        <div className="flex items-center gap-2">
                            <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
                                Exporter
                            </button>
                            <button className="text-sm text-red-600 hover:text-red-700 font-medium">
                                Supprimer
                            </button>
                        </div>
                    </div>
                </div>
            )}

            <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                        <tr>
                            <th className="px-6 py-3 text-left">
                                <input
                                    type="checkbox"
                                    checked={
                                        polls.length > 0 && selectedPolls.length === polls.length
                                    }
                                    onChange={handleSelectAll}
                                    className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                                />
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Titre
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Zone
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Statut
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Participations
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date de fin
                            </th>
                            <th className="relative px-6 py-3">
                                <span className="sr-only">Actions</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {polls.length > 0 ? (
                            polls.map((poll) => (
                                <tr key={poll.documentId} className="hover:bg-gray-50">
                                    <td className="px-6 py-4">
                                        <input
                                            type="checkbox"
                                            checked={selectedPolls.includes(poll.documentId)}
                                            onChange={() => handleSelectPoll(poll.documentId)}
                                            className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                                        />
                                    </td>
                                    <td className="px-6 py-4">
                                        <div>
                                            <div className="text-sm font-medium text-gray-900">
                                                {poll.title}
                                            </div>
                                            <div className="text-sm text-gray-500">
                                                {poll.questions?.length || 0} questions •{" "}
                                                {poll.category}
                                            </div>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4">
                                        <div className="text-sm text-gray-900">
                                            {poll.geographicZone?.name}
                                        </div>
                                        <div className="text-sm text-gray-500">
                                            {poll.geographicZone?.type === "city"
                                                ? "Ville"
                                                : poll.geographicZone?.type === "department"
                                                ? "Département"
                                                : poll.geographicZone?.type === "region"
                                                ? "Région"
                                                : poll.geographicZone?.type}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4">
                                        {getStatusBadge(poll.pollStatus)}
                                    </td>
                                    <td className="px-6 py-4">
                                        <div className="flex items-center">
                                            <div className="text-sm text-gray-900">
                                                {poll.participations?.length || 0}
                                            </div>
                                            {poll.geographicZone?.population && (
                                                <div className="ml-2 text-xs text-gray-500">
                                                    (
                                                    {(
                                                        ((poll.participations?.length || 0) /
                                                            poll.geographicZone.population) *
                                                        100
                                                    ).toFixed(1)}
                                                    %)
                                                </div>
                                            )}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 text-sm text-gray-900">
                                        {new Date(poll.endDate).toLocaleDateString("fr-FR")}
                                    </td>
                                    <td className="px-6 py-4 text-right">
                                        <div className="relative inline-block text-left">
                                            <button className="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                                                <svg
                                                    className="w-5 h-5 text-gray-500"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    viewBox="0 0 24 24"
                                                >
                                                    <path
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth={2}
                                                        d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                                                    />
                                                </svg>
                                            </button>
                                            <div className="origin-top-right absolute right-0 mt-2 w-48 rounded-lg shadow-lg bg-white ring-1 ring-black ring-opacity-5 hidden">
                                                <div className="py-1">
                                                    <Link
                                                        href={`/admin/polls/${poll.documentId}/analytics`}
                                                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                    >
                                                        Voir les statistiques
                                                    </Link>
                                                    <Link
                                                        href={`/admin/polls/${poll.documentId}/edit`}
                                                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                    >
                                                        Modifier
                                                    </Link>
                                                    {poll.pollStatus === "draft" && (
                                                        <button
                                                            onClick={() =>
                                                                handlePublishPoll(poll.documentId)
                                                            }
                                                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                        >
                                                            Publier
                                                        </button>
                                                    )}
                                                    {poll.pollStatus === "active" && (
                                                        <button
                                                            onClick={() =>
                                                                handleClosePoll(poll.documentId)
                                                            }
                                                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                        >
                                                            Fermer
                                                        </button>
                                                    )}
                                                    <button
                                                        onClick={() => handleDeletePoll(poll.documentId)}
                                                        disabled={isDeleting}
                                                        className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                                                    >
                                                        Supprimer
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            ))
                        ) : (
                            <tr>
                                <td colSpan={7} className="px-6 py-8 text-center">
                                    <div className="text-gray-500">
                                        <svg
                                            className="mx-auto h-12 w-12 text-gray-400 mb-4"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                                            />
                                        </svg>
                                        <p className="text-sm mb-2">Aucun sondage trouvé</p>
                                        <Link
                                            href="/admin/polls/create"
                                            className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                                        >
                                            Créer votre premier sondage
                                        </Link>
                                    </div>
                                </td>
                            </tr>
                        )}
                    </tbody>
                </table>
            </div>

            {/* Pagination */}
            {polls.length > 0 && (
                <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                    <div className="flex items-center justify-between">
                        <div className="text-sm text-gray-700">
                            Affichage <span className="font-medium">1</span> à{" "}
                            <span className="font-medium">{polls.length}</span> sur{" "}
                            <span className="font-medium">{polls.length}</span> résultats
                        </div>
                        <div className="flex items-center gap-2">
                            <button className="px-3 py-1 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                Précédent
                            </button>
                            <button className="px-3 py-1 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                Suivant
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}