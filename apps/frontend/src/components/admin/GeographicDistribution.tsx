"use client";

import { Poll } from "@/types";
import { useMemo } from "react";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON>, Tooltip } from "recharts";

interface GeographicDistributionProps {
    polls: Poll[];
}

const COLORS = ["#2563eb", "#0ea5e9", "#06b6d4", "#14b8a6", "#10b981", "#84cc16"];

export default function GeographicDistribution({ polls }: GeographicDistributionProps) {
    const data = useMemo(() => {
        // Group polls by geographic zone type
        const zoneTypes = polls.reduce((acc, poll) => {
            const type = poll.geographicZone?.type || "unknown";
            acc[type] = (acc[type] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        return Object.entries(zoneTypes).map(([name, value]) => ({
            name: name === "city" ? "Ville" : 
                  name === "department" ? "Département" : 
                  name === "region" ? "Région" : 
                  name === "country" ? "Pays" : name,
            value,
        }));
    }, [polls]);

    const renderCustomizedLabel = ({
        cx,
        cy,
        midAngle,
        innerRadius,
        outerRadius,
        percent,
    }: any) => {
        const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
        const x = cx + radius * Math.cos(-midAngle * (Math.PI / 180));
        const y = cy + radius * Math.sin(-midAngle * (Math.PI / 180));

        return (
            <text
                x={x}
                y={y}
                fill="white"
                textAnchor={x > cx ? "start" : "end"}
                dominantBaseline="central"
                className="font-semibold"
            >
                {`${(percent * 100).toFixed(0)}%`}
            </text>
        );
    };

    return (
        <div className="bg-white rounded-2xl p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">
                Répartition géographique
            </h3>
            {data.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                        <Pie
                            data={data}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={renderCustomizedLabel}
                            outerRadius={100}
                            fill="#8884d8"
                            dataKey="value"
                        >
                            {data.map((entry, index) => (
                                <Cell
                                    key={`cell-${index}`}
                                    fill={COLORS[index % COLORS.length]}
                                />
                            ))}
                        </Pie>
                        <Tooltip
                            contentStyle={{
                                backgroundColor: "#fff",
                                border: "1px solid #e2e8f0",
                                borderRadius: "8px",
                                boxShadow: "0 4px 6px -1px rgb(0 0 0 / 0.1)",
                            }}
                        />
                        <Legend
                            verticalAlign="bottom"
                            height={36}
                            iconType="circle"
                            formatter={(value: string) => (
                                <span className="text-sm text-gray-700">{value}</span>
                            )}
                        />
                    </PieChart>
                </ResponsiveContainer>
            ) : (
                <div className="h-[300px] flex items-center justify-center">
                    <p className="text-gray-500">Aucune donnée disponible</p>
                </div>
            )}
        </div>
    );
}