import { Poll } from "@/types";
import Link from "next/link";

interface RecentPollsTableProps {
    polls: Poll[];
}

export default function RecentPollsTable({ polls }: RecentPollsTableProps) {
    const getStatusBadge = (status: string) => {
        const badges = {
            active: {
                bg: "bg-green-100",
                text: "text-green-800",
                dot: "bg-green-500",
                label: "Actif",
            },
            scheduled: {
                bg: "bg-blue-100",
                text: "text-blue-800",
                dot: "bg-blue-500",
                label: "Programmé",
            },
            closed: {
                bg: "bg-gray-100",
                text: "text-gray-800",
                dot: "bg-gray-500",
                label: "Terminé",
            },
            draft: {
                bg: "bg-yellow-100",
                text: "text-yellow-800",
                dot: "bg-yellow-500",
                label: "Brouillon",
            },
            archived: {
                bg: "bg-red-100",
                text: "text-red-800",
                dot: "bg-red-500",
                label: "Archivé",
            },
        };

        const badge = badges[status as keyof typeof badges] || badges.draft;

        return (
            <span
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badge.bg} ${badge.text}`}
            >
                <span className={`w-2 h-2 rounded-full ${badge.dot} mr-1.5`}></span>
                {badge.label}
            </span>
        );
    };

    return (
        <div className="bg-white rounded-2xl shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900">Sondages récents</h3>
                    <Link
                        href="/admin/polls"
                        className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                    >
                        Voir tout
                    </Link>
                </div>
            </div>

            <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                        <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Titre
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Zone
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Statut
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Participations
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date de fin
                            </th>
                            <th className="relative px-6 py-3">
                                <span className="sr-only">Actions</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {polls.length > 0 ? (
                            polls.map((poll) => (
                                <tr key={poll.documentId} className="hover:bg-gray-50">
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm font-medium text-gray-900">
                                            {poll.title}
                                        </div>
                                        <div className="text-sm text-gray-500">
                                            {poll.questions?.length || 0} questions
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900">
                                            {poll.geographicZone?.name}
                                        </div>
                                        <div className="text-sm text-gray-500">
                                            {poll.geographicZone?.type === "city"
                                                ? "Ville"
                                                : poll.geographicZone?.type === "department"
                                                ? "Département"
                                                : poll.geographicZone?.type === "region"
                                                ? "Région"
                                                : poll.geographicZone?.type}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        {getStatusBadge(poll.pollStatus)}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {poll.participations?.length || 0}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {new Date(poll.endDate).toLocaleDateString("fr-FR")}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div className="flex items-center justify-end gap-2">
                                            <Link
                                                href={`/admin/polls/${poll.documentId}/analytics`}
                                                className="text-blue-600 hover:text-blue-900"
                                            >
                                                Statistiques
                                            </Link>
                                            <Link
                                                href={`/admin/polls/${poll.documentId}/edit`}
                                                className="text-indigo-600 hover:text-indigo-900"
                                            >
                                                Modifier
                                            </Link>
                                        </div>
                                    </td>
                                </tr>
                            ))
                        ) : (
                            <tr>
                                <td colSpan={6} className="px-6 py-8 text-center">
                                    <div className="text-gray-500">
                                        <svg
                                            className="mx-auto h-12 w-12 text-gray-400 mb-4"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                                            />
                                        </svg>
                                        <p className="text-sm">Aucun sondage créé</p>
                                        <Link
                                            href="/admin/polls/create"
                                            className="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                                        >
                                            Créer votre premier sondage
                                        </Link>
                                    </div>
                                </td>
                            </tr>
                        )}
                    </tbody>
                </table>
            </div>
        </div>
    );
}