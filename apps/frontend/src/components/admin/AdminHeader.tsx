"use client";

import { User } from "@/types";
import { UserMenu } from "@/components/auth/UserMenu";
import { useState } from "react";

interface AdminHeaderProps {
    user: User;
}

export default function AdminHeader({ user }: AdminHeaderProps) {
    const [notifications] = useState([
        { id: 1, title: "Nouveau sondage actif", time: "Il y a 5 min", unread: true },
        { id: 2, title: "100 participations atteintes", time: "Il y a 1h", unread: true },
        { id: 3, title: "Sondage terminé", time: "Il y a 2h", unread: false },
    ]);

    const unreadCount = notifications.filter((n) => n.unread).length;

    return (
        <header className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold text-gray-900">Administration</h2>
                    <p className="text-sm text-gray-500 mt-1">
                        <PERSON><PERSON><PERSON> vos sondages et analysez les résultats
                    </p>
                </div>

                <div className="flex items-center gap-4">
                    {/* Search */}
                    <div className="relative">
                        <input
                            type="search"
                            placeholder="Rechercher..."
                            className="w-80 pl-10 pr-4 py-2 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        <svg
                            className="absolute left-3 top-2.5 w-5 h-5 text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                            />
                        </svg>
                    </div>

                    {/* Notifications */}
                    <div className="relative">
                        <button className="relative p-2 rounded-lg hover:bg-gray-100 transition-colors">
                            <svg
                                className="w-6 h-6 text-gray-600"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
                                />
                            </svg>
                            {unreadCount > 0 && (
                                <span className="absolute top-0 right-0 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                                    {unreadCount}
                                </span>
                            )}
                        </button>
                    </div>

                    {/* User Menu */}
                    <UserMenu user={user} />
                </div>
            </div>
        </header>
    );
}