"use client";

import { useState } from "react";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";

interface Question {
    id: string;
    text: string;
    type: "single_choice" | "multiple_choice" | "rating" | "text" | "ranking";
    required: boolean;
    options: { id: string; text: string }[];
    ratingScale?: { min: number; max: number };
}

interface QuestionBuilderProps {
    questions: Question[];
    onChange: (questions: Question[]) => void;
}

export default function QuestionBuilder({ questions, onChange }: QuestionBuilderProps) {
    const [isAddingQuestion, setIsAddingQuestion] = useState(false);
    const [editingQuestionId, setEditingQuestionId] = useState<string | null>(null);

    const questionTypes = [
        { value: "single_choice", label: "Choix unique", icon: "🔘" },
        { value: "multiple_choice", label: "Choix multiple", icon: "☑️" },
        { value: "rating", label: "Évaluation", icon: "⭐" },
        { value: "text", label: "Texte libre", icon: "📝" },
        { value: "ranking", label: "Classement", icon: "🔢" },
    ];

    const handleDragEnd = (result: any) => {
        if (!result.destination) return;

        const newQuestions = Array.from(questions);
        const [reorderedItem] = newQuestions.splice(result.source.index, 1);
        newQuestions.splice(result.destination.index, 0, reorderedItem);

        onChange(newQuestions);
    };

    const addQuestion = (type: Question["type"]) => {
        const newQuestion: Question = {
            id: Date.now().toString(),
            text: "",
            type,
            required: true,
            options: type === "single_choice" || type === "multiple_choice" || type === "ranking"
                ? [
                    { id: "1", text: "" },
                    { id: "2", text: "" },
                  ]
                : [],
            ratingScale: type === "rating" ? { min: 1, max: 5 } : undefined,
        };

        onChange([...questions, newQuestion]);
        setEditingQuestionId(newQuestion.id);
        setIsAddingQuestion(false);
    };

    const updateQuestion = (questionId: string, updates: Partial<Question>) => {
        onChange(
            questions.map((q) => (q.id === questionId ? { ...q, ...updates } : q))
        );
    };

    const deleteQuestion = (questionId: string) => {
        onChange(questions.filter((q) => q.id !== questionId));
    };

    const addOption = (questionId: string) => {
        const question = questions.find((q) => q.id === questionId);
        if (question) {
            const newOption = {
                id: Date.now().toString(),
                text: "",
            };
            updateQuestion(questionId, {
                options: [...question.options, newOption],
            });
        }
    };

    const updateOption = (questionId: string, optionId: string, text: string) => {
        const question = questions.find((q) => q.id === questionId);
        if (question) {
            updateQuestion(questionId, {
                options: question.options.map((opt) =>
                    opt.id === optionId ? { ...opt, text } : opt
                ),
            });
        }
    };

    const deleteOption = (questionId: string, optionId: string) => {
        const question = questions.find((q) => q.id === questionId);
        if (question && question.options.length > 2) {
            updateQuestion(questionId, {
                options: question.options.filter((opt) => opt.id !== optionId),
            });
        }
    };

    return (
        <div className="space-y-6">
            <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="questions">
                    {(provided) => (
                        <div {...provided.droppableProps} ref={provided.innerRef}>
                            {questions.map((question, index) => (
                                <Draggable
                                    key={question.id}
                                    draggableId={question.id}
                                    index={index}
                                >
                                    {(provided, snapshot) => (
                                        <div
                                            ref={provided.innerRef}
                                            {...provided.draggableProps}
                                            className={`bg-white border-2 rounded-xl p-6 mb-4 transition-all ${
                                                snapshot.isDragging
                                                    ? "shadow-lg border-blue-300"
                                                    : editingQuestionId === question.id
                                                    ? "border-blue-500"
                                                    : "border-gray-200"
                                            }`}
                                        >
                                            <div className="flex items-start gap-4">
                                                <div
                                                    {...provided.dragHandleProps}
                                                    className="pt-1 cursor-move"
                                                >
                                                    <svg
                                                        className="w-5 h-5 text-gray-400"
                                                        fill="none"
                                                        stroke="currentColor"
                                                        viewBox="0 0 24 24"
                                                    >
                                                        <path
                                                            strokeLinecap="round"
                                                            strokeLinejoin="round"
                                                            strokeWidth={2}
                                                            d="M4 8h16M4 16h16"
                                                        />
                                                    </svg>
                                                </div>

                                                <div className="flex-1">
                                                    <div className="flex items-center gap-2 mb-4">
                                                        <span className="text-sm font-medium text-gray-500">
                                                            Question {index + 1}
                                                        </span>
                                                        <span className="text-lg">
                                                            {
                                                                questionTypes.find(
                                                                    (t) => t.value === question.type
                                                                )?.icon
                                                            }
                                                        </span>
                                                        <span className="text-sm text-gray-600">
                                                            {
                                                                questionTypes.find(
                                                                    (t) => t.value === question.type
                                                                )?.label
                                                            }
                                                        </span>
                                                    </div>

                                                    {editingQuestionId === question.id ? (
                                                        <div className="space-y-4">
                                                            <input
                                                                type="text"
                                                                value={question.text}
                                                                onChange={(e) =>
                                                                    updateQuestion(question.id, {
                                                                        text: e.target.value,
                                                                    })
                                                                }
                                                                placeholder="Entrez votre question..."
                                                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                                autoFocus
                                                            />

                                                            {/* Options for choice questions */}
                                                            {(question.type === "single_choice" ||
                                                                question.type === "multiple_choice" ||
                                                                question.type === "ranking") && (
                                                                <div className="space-y-2">
                                                                    <p className="text-sm font-medium text-gray-700">
                                                                        Options :
                                                                    </p>
                                                                    {question.options.map(
                                                                        (option, optIndex) => (
                                                                            <div
                                                                                key={option.id}
                                                                                className="flex items-center gap-2"
                                                                            >
                                                                                <span className="text-gray-400">
                                                                                    {question.type ===
                                                                                    "single_choice"
                                                                                        ? "○"
                                                                                        : question.type ===
                                                                                          "multiple_choice"
                                                                                        ? "☐"
                                                                                        : `${
                                                                                              optIndex + 1
                                                                                          }.`}
                                                                                </span>
                                                                                <input
                                                                                    type="text"
                                                                                    value={option.text}
                                                                                    onChange={(e) =>
                                                                                        updateOption(
                                                                                            question.id,
                                                                                            option.id,
                                                                                            e.target.value
                                                                                        )
                                                                                    }
                                                                                    placeholder={`Option ${
                                                                                        optIndex + 1
                                                                                    }`}
                                                                                    className="flex-1 px-3 py-1.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                                                />
                                                                                <button
                                                                                    onClick={() =>
                                                                                        deleteOption(
                                                                                            question.id,
                                                                                            option.id
                                                                                        )
                                                                                    }
                                                                                    disabled={
                                                                                        question.options
                                                                                            .length <= 2
                                                                                    }
                                                                                    className="p-1.5 text-red-500 hover:bg-red-50 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                                                                                >
                                                                                    <svg
                                                                                        className="w-4 h-4"
                                                                                        fill="none"
                                                                                        stroke="currentColor"
                                                                                        viewBox="0 0 24 24"
                                                                                    >
                                                                                        <path
                                                                                            strokeLinecap="round"
                                                                                            strokeLinejoin="round"
                                                                                            strokeWidth={2}
                                                                                            d="M6 18L18 6M6 6l12 12"
                                                                                        />
                                                                                    </svg>
                                                                                </button>
                                                                            </div>
                                                                        )
                                                                    )}
                                                                    <button
                                                                        onClick={() =>
                                                                            addOption(question.id)
                                                                        }
                                                                        className="text-sm text-blue-600 hover:text-blue-700"
                                                                    >
                                                                        + Ajouter une option
                                                                    </button>
                                                                </div>
                                                            )}

                                                            {/* Rating scale */}
                                                            {question.type === "rating" && (
                                                                <div className="flex items-center gap-4">
                                                                    <label className="text-sm text-gray-700">
                                                                        Échelle :
                                                                    </label>
                                                                    <input
                                                                        type="number"
                                                                        value={question.ratingScale?.min}
                                                                        onChange={(e) =>
                                                                            updateQuestion(question.id, {
                                                                                ratingScale: {
                                                                                    ...question.ratingScale!,
                                                                                    min: parseInt(
                                                                                        e.target.value
                                                                                    ),
                                                                                },
                                                                            })
                                                                        }
                                                                        className="w-16 px-2 py-1 border border-gray-300 rounded"
                                                                    />
                                                                    <span className="text-gray-500">à</span>
                                                                    <input
                                                                        type="number"
                                                                        value={question.ratingScale?.max}
                                                                        onChange={(e) =>
                                                                            updateQuestion(question.id, {
                                                                                ratingScale: {
                                                                                    ...question.ratingScale!,
                                                                                    max: parseInt(
                                                                                        e.target.value
                                                                                    ),
                                                                                },
                                                                            })
                                                                        }
                                                                        className="w-16 px-2 py-1 border border-gray-300 rounded"
                                                                    />
                                                                </div>
                                                            )}

                                                            <div className="flex items-center justify-between pt-2">
                                                                <label className="flex items-center">
                                                                    <input
                                                                        type="checkbox"
                                                                        checked={question.required}
                                                                        onChange={(e) =>
                                                                            updateQuestion(question.id, {
                                                                                required: e.target.checked,
                                                                            })
                                                                        }
                                                                        className="w-4 h-4 text-blue-600 rounded"
                                                                    />
                                                                    <span className="ml-2 text-sm text-gray-700">
                                                                        Réponse obligatoire
                                                                    </span>
                                                                </label>

                                                                <button
                                                                    onClick={() =>
                                                                        setEditingQuestionId(null)
                                                                    }
                                                                    className="text-sm text-blue-600 hover:text-blue-700"
                                                                >
                                                                    Terminer
                                                                </button>
                                                            </div>
                                                        </div>
                                                    ) : (
                                                        <div
                                                            onClick={() =>
                                                                setEditingQuestionId(question.id)
                                                            }
                                                            className="cursor-pointer"
                                                        >
                                                            <p className="font-medium text-gray-900">
                                                                {question.text || "Cliquez pour éditer"}
                                                            </p>
                                                            {question.options.length > 0 && (
                                                                <div className="mt-2 space-y-1">
                                                                    {question.options.map((option) => (
                                                                        <p
                                                                            key={option.id}
                                                                            className="text-sm text-gray-600"
                                                                        >
                                                                            • {option.text}
                                                                        </p>
                                                                    ))}
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                </div>

                                                <button
                                                    onClick={() => deleteQuestion(question.id)}
                                                    className="p-2 text-red-500 hover:bg-red-50 rounded-lg"
                                                >
                                                    <svg
                                                        className="w-5 h-5"
                                                        fill="none"
                                                        stroke="currentColor"
                                                        viewBox="0 0 24 24"
                                                    >
                                                        <path
                                                            strokeLinecap="round"
                                                            strokeLinejoin="round"
                                                            strokeWidth={2}
                                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                                        />
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    )}
                                </Draggable>
                            ))}
                            {provided.placeholder}
                        </div>
                    )}
                </Droppable>
            </DragDropContext>

            {/* Add Question Button */}
            {!isAddingQuestion ? (
                <button
                    onClick={() => setIsAddingQuestion(true)}
                    className="w-full py-4 border-2 border-dashed border-gray-300 rounded-xl text-gray-600 hover:border-gray-400 hover:text-gray-700 transition-all"
                >
                    <span className="text-lg mr-2">+</span>
                    Ajouter une question
                </button>
            ) : (
                <div className="bg-gray-50 rounded-xl p-6">
                    <p className="font-medium text-gray-900 mb-4">
                        Choisissez le type de question :
                    </p>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                        {questionTypes.map((type) => (
                            <button
                                key={type.value}
                                onClick={() => addQuestion(type.value as Question["type"])}
                                className="flex flex-col items-center p-4 bg-white rounded-lg border-2 border-gray-200 hover:border-blue-500 hover:bg-blue-50 transition-all"
                            >
                                <span className="text-2xl mb-2">{type.icon}</span>
                                <span className="text-sm font-medium text-gray-700">
                                    {type.label}
                                </span>
                            </button>
                        ))}
                    </div>
                    <button
                        onClick={() => setIsAddingQuestion(false)}
                        className="mt-4 text-sm text-gray-600 hover:text-gray-700"
                    >
                        Annuler
                    </button>
                </div>
            )}
        </div>
    );
}