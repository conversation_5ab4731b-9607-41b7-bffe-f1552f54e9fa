interface StatsProps {
    stats: {
        totalPolls: number;
        activePolls: number;
        totalParticipations: number;
        avgParticipationRate: string;
    };
}

export default function AdminDashboardStats({ stats }: StatsProps) {
    const statCards = [
        {
            title: "Total des sondages",
            value: stats.totalPolls,
            icon: (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                    />
                </svg>
            ),
            bgColor: "bg-blue-500",
            change: "+12%",
            changeType: "increase",
        },
        {
            title: "Sondages actifs",
            value: stats.activePolls,
            icon: (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                    />
                </svg>
            ),
            bgColor: "bg-green-500",
            change: "+2",
            changeType: "increase",
        },
        {
            title: "Participations totales",
            value: stats.totalParticipations,
            icon: (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                </svg>
            ),
            bgColor: "bg-purple-500",
            change: "+25%",
            changeType: "increase",
        },
        {
            title: "Taux de participation",
            value: `${stats.avgParticipationRate}%`,
            icon: (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                </svg>
            ),
            bgColor: "bg-orange-500",
            change: "-3%",
            changeType: "decrease",
        },
    ];

    return (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {statCards.map((stat, index) => (
                <div
                    key={index}
                    className="bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow"
                >
                    <div className="flex items-center justify-between mb-4">
                        <div
                            className={`w-12 h-12 ${stat.bgColor} bg-opacity-20 rounded-xl flex items-center justify-center`}
                        >
                            <span className={`${stat.bgColor.replace("bg-", "text-")}`}>
                                {stat.icon}
                            </span>
                        </div>
                        <span
                            className={`text-sm font-medium ${
                                stat.changeType === "increase"
                                    ? "text-green-600"
                                    : "text-red-600"
                            }`}
                        >
                            {stat.change}
                        </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">{stat.value}</h3>
                    <p className="text-sm text-gray-600 mt-1">{stat.title}</p>
                </div>
            ))}
        </div>
    );
}