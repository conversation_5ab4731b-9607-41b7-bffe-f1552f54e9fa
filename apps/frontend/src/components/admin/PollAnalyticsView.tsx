"use client";

import { Poll, PollAnalytics } from "@/types";
import {
    <PERSON><PERSON><PERSON>,
    Bar,
    <PERSON>Chart,
    Line,
    PieChart,
    Pie,
    Cell,
    XAxis,
    YAxis,
    CartesianGrid,
    <PERSON>ltip,
    Legend,
    ResponsiveContainer,
} from "recharts";

interface PollAnalyticsViewProps {
    poll: Poll;
    analytics: PollAnalytics;
}

const COLORS = ["#2563eb", "#0ea5e9", "#06b6d4", "#14b8a6", "#10b981", "#84cc16", "#eab308", "#f97316"];

export default function PollAnalyticsView({ poll, analytics }: PollAnalyticsViewProps) {
    // Key Metrics Cards
    const metrics = [
        {
            title: "Participants total",
            value: analytics.statistics.totalParticipants,
            subtitle: `sur ${analytics.statistics.targetPopulation.toLocaleString()} habitants`,
            icon: "👥",
            color: "bg-blue-500",
        },
        {
            title: "Taux de participation",
            value: `${analytics.statistics.participationRate}%`,
            subtitle: "de la population cible",
            icon: "📊",
            color: "bg-green-500",
        },
        {
            title: "Temps moyen",
            value: `${analytics.statistics.averageCompletionTime} min`,
            subtitle: "pour compléter",
            icon: "⏱️",
            color: "bg-purple-500",
        },
        {
            title: "Taux de complétion",
            value: `${analytics.statistics.completionRate}%`,
            subtitle: "des participants",
            icon: "✅",
            color: "bg-orange-500",
        },
    ];

    const renderQuestionChart = (question: any) => {
        const { type, data, questionText, questionDocumentId } = question;

        if (type === "single_choice" || type === "multiple_choice") {
            return (
                <div key={questionDocumentId} className="bg-white rounded-xl p-6 shadow-sm">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">{questionText}</h3>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <ResponsiveContainer width="100%" height={300}>
                            <PieChart>
                                <Pie
                                    data={data}
                                    cx="50%"
                                    cy="50%"
                                    labelLine={false}
                                    label={({ percent }) => percent ? `${(percent * 100).toFixed(0)}%` : ''}
                                    outerRadius={100}
                                    fill="#8884d8"
                                    dataKey="count"
                                    nameKey="label"
                                >
                                    {data.map((entry: any, index: number) => (
                                        <Cell
                                            key={`cell-${index}`}
                                            fill={COLORS[index % COLORS.length]}
                                        />
                                    ))}
                                </Pie>
                                <Tooltip />
                                <Legend />
                            </PieChart>
                        </ResponsiveContainer>
                        <div className="space-y-3">
                            {data.map((item: any, index: number) => (
                                <div key={index} className="flex items-center justify-between">
                                    <div className="flex items-center gap-3">
                                        <div
                                            className="w-4 h-4 rounded"
                                            style={{ backgroundColor: COLORS[index % COLORS.length] }}
                                        />
                                        <span className="text-sm text-gray-700">{item.label}</span>
                                    </div>
                                    <div className="text-sm font-medium text-gray-900">
                                        {item.count} ({item.percentage}%)
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            );
        }

        if (type === "rating") {
            return (
                <div key={questionDocumentId} className="bg-white rounded-xl p-6 shadow-sm">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">{questionText}</h3>
                    <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={data}>
                            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                            <XAxis dataKey="rating" />
                            <YAxis />
                            <Tooltip />
                            <Bar dataKey="count" fill="#2563eb" radius={[8, 8, 0, 0]} />
                        </BarChart>
                    </ResponsiveContainer>
                </div>
            );
        }

        if (type === "text") {
            return (
                <div key={questionDocumentId} className="bg-white rounded-xl p-6 shadow-sm">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">{questionText}</h3>
                    <div className="space-y-3 max-h-96 overflow-y-auto">
                        {data.slice(0, 10).map((response: any, index: number) => (
                            <div key={index} className="p-3 bg-gray-50 rounded-lg">
                                <p className="text-sm text-gray-700">{response.text}</p>
                                <p className="text-xs text-gray-500 mt-1">
                                    {new Date(response.date).toLocaleDateString("fr-FR")}
                                </p>
                            </div>
                        ))}
                        {data.length > 10 && (
                            <p className="text-sm text-gray-500 text-center py-2">
                                Et {data.length - 10} autres réponses...
                            </p>
                        )}
                    </div>
                </div>
            );
        }

        return null;
    };

    return (
        <div className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                {metrics.map((metric, index) => (
                    <div key={index} className="bg-white rounded-xl p-6 shadow-sm">
                        <div className="flex items-center justify-between mb-4">
                            <div
                                className={`w-12 h-12 ${metric.color} bg-opacity-20 rounded-xl flex items-center justify-center`}
                            >
                                <span className="text-2xl">{metric.icon}</span>
                            </div>
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900">{metric.value}</h3>
                        <p className="text-sm text-gray-600 mt-1">{metric.title}</p>
                        <p className="text-xs text-gray-500">{metric.subtitle}</p>
                    </div>
                ))}
            </div>

            {/* Participation Timeline */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-6">
                    Évolution des participations
                </h3>
                <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={analytics.temporal}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip />
                        <Line
                            type="monotone"
                            dataKey="daily_participations"
                            stroke="#2563eb"
                            strokeWidth={3}
                            dot={{ fill: "#2563eb" }}
                            name="Participations"
                        />
                    </LineChart>
                </ResponsiveContainer>
            </div>

            {/* Geographic Distribution */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-6">
                    Répartition géographique
                </h3>
                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Zone
                                </th>
                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Type
                                </th>
                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Participants
                                </th>
                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Pourcentage
                                </th>
                            </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                            {analytics.geographic.distribution.map((zone, index) => (
                                <tr key={index}>
                                    <td className="px-4 py-3 text-sm text-gray-900">
                                        {zone.zone_name}
                                    </td>
                                    <td className="px-4 py-3 text-sm text-gray-500">
                                        {zone.zone_type === "city"
                                            ? "Ville"
                                            : zone.zone_type === "department"
                                            ? "Département"
                                            : zone.zone_type === "region"
                                            ? "Région"
                                            : zone.zone_type}
                                    </td>
                                    <td className="px-4 py-3 text-sm text-gray-900">
                                        {zone.participants}
                                    </td>
                                    <td className="px-4 py-3 text-sm text-gray-900">
                                        {(
                                            (zone.participants /
                                                analytics.statistics.totalParticipants) *
                                            100
                                        ).toFixed(1)}
                                        %
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Question Results */}
            <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Résultats par question</h2>
                {analytics.responses.map((question) => renderQuestionChart(question))}
            </div>
        </div>
    );
}