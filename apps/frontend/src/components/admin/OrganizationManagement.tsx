"use client";

import { useState } from "react";
import { Organization, GeographicZone, User } from "@/types";

interface OrganizationManagementProps {
    organization?: Organization;
    geographicZones: GeographicZone[];
    currentUser: User;
}

export default function OrganizationManagement({
    organization,
    geographicZones,
    currentUser,
}: OrganizationManagementProps) {
    const [isEditing, setIsEditing] = useState(!organization);
    const [formData, setFormData] = useState({
        name: organization?.name || "",
        type: organization?.type || "municipality",
        description: organization?.description || "",
        contactEmail: organization?.contactEmail || "",
        geographicZoneId: organization?.geographicZone?.documentId || "",
    });

    const organizationTypes = [
        { value: "municipality", label: "Municipalité", icon: "🏛️" },
        { value: "department", label: "Département", icon: "🗺️" },
        { value: "region", label: "Région", icon: "🌍" },
        { value: "association", label: "Association", icon: "🤝" },
        { value: "public_service", label: "Service public", icon: "🏢" },
    ];

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        // TODO: Call server action to create/update organization
        console.log("Saving organization:", formData);
        setIsEditing(false);
    };

    if (!organization && !isEditing) {
        return (
            <div className="bg-white rounded-2xl p-8 shadow-sm text-center">
                <svg
                    className="mx-auto h-12 w-12 text-gray-400 mb-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                    />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Aucune organisation configurée
                </h3>
                <p className="text-gray-600 mb-4">
                    Créez votre organisation pour commencer à gérer des sondages.
                </p>
                <button
                    onClick={() => setIsEditing(true)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-all"
                >
                    Créer une organisation
                </button>
            </div>
        );
    }

    return (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Organization Info */}
            <div className="lg:col-span-2">
                <div className="bg-white rounded-2xl shadow-sm">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <div className="flex items-center justify-between">
                            <h2 className="text-lg font-semibold text-gray-900">
                                Informations de l'organisation
                            </h2>
                            {!isEditing && (
                                <button
                                    onClick={() => setIsEditing(true)}
                                    className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                                >
                                    Modifier
                                </button>
                            )}
                        </div>
                    </div>

                    <div className="p-6">
                        {isEditing ? (
                            <form onSubmit={handleSubmit} className="space-y-6">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Nom de l'organisation
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.name}
                                        onChange={(e) =>
                                            setFormData({ ...formData, name: e.target.value })
                                        }
                                        className="w-full px-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        required
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Type d'organisation
                                    </label>
                                    <div className="grid grid-cols-2 gap-3">
                                        {organizationTypes.map((type) => (
                                            <label
                                                key={type.value}
                                                className={`flex items-center p-3 rounded-xl border-2 cursor-pointer transition-all ${
                                                    formData.type === type.value
                                                        ? "border-blue-500 bg-blue-50"
                                                        : "border-gray-200 hover:border-gray-300"
                                                }`}
                                            >
                                                <input
                                                    type="radio"
                                                    name="type"
                                                    value={type.value}
                                                    checked={formData.type === type.value}
                                                    onChange={(e) =>
                                                        setFormData({
                                                            ...formData,
                                                            type: e.target.value as any,
                                                        })
                                                    }
                                                    className="sr-only"
                                                />
                                                <span className="text-2xl mr-3">{type.icon}</span>
                                                <span className="font-medium">{type.label}</span>
                                            </label>
                                        ))}
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Description
                                    </label>
                                    <textarea
                                        value={formData.description}
                                        onChange={(e) =>
                                            setFormData({
                                                ...formData,
                                                description: e.target.value,
                                            })
                                        }
                                        rows={3}
                                        className="w-full px-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Email de contact
                                    </label>
                                    <input
                                        type="email"
                                        value={formData.contactEmail}
                                        onChange={(e) =>
                                            setFormData({
                                                ...formData,
                                                contactEmail: e.target.value,
                                            })
                                        }
                                        className="w-full px-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        required
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Zone géographique
                                    </label>
                                    <select
                                        value={formData.geographicZoneId}
                                        onChange={(e) =>
                                            setFormData({
                                                ...formData,
                                                geographicZoneId: e.target.value,
                                            })
                                        }
                                        className="w-full px-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        required
                                    >
                                        <option value="">Sélectionnez une zone</option>
                                        {geographicZones.map((zone) => (
                                            <option key={zone.documentId} value={zone.documentId}>
                                                {zone.name} ({zone.type})
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div className="flex gap-3">
                                    <button
                                        type="submit"
                                        className="px-4 py-2 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-all"
                                    >
                                        Enregistrer
                                    </button>
                                    {organization && (
                                        <button
                                            type="button"
                                            onClick={() => setIsEditing(false)}
                                            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-xl font-medium hover:bg-gray-300 transition-all"
                                        >
                                            Annuler
                                        </button>
                                    )}
                                </div>
                            </form>
                        ) : (
                            <dl className="space-y-4">
                                <div>
                                    <dt className="text-sm font-medium text-gray-500">Nom</dt>
                                    <dd className="mt-1 text-lg text-gray-900">
                                        {organization?.name}
                                    </dd>
                                </div>
                                <div>
                                    <dt className="text-sm font-medium text-gray-500">Type</dt>
                                    <dd className="mt-1 flex items-center gap-2">
                                        <span className="text-lg">
                                            {
                                                organizationTypes.find(
                                                    (t) => t.value === organization?.type
                                                )?.icon
                                            }
                                        </span>
                                        <span className="text-gray-900">
                                            {
                                                organizationTypes.find(
                                                    (t) => t.value === organization?.type
                                                )?.label
                                            }
                                        </span>
                                    </dd>
                                </div>
                                {organization?.description && (
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">
                                            Description
                                        </dt>
                                        <dd className="mt-1 text-gray-900">
                                            {organization.description}
                                        </dd>
                                    </div>
                                )}
                                <div>
                                    <dt className="text-sm font-medium text-gray-500">
                                        Email de contact
                                    </dt>
                                    <dd className="mt-1 text-gray-900">
                                        {organization?.contactEmail}
                                    </dd>
                                </div>
                                <div>
                                    <dt className="text-sm font-medium text-gray-500">
                                        Zone géographique
                                    </dt>
                                    <dd className="mt-1 text-gray-900">
                                        {organization?.geographicZone?.name} (
                                        {organization?.geographicZone?.type})
                                    </dd>
                                </div>
                            </dl>
                        )}
                    </div>
                </div>

                {/* Administrators */}
                {organization && (
                    <div className="bg-white rounded-2xl shadow-sm mt-6">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center justify-between">
                                <h2 className="text-lg font-semibold text-gray-900">
                                    Administrateurs
                                </h2>
                                <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
                                    Ajouter
                                </button>
                            </div>
                        </div>
                        <div className="p-6">
                            <div className="space-y-3">
                                {organization.administrators?.map((admin) => (
                                    <div
                                        key={admin.documentId}
                                        className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                                    >
                                        <div className="flex items-center gap-3">
                                            <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium">
                                                {admin.username.charAt(0).toUpperCase()}
                                            </div>
                                            <div>
                                                <p className="font-medium text-gray-900">
                                                    {admin.username}
                                                </p>
                                                <p className="text-sm text-gray-500">
                                                    {admin.email}
                                                </p>
                                            </div>
                                        </div>
                                        {admin.documentId !== currentUser.documentId && (
                                            <button className="text-sm text-red-600 hover:text-red-700">
                                                Retirer
                                            </button>
                                        )}
                                    </div>
                                ))}
                                {(!organization.administrators ||
                                    organization.administrators.length === 0) && (
                                    <p className="text-sm text-gray-500 text-center py-4">
                                        Aucun administrateur configuré
                                    </p>
                                )}
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Stats Sidebar */}
            <div className="space-y-6">
                <div className="bg-white rounded-2xl p-6 shadow-sm">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Statistiques</h3>
                    <dl className="space-y-4">
                        <div>
                            <dt className="text-sm text-gray-500">Sondages créés</dt>
                            <dd className="text-2xl font-bold text-gray-900">
                                {organization?.polls?.length || 0}
                            </dd>
                        </div>
                        <div>
                            <dt className="text-sm text-gray-500">Participants total</dt>
                            <dd className="text-2xl font-bold text-gray-900">
                                {organization?.polls?.reduce(
                                    (acc, poll) => acc + (poll.participations?.length || 0),
                                    0
                                ) || 0}
                            </dd>
                        </div>
                        <div>
                            <dt className="text-sm text-gray-500">Statut</dt>
                            <dd className="mt-1">
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <span className="w-2 h-2 bg-green-500 rounded-full mr-1.5"></span>
                                    Active
                                </span>
                            </dd>
                        </div>
                    </dl>
                </div>

                {organization?.logo && (
                    <div className="bg-white rounded-2xl p-6 shadow-sm">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Logo</h3>
                        <img
                            src={organization.logo.url}
                            alt={organization.name}
                            className="w-full h-auto rounded-lg"
                        />
                        <button className="mt-4 w-full text-sm text-blue-600 hover:text-blue-700 font-medium">
                            Changer le logo
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
}