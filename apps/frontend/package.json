{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.1.1", "@tanstack/react-query": "^5.81.5", "axios": "^1.10.0", "clsx": "^2.1.1", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "lucide-react": "^0.525.0", "next": "15.3.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-leaflet": "^5.0.0", "recharts": "^3.0.2", "tailwind-merge": "^3.3.1", "zod": "^3.25.75"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/leaflet": "^1.9.19", "@types/node": "^20.19.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tailwindcss": "^4.1.11", "ts-jest": "^29.4.0", "typescript": "^5.8.3"}}