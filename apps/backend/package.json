{"name": "backend", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "build:plugins": "./build-plugins.sh", "build:plugins:clean": "./build-plugins-advanced.sh --clean", "build:plugins:watch": "./build-plugins-advanced.sh --watch", "build:plugins:parallel": "./build-plugins-advanced.sh --parallel", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry", "import:geographic-data": "pnpm build && NODE_ENV=development node -r ts-node/register src/scripts/run-import.ts"}, "dependencies": {"@babel/runtime": "^7.27.6", "@codemirror/autocomplete": "^6.18.6", "@codemirror/language": "^6.11.1", "@codemirror/lint": "^6.8.5", "@codemirror/search": "^6.5.11", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.3", "@codemirror/view": "^6.37.2", "@strapi/plugin-cloud": "5.16.1", "@strapi/plugin-users-permissions": "5.16.1", "@strapi/provider-email-sendgrid": "^5.16.1", "@strapi/strapi": "5.16.1", "axios": "^1.10.0", "codemirror": "^6.0.2", "grant": "^5.4.24", "knex-postgis": "^0.14.3", "passport-oauth2": "^1.8.0", "pg": "^8.16.2", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.0.0", "zod": "^3.25.67"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20", "@types/pg": "^8.15.4", "@types/react": "^18", "@types/react-dom": "^18", "@types/supertest": "^6.0.0", "jest": "^29.5.0", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-node": "^10.9.2", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "4a6911a7a13dd95b0e0a25d1146ff21e9ce88c766124f1fc1c4ca6cdf08158c3"}}