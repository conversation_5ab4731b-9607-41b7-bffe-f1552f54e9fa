#!/bin/bash

# Build script for all Strapi plugins
# This script will run pnpm build in each plugin directory

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PLUGINS_DIR="${SCRIPT_DIR}/src/plugins"

echo -e "${YELLOW}Starting plugin build process...${NC}"
echo "Plugins directory: ${PLUGINS_DIR}"

# Check if plugins directory exists
if [ ! -d "$PLUGINS_DIR" ]; then
    echo -e "${RED}Error: Plugins directory not found at ${PLUGINS_DIR}${NC}"
    exit 1
fi

# Counter for statistics
TOTAL_PLUGINS=0
SUCCESSFUL_BUILDS=0
FAILED_BUILDS=0

# Find all plugin directories
for plugin_dir in "$PLUGINS_DIR"/*; do
    if [ -d "$plugin_dir" ]; then
        plugin_name=$(basename "$plugin_dir")
        
        # Check if package.json exists
        if [ -f "$plugin_dir/package.json" ]; then
            TOTAL_PLUGINS=$((TOTAL_PLUGINS + 1))
            
            echo -e "\n${YELLOW}Building plugin: ${plugin_name}${NC}"
            echo "Path: $plugin_dir"
            
            # Change to plugin directory
            cd "$plugin_dir"
            
            # Check if build script exists in package.json
            if grep -q '"build"' package.json; then
                # Run pnpm build
                if pnpm build; then
                    echo -e "${GREEN}✓ Successfully built ${plugin_name}${NC}"
                    SUCCESSFUL_BUILDS=$((SUCCESSFUL_BUILDS + 1))
                else
                    echo -e "${RED}✗ Failed to build ${plugin_name}${NC}"
                    FAILED_BUILDS=$((FAILED_BUILDS + 1))
                fi
            else
                echo -e "${YELLOW}⚠ No build script found for ${plugin_name}, skipping...${NC}"
            fi
            
            # Return to plugins directory
            cd "$PLUGINS_DIR"
        else
            echo -e "${YELLOW}Skipping ${plugin_name} - no package.json found${NC}"
        fi
    fi
done

# Summary
echo -e "\n${YELLOW}================================${NC}"
echo -e "${YELLOW}Build Summary:${NC}"
echo -e "${YELLOW}================================${NC}"
echo -e "Total plugins found: ${TOTAL_PLUGINS}"
echo -e "${GREEN}Successful builds: ${SUCCESSFUL_BUILDS}${NC}"
echo -e "${RED}Failed builds: ${FAILED_BUILDS}${NC}"

# Exit with error if any builds failed
if [ $FAILED_BUILDS -gt 0 ]; then
    echo -e "\n${RED}Some plugins failed to build!${NC}"
    exit 1
else
    echo -e "\n${GREEN}All plugins built successfully!${NC}"
    exit 0
fi