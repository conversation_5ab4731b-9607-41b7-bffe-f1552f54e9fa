# CivicPoll Backend Plugins

This directory contains custom Strapi plugins developed for the CivicPoll platform.

## Available Plugins

### 1. **audit-log**

Comprehensive audit logging system for tracking all system events and user actions.

**Features:**

- Tracks authentication events (login/logout)
- Records poll lifecycle events (creation, updates, participation)
- Monitors GDPR operations
- Configurable retention policy (180 days)
- Analytics and reporting capabilities

### 2. **gdpr**

GDPR compliance plugin for managing user data privacy and rights.

**Features:**

- User data export functionality
- Right to deletion implementation
- Data anonymization support
- Consent tracking
- Configurable retention policies (2 years for poll data)

## Building Plugins

We provide several scripts to build the plugins:

### Basic Build

```bash
pnpm build:plugins
```

### Clean Build

Remove all build artifacts and rebuild:

```bash
pnpm build:plugins:clean
```

### Watch Mode

Build plugins and watch for changes:

```bash
pnpm build:plugins:watch
```

### Parallel Build

Build all plugins in parallel for faster compilation:

```bash
pnpm build:plugins:parallel
```

### Advanced Options

Use the advanced build script directly:

```bash
./build-plugins-advanced.sh --clean --parallel --jobs 8
```

## Plugin Structure

Each plugin follows the standard Strapi plugin structure:

```
plugin-name/
├── admin/          # Admin panel UI components
├── server/         # Backend logic
│   ├── src/
│   │   ├── bootstrap.ts    # Plugin initialization
│   │   ├── config/         # Plugin configuration
│   │   ├── content-types/  # Custom content types
│   │   ├── controllers/    # API controllers
│   │   ├── services/       # Business logic
│   │   ├── policies/       # Access policies
│   │   └── routes/         # API routes
│   └── index.ts
├── package.json
└── tsconfig.json
```

## Development Guidelines

1. **TypeScript**: All plugins are written in TypeScript
2. **Naming**: Use kebab-case for plugin names
3. **Configuration**: Store settings in `config/index.ts`
4. **Events**: Use Strapi lifecycle hooks for event tracking
5. **Testing**: Add tests in `__tests__` directory

## Plugin Configuration

Plugins are configured in the main Strapi configuration:

```typescript
// config/plugins.ts
export default {
  'audit-log': {
    enabled: true,
    config: {
      retentionDays: 180,
      enabledEvents: ['poll.created', 'poll.updated', ...]
    }
  },
  'gdpr': {
    enabled: true,
    config: {
      dataRetentionDays: 730,
      anonymizeOnDelete: true
    }
  }
}
```

## Troubleshooting

### Build Errors

1. Ensure all dependencies are installed: `pnpm install`
2. Check TypeScript configuration in `tsconfig.json`
3. Verify Strapi version compatibility

### Plugin Not Loading

1. Check if plugin is enabled in configuration
2. Verify plugin registration in `config/plugins.ts`
3. Check for bootstrap errors in logs

## Future Plugins

Planned plugins for future phases:

- **notifications**: Email and in-app notifications
- **analytics**: Advanced analytics and reporting
- **scheduler**: Job scheduling for polls
- **export**: Data export in various formats
