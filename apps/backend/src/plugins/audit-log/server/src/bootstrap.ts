export default async ({ strapi }: { strapi: any }) => {
    // Set up lifecycle hooks for content type events
    strapi.db.lifecycles.subscribe({
        models: ["*"],

        async afterCreate(event) {
            const { model, result } = event;
            await strapi
                .plugin("audit-log")
                .service("auditLog")
                .log({
                    event: "content.create",
                    action: "create",
                    targetType: model.uid,
                    targetId: result.id,
                    userId: strapi.requestContext?.get()?.state?.user?.id,
                    metadata: {
                        modelName: model.singularName,
                        data: result,
                    },
                });
        },

        async afterUpdate(event) {
            const { model, result, params } = event;
            await strapi
                .plugin("audit-log")
                .service("auditLog")
                .log({
                    event: "content.update",
                    action: "update",
                    targetType: model.uid,
                    targetId: result.id,
                    userId: strapi.requestContext?.get()?.state?.user?.id,
                    metadata: {
                        modelName: model.singularName,
                        data: result,
                        where: params.where,
                    },
                });
        },

        async afterDelete(event) {
            const { model, result, params } = event;
            await strapi
                .plugin("audit-log")
                .service("auditLog")
                .log({
                    event: "content.delete",
                    action: "delete",
                    targetType: model.uid,
                    targetId: params.where?.id || result?.id,
                    userId: strapi.requestContext?.get()?.state?.user?.id,
                    metadata: {
                        modelName: model.singularName,
                        deletedData: result,
                        where: params.where,
                    },
                });
        },
    });

    // Set up CivicPoll-specific event listeners

    // Listen for poll-related events
    strapi.db.lifecycles.subscribe({
        models: ["api::poll.poll"],
        async afterCreate(event) {
            await strapi
                .plugin("audit-log")
                .service("auditLog")
                .log({
                    event: "poll.created",
                    action: "create",
                    targetType: "api::poll.poll",
                    targetId: event.result.id,
                    userId: strapi.requestContext?.get()?.state?.user?.id,
                    metadata: {
                        title: event.result.title,
                        geographicZone: event.result.geographicZone,
                        organization: event.result.organization,
                    },
                });
        },
        async afterUpdate(event) {
            await strapi
                .plugin("audit-log")
                .service("auditLog")
                .log({
                    event: "poll.updated",
                    action: "update",
                    targetType: "api::poll.poll",
                    targetId: event.result.id,
                    userId: strapi.requestContext?.get()?.state?.user?.id,
                    metadata: {
                        title: event.result.title,
                        status: event.result.status,
                    },
                });
        },
    });

    // Listen for participation events
    strapi.db.lifecycles.subscribe({
        models: ["api::participation.participation"],
        async afterCreate(event) {
            await strapi
                .plugin("audit-log")
                .service("auditLog")
                .log({
                    event: "participation.submitted",
                    action: "create",
                    targetType: "api::participation.participation",
                    targetId: event.result.id,
                    userId: event.result.user,
                    metadata: {
                        pollId: event.result.poll,
                        completed: event.result.completed,
                        userGeographicZone: event.result.userGeographicZone,
                    },
                });
        },
    });

    // Listen for user location validation
    strapi.db.lifecycles.subscribe({
        models: ["api::user-location.user-location"],
        async afterUpdate(event) {
            if (event.result.validated && !event.params.data.validated) {
                await strapi
                    .plugin("audit-log")
                    .service("auditLog")
                    .log({
                        event: "location.validated",
                        action: "update",
                        targetType: "api::user-location.user-location",
                        targetId: event.result.id,
                        userId: event.result.user,
                        metadata: {
                            city: event.result.city,
                            postalCode: event.result.postalCode,
                            geographicZone: event.result.geographicZone,
                        },
                    });
            }
        },
    });

    // Schedule retention policy enforcement
    const runRetentionPolicy = async () => {
        try {
            await strapi.plugin("audit-log").service("auditLog").enforceRetentionPolicy();
        } catch (error) {
            strapi.log.error("Failed to enforce audit log retention policy:", error);
        }
    };

    // Run retention policy daily
    setInterval(runRetentionPolicy, 24 * 60 * 60 * 1000);

    // Run retention policy on startup
    setTimeout(runRetentionPolicy, 5000);
};
