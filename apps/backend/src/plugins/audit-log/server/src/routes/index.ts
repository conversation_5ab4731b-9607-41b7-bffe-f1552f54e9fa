export default [
    {
        method: "GET",
        path: "/logs",
        handler: "auditLog.search",
        config: {
            policies: ["admin::isAuthenticatedAdmin"],
            description: "Search audit logs with filters",
        },
    },
    {
        method: "GET",
        path: "/logs/statistics",
        handler: "auditLog.statistics",
        config: {
            policies: ["admin::isAuthenticatedAdmin"],
            description: "Get audit log statistics",
        },
    },
    {
        method: "GET",
        path: "/logs/export",
        handler: "auditLog.export",
        config: {
            policies: ["admin::isAuthenticatedAdmin"],
            description: "Export audit logs",
        },
    },
    {
        method: "GET",
        path: "/event-types",
        handler: "auditLog.eventTypes",
        config: {
            policies: ["admin::isAuthenticatedAdmin"],
            description: "Get available event types",
        },
    },
    {
        method: "GET",
        path: "/actions",
        handler: "auditLog.actions",
        config: {
            policies: ["admin::isAuthenticatedAdmin"],
            description: "Get available actions",
        },
    },
    {
        method: "POST",
        path: "/retention/enforce",
        handler: "auditLog.enforceRetention",
        config: {
            policies: ["admin::isAuthenticatedAdmin"],
            description: "Manually enforce retention policy",
        },
    },
    {
        method: "GET",
        path: "/config",
        handler: "auditLog.config",
        config: {
            policies: ["admin::isAuthenticatedAdmin"],
            description: "Get plugin configuration",
        },
    },
];
