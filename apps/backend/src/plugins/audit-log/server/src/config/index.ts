export default {
    default: {
        retentionDays: 180, // 6 months for CivicPoll
        enabledEvents: [
            // Authentication events
            "user.login",
            "user.logout",
            // Poll management
            "poll.created",
            "poll.updated",
            "poll.deleted",
            "poll.published",
            "poll.closed",
            // Participation tracking
            "participation.submitted",
            "participation.validated",
            // User location
            "location.validated",
            "location.updated",
            // Organization management
            "organization.created",
            "organization.updated",
            // GDPR operations
            "gdpr.export",
            "gdpr.deletion",
            "gdpr.anonymization",
            "gdpr.consent",
            // System events
            "permission.change",
            "config.change",
        ],
        maxLogsPerPage: 100,
        // CivicPoll-specific settings
        anonymizeUserData: true,
        retainStatistics: true,
    },
    validator: (config: any) => {
        if (config.retentionDays && config.retentionDays < 1) {
            throw new Error("retentionDays must be at least 1");
        }
    },
};
