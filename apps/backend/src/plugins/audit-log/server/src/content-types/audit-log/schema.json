{"kind": "collectionType", "collectionName": "audit_logs", "info": {"singularName": "audit-log", "pluralName": "audit-logs", "displayName": "<PERSON>t Log", "description": "System audit log entries"}, "options": {"draftAndPublish": false}, "pluginOptions": {"content-manager": {"visible": false}, "content-type-builder": {"visible": false}}, "attributes": {"event": {"type": "string", "required": true, "index": true}, "action": {"type": "enumeration", "enum": ["create", "read", "update", "delete", "login", "logout", "export", "anonymize", "other"], "required": true, "index": true}, "targetType": {"type": "string", "required": true, "index": true}, "targetId": {"type": "string", "index": true}, "userId": {"type": "integer", "index": true}, "userEmail": {"type": "string", "index": true}, "ip": {"type": "string"}, "userAgent": {"type": "text"}, "metadata": {"type": "json"}, "timestamp": {"type": "datetime", "required": true, "default": "CURRENT_TIMESTAMP", "index": true}}}