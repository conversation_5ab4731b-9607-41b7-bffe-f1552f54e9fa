declare module "*.svg" {
    import type { StrapiApp } from "@strapi/strapi/admin";

    const content: string;

    export default content;
}

declare module "*.png" {
    const content: string;

    export default content;
}

declare module "*.jpg" {
    const content: string;

    export default content;
}

declare module "*.gif" {
    const content: string;

    export default content;
}

declare module "*.webp" {
    const content: string;

    export default content;
}

declare module "*.bmp" {
    const content: string;

    export default content;
}

interface StrapiApp {
    addMenuLink: (link: object) => void;
    registerPlugin: (plugin: object) => void;
}
