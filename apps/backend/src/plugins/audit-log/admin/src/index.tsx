import pluginId from "./pluginId";
import PluginIcon from "./components/PluginIcon";

const name = "Audit Log";

// Helper function to prefix plugin translations (replaces deprecated helper-plugin)
const prefixPluginTranslations = (
    trad: Record<string, string>,
    pluginId: string,
): Record<string, string> => {
    if (!pluginId) {
        throw new TypeError("pluginId can't be empty");
    }
    return Object.keys(trad).reduce(
        (acc, current) => {
            acc[`${pluginId}.${current}`] = trad[current];
            return acc;
        },
        {} as Record<string, string>,
    );
};

export default {
    register(app: any) {
        app.addMenuLink({
            to: `/plugins/${pluginId}`,
            icon: PluginIcon,
            intlLabel: {
                id: `${pluginId}.plugin.name`,
                defaultMessage: name,
            },
            Component: async () => {
                const component = await import("./pages/App");
                return component;
            },
            permissions: [],
        });

        app.registerPlugin({
            id: pluginId,
            name,
        });
    },

    bootstrap(app: any) {},

    async registerTrads({ locales }: { locales: string[] }) {
        const importedTrads = await Promise.all(
            locales.map((locale) => {
                return import(`./translations/${locale}.json`)
                    .then(({ default: data }) => {
                        return {
                            data: prefixPluginTranslations(data, pluginId),
                            locale,
                        };
                    })
                    .catch(() => {
                        return {
                            data: {},
                            locale,
                        };
                    });
            }),
        );

        return Promise.resolve(importedTrads);
    },
};
