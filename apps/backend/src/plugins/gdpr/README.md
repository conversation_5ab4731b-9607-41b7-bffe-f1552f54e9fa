# GDPR Compliance Plugin for Strapi

This plugin provides GDPR compliance features for CivicPoll including data export, deletion, anonymization, consent management, and audit logging.

## Features

### 1. Data Export

- Export all user data in JSON format
- Includes profile information, content created by user, consents, and activities
- Accessible via: `GET /api/gdpr/export/:userId`

### 2. Data Deletion

- Complete deletion of user data from the system
- Removes polls, votes, comments, consents, and user account
- Accessible via: `DELETE /api/gdpr/delete/:userId`

### 3. Data Anonymization

- Alternative to deletion - anonymizes user data instead
- Replaces personal information with anonymous identifiers
- Blocks the account to prevent further access
- Accessible via: `POST /api/gdpr/anonymize/:userId`

### 4. Consent Management

- Track user consents for different data processing purposes
- Support for multiple consent types (marketing, analytics, etc.)
- Get consents: `GET /api/gdpr/consents/:userId`
- Update consents: `PUT /api/gdpr/consents/:userId`

### 5. Audit Logging

- Tracks all GDPR-related actions
- Records who performed actions and when
- Accessible via: `GET /api/gdpr/audit-logs` (admin only)

## Installation

The plugin is automatically loaded as it's in the `src/plugins` directory.

## Configuration

The plugin initializes with default settings in the bootstrap phase:

- Data retention: 365 days
- Audit log retention: 730 days
- Default consent types: marketing, analytics, essential

## Permissions

The plugin uses Strapi's permission system. Configure permissions for:

- `plugin::gdpr.gdpr.export` - Export user data
- `plugin::gdpr.gdpr.delete` - Delete user data
- `plugin::gdpr.gdpr.anonymize` - Anonymize user data
- `plugin::gdpr.gdpr.consents.read` - View consents
- `plugin::gdpr.gdpr.consents.update` - Update consents
- `plugin::gdpr.gdpr.audit.read` - View audit logs (admin only)

## Usage Example

### Export User Data

```javascript
// GET /api/gdpr/export/123
// Returns all data associated with user ID 123
```

### Update Consents

```javascript
// PUT /api/gdpr/consents/123
{
  "consents": [
    { "type": "marketing", "granted": true },
    { "type": "analytics", "granted": false }
  ]
}
```

### View Audit Logs

```javascript
// GET /api/gdpr/audit-logs?userId=123&page=1&pageSize=20
// Returns paginated audit logs, optionally filtered by user
```

## Security Considerations

- Users can only access their own data (except admins)
- All actions are logged in the audit trail
- Sensitive data like passwords are never exported
- IP addresses and user agents are tracked for consent records

## Extending the Plugin

To add new data types for export/deletion:

1. Update the `exportUserData` method in `services/gdpr.ts`
2. Update the `deleteUserData` method to include new content types
3. Update the `anonymizeUserData` method if needed

To add new consent types:

1. Update the consent enumeration in `content-types/consent/schema.json`
2. Update the default consent types in `bootstrap.ts`
