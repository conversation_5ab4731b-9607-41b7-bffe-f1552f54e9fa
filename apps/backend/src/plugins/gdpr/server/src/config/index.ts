export default {
    default: {
        enabled: true,
        dataRetentionDays: 730, // 2 years for poll data
        anonymizeOnDelete: true,
        enableConsentTracking: true,
        enableDataExport: true,
        enableDataDeletion: true,
        // CivicPoll-specific settings
        anonymizeParticipations: true,
        retainAggregatedData: true,
        excludedContentTypes: [
            "api::geographic-zone.geographic-zone",
            "api::poll-template.poll-template",
            "api::organization.organization",
        ],
        sensitiveFields: {
            "api::user-location.user-location": ["address", "latitude", "longitude"],
            "api::participation.participation": ["ipAddress", "userAgent"],
            "plugin::users-permissions.user": ["email", "username"],
        },
    },
    validator: (config: any) => {
        if (config.dataRetentionDays && config.dataRetentionDays < 1) {
            throw new Error("dataRetentionDays must be at least 1");
        }
    },
};
