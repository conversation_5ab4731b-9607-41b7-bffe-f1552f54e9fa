{"kind": "collectionType", "collectionName": "gdpr_requests", "info": {"singularName": "gdpr-request", "pluralName": "gdpr-requests", "displayName": "GDPR Request", "description": "Track GDPR requests for async processing"}, "options": {"draftAndPublish": false, "comment": ""}, "pluginOptions": {"content-manager": {"visible": true}, "content-type-builder": {"visible": false}}, "attributes": {"type": {"type": "enumeration", "enum": ["export", "delete", "anonymize"], "required": true}, "status": {"type": "enumeration", "enum": ["pending", "processing", "completed", "failed"], "default": "pending", "required": true}, "userId": {"type": "string", "required": true}, "requestedBy": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}, "result": {"type": "json"}, "error": {"type": "text"}, "metadata": {"type": "json"}, "completedAt": {"type": "datetime"}}}