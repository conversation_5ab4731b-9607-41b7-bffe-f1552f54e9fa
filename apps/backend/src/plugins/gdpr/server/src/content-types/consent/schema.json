{"kind": "collectionType", "collectionName": "gdpr_consents", "info": {"singularName": "consent", "pluralName": "consents", "displayName": "GDPR Consent", "description": "User consent records for GDPR compliance"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "required": true}, "consentType": {"type": "enumeration", "enum": ["marketing", "analytics", "essential", "performance", "functionality", "targeting", "social_media"], "required": true}, "granted": {"type": "boolean", "required": true, "default": false}, "version": {"type": "string", "required": true, "default": "1.0"}, "ipAddress": {"type": "string"}, "userAgent": {"type": "string"}, "consentMethod": {"type": "enumeration", "enum": ["website_form", "mobile_app", "api", "manual", "imported"], "default": "website_form"}, "withdrawnAt": {"type": "datetime"}}}