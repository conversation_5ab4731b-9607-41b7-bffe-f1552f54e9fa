export default async ({ strapi }: { strapi: any }) => {
    // Initialize GDPR plugin
    strapi.log.info("GDPR plugin initialized");

    // Set up any initial configurations
    const pluginStore = strapi.store({
        type: "plugin",
        name: "gdpr",
    });

    // Check if initial settings exist, if not create them
    const settings = await pluginStore.get({ key: "settings" });

    if (!settings) {
        await pluginStore.set({
            key: "settings",
            value: {
                dataRetentionDays: 365,
                anonymizationEnabled: true,
                auditLogRetentionDays: 730,
                consentTypes: [
                    { key: "marketing", label: "Marketing Communications", required: false },
                    { key: "analytics", label: "Analytics & Performance", required: false },
                    { key: "essential", label: "Essential Cookies", required: true },
                ],
            },
        });
    }
};
