export default (policyContext: any) => {
    const { userId } = policyContext.params;
    const requestingUser = policyContext.state.user;

    if (!requestingUser) {
        return false;
    }

    // Check if user is admin (handle both admin panel and API users)
    if (requestingUser.isAdmin || requestingUser.role?.type === "admin") {
        return true;
    }

    // Check if user is requesting their own data
    if (requestingUser.id === parseInt(userId)) {
        return true;
    }

    return false;
};
