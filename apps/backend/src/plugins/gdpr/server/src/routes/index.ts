export default [
    {
        method: "GET",
        path: "/gdpr/export/:userId",
        handler: "gdpr.exportData",
        config: {
            policies: ["plugin::gdpr.is-owner-or-admin"],
        },
    },
    {
        method: "DELETE",
        path: "/gdpr/delete/:userId",
        handler: "gdpr.deleteData",
        config: {
            policies: ["plugin::gdpr.is-owner-or-admin"],
        },
    },
    {
        method: "POST",
        path: "/gdpr/anonymize/:userId",
        handler: "gdpr.anonymizeData",
        config: {
            policies: ["plugin::gdpr.is-owner-or-admin"],
        },
    },
    {
        method: "GET",
        path: "/gdpr/consents/:userId",
        handler: "gdpr.getConsents",
        config: {
            policies: ["plugin::gdpr.is-owner-or-admin"],
        },
    },
    {
        method: "PUT",
        path: "/gdpr/consents/:userId",
        handler: "gdpr.updateConsents",
        config: {
            policies: ["plugin::gdpr.is-owner-or-admin"],
        },
    },
    {
        method: "GET",
        path: "/gdpr/audit-logs",
        handler: "gdpr.getAuditLogs",
        config: {
            policies: ["admin::isAuthenticatedAdmin"],
        },
    },
    {
        method: "GET",
        path: "/gdpr/request/:requestId",
        handler: "gdpr.getRequestStatus",
        config: {
            policies: [],
        },
    },
    {
        method: "GET",
        path: "/gdpr/audit-logs/export",
        handler: "gdpr.exportAuditLogs",
        config: {
            policies: ["admin::isAuthenticatedAdmin"],
        },
    },
    {
        method: "GET",
        path: "/gdpr/statistics",
        handler: "gdpr.getStatistics",
        config: {
            policies: ["admin::isAuthenticatedAdmin"],
        },
    },
];
