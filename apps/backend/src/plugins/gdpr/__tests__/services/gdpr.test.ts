import gdprService from "../../server/services/gdpr";

describe("GDPR Service", () => {
    let strapi: any;

    beforeEach(() => {
        // Mock Strapi instance
        strapi = {
            entityService: {
                findMany: jest.fn(),
                findOne: jest.fn(),
                create: jest.fn(),
                update: jest.fn(),
                delete: jest.fn(),
            },
            plugin: jest.fn(() => ({
                service: jest.fn(() => ({
                    create: jest.fn(),
                })),
            })),
        };
    });

    describe("exportUserData", () => {
        it("should export user data for a given user ID", async () => {
            const userId = "123";
            const mockUserData = {
                id: userId,
                email: "<EMAIL>",
                username: "testuser",
            };

            strapi.entityService.findOne.mockResolvedValue(mockUserData);

            const service = gdprService({ strapi });
            const result = await service.exportUserData(userId);

            expect(strapi.entityService.findOne).toHaveBeenCalledWith(
                "plugin::users-permissions.user",
                userId,
                expect.any(Object),
            );
            expect(result).toEqual(mockUserData);
        });

        it("should return null if user not found", async () => {
            const userId = "nonexistent";
            strapi.entityService.findOne.mockResolvedValue(null);

            const service = gdprService({ strapi });
            const result = await service.exportUserData(userId);

            expect(result).toBeNull();
        });
    });

    describe("deleteUserData", () => {
        it("should anonymize user data", async () => {
            const userId = "123";
            const anonymizedData = {
                id: userId,
                email: `deleted-${userId}@deleted.com`,
                username: `deleted-${userId}`,
                blocked: true,
            };

            strapi.entityService.update.mockResolvedValue(anonymizedData);

            const service = gdprService({ strapi });
            const result = await service.deleteUserData(userId);

            expect(strapi.entityService.update).toHaveBeenCalledWith(
                "plugin::users-permissions.user",
                userId,
                expect.objectContaining({
                    data: expect.objectContaining({
                        email: expect.stringContaining("deleted"),
                        username: expect.stringContaining("deleted"),
                        blocked: true,
                    }),
                }),
            );
            expect(result).toEqual(anonymizedData);
        });
    });

    describe("logDataAccess", () => {
        it("should create an audit log entry", async () => {
            const logData = {
                action: "data_export",
                userId: "123",
                performedBy: "admin",
                ipAddress: "127.0.0.1",
            };

            strapi
                .plugin()
                .service()
                .create.mockResolvedValue({
                    id: "1",
                    ...logData,
                    createdAt: new Date().toISOString(),
                });

            const service = gdprService({ strapi });
            await service.logDataAccess(logData);

            expect(strapi.plugin).toHaveBeenCalledWith("gdpr");
            expect(strapi.plugin().service).toHaveBeenCalledWith("audit-log");
            expect(strapi.plugin().service().create).toHaveBeenCalledWith(logData);
        });
    });

    describe("getConsentHistory", () => {
        it("should return consent history for a user", async () => {
            const userId = "123";
            const mockConsents = [
                {
                    id: "1",
                    type: "marketing",
                    granted: true,
                    createdAt: "2024-01-01",
                },
                {
                    id: "2",
                    type: "analytics",
                    granted: false,
                    createdAt: "2024-01-02",
                },
            ];

            strapi.entityService.findMany.mockResolvedValue(mockConsents);

            const service = gdprService({ strapi });
            const result = await service.getConsentHistory(userId);

            expect(strapi.entityService.findMany).toHaveBeenCalledWith(
                "plugin::gdpr.consent",
                expect.objectContaining({
                    filters: { user: userId },
                    sort: { createdAt: "desc" },
                }),
            );
            expect(result).toEqual(mockConsents);
        });
    });
});
