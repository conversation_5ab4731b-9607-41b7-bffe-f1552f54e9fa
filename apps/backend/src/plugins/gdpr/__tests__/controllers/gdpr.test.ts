import gdprController from "../../server/controllers/gdpr";

describe("GDPR Controller", () => {
    let strapi: any;
    let ctx: any;

    beforeEach(() => {
        // Mock Strapi instance
        strapi = {
            plugin: jest.fn(() => ({
                service: jest.fn(() => ({
                    exportUserData: jest.fn(),
                    deleteUserData: jest.fn(),
                    updateConsent: jest.fn(),
                    getConsentHistory: jest.fn(),
                })),
            })),
        };

        // Mock Koa context
        ctx = {
            params: {},
            request: {
                body: {},
            },
            state: {
                user: {
                    id: "123",
                },
            },
            send: jest.fn(),
            notFound: jest.fn(),
            badRequest: jest.fn(),
            unauthorized: jest.fn(),
        };
    });

    describe("exportData", () => {
        it("should export user data successfully", async () => {
            const mockData = {
                id: "123",
                email: "<EMAIL>",
                profile: { name: "Test User" },
            };

            strapi.plugin().service().exportUserData.mockResolvedValue(mockData);

            const controller = gdprController({ strapi });
            await controller.exportData(ctx);

            expect(strapi.plugin().service().exportUserData).toHaveBeenCalledWith("123");
            expect(ctx.send).toHaveBeenCalledWith({
                data: mockData,
                exportedAt: expect.any(String),
            });
        });

        it("should return 404 if user data not found", async () => {
            strapi.plugin().service().exportUserData.mockResolvedValue(null);

            const controller = gdprController({ strapi });
            await controller.exportData(ctx);

            expect(ctx.notFound).toHaveBeenCalledWith("User data not found");
        });
    });

    describe("deleteData", () => {
        it("should delete user data with confirmation", async () => {
            ctx.request.body = { confirm: true };
            const mockResult = { id: "123", anonymized: true };

            strapi.plugin().service().deleteUserData.mockResolvedValue(mockResult);

            const controller = gdprController({ strapi });
            await controller.deleteData(ctx);

            expect(strapi.plugin().service().deleteUserData).toHaveBeenCalledWith("123");
            expect(ctx.send).toHaveBeenCalledWith({
                message: "User data has been anonymized",
                result: mockResult,
            });
        });

        it("should require confirmation before deletion", async () => {
            ctx.request.body = { confirm: false };

            const controller = gdprController({ strapi });
            await controller.deleteData(ctx);

            expect(ctx.badRequest).toHaveBeenCalledWith(
                "Please confirm the deletion by setting confirm to true",
            );
            expect(strapi.plugin().service().deleteUserData).not.toHaveBeenCalled();
        });
    });

    describe("updateConsent", () => {
        it("should update user consent preferences", async () => {
            ctx.request.body = {
                consentType: "marketing",
                granted: true,
            };

            const mockConsent = {
                id: "1",
                user: "123",
                type: "marketing",
                granted: true,
                createdAt: new Date().toISOString(),
            };

            strapi.plugin().service().updateConsent.mockResolvedValue(mockConsent);

            const controller = gdprController({ strapi });
            await controller.updateConsent(ctx);

            expect(strapi.plugin().service().updateConsent).toHaveBeenCalledWith(
                "123",
                "marketing",
                true,
            );
            expect(ctx.send).toHaveBeenCalledWith({
                consent: mockConsent,
                message: "Consent preferences updated",
            });
        });

        it("should validate consent type", async () => {
            ctx.request.body = {
                granted: true,
            };

            const controller = gdprController({ strapi });
            await controller.updateConsent(ctx);

            expect(ctx.badRequest).toHaveBeenCalledWith("Consent type is required");
        });
    });

    describe("consentHistory", () => {
        it("should return consent history", async () => {
            const mockHistory = [
                { id: "1", type: "marketing", granted: true, createdAt: "2024-01-01" },
                { id: "2", type: "analytics", granted: false, createdAt: "2024-01-02" },
            ];

            strapi.plugin().service().getConsentHistory.mockResolvedValue(mockHistory);

            const controller = gdprController({ strapi });
            await controller.consentHistory(ctx);

            expect(strapi.plugin().service().getConsentHistory).toHaveBeenCalledWith("123");
            expect(ctx.send).toHaveBeenCalledWith({
                history: mockHistory,
                count: mockHistory.length,
            });
        });
    });
});
