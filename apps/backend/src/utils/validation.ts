/**
 * Validation Utilities
 * Basic validation helpers for CivicPoll backend
 */

/**
 * Check if string is a valid email
 */
export function isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Check if string is a valid URL
 */
export function isValidUrl(url: string): boolean {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

/**
 * Validate French postal code
 */
export function isValidFrenchPostalCode(postalCode: string): boolean {
    return /^\d{5}$/.test(postalCode);
}

/**
 * Validate phone number (basic)
 */
export function isValidPhoneNumber(phone: string): boolean {
    return /^[\+]?[1-9][\d]{0,15}$/.test(phone);
}

/**
 * Sanitize string input
 */
export function sanitizeString(input: string): string {
    return input.trim().replace(/[<>]/g, "");
}

/**
 * Validate geographic coordinates
 */
export function isValidCoordinate(lat: number, lng: number): boolean {
    return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
}

/**
 * Validate array of IDs
 */
export function validateIds(ids: unknown[]): number[] {
    const validIds: number[] = [];

    for (const id of ids) {
        if (typeof id === "number" && id > 0) {
            validIds.push(id);
        } else if (typeof id === "string" && !isNaN(Number(id))) {
            const numId = Number(id);
            if (numId > 0) {
                validIds.push(numId);
            }
        }
    }

    return validIds;
}
