/**
 * Permission Setup Utilities
 * Comprehensive permission management system based on PillarScan conventions
 */

export interface PermissionConfig {
    contentType: string;
    actions: string[];
    roles: string[];
    conditions?: any[];
}

export interface RoleConfig {
    name: string;
    description: string;
    type: string;
    permissions: PermissionConfig[];
}

/**
 * Default permission configurations for CivicPoll content types
 */
export const DEFAULT_PERMISSIONS: PermissionConfig[] = [
    // Poll permissions
    {
        contentType: "api::poll.poll",
        actions: ["find", "findOne"],
        roles: ["public", "authenticated"],
    },
    {
        contentType: "api::poll.poll",
        actions: ["create", "update", "delete"],
        roles: ["organization_admin", "admin"],
    },

    // Question permissions
    {
        contentType: "api::question.question",
        actions: ["find", "findOne"],
        roles: ["public", "authenticated"],
    },
    {
        contentType: "api::question.question",
        actions: ["create", "update", "delete"],
        roles: ["organization_admin", "admin"],
    },

    // Participation permissions
    {
        contentType: "api::participation.participation",
        actions: ["create"],
        roles: ["authenticated"],
    },
    {
        contentType: "api::participation.participation",
        actions: ["find", "findOne", "update", "delete"],
        roles: ["admin"],
        conditions: [{ "user.id": { $eq: "$user.id" } }],
    },

    // Response permissions
    {
        contentType: "api::response.response",
        actions: ["create"],
        roles: ["authenticated"],
    },
    {
        contentType: "api::response.response",
        actions: ["find", "findOne", "update", "delete"],
        roles: ["admin"],
    },

    // Organization permissions
    {
        contentType: "api::organization.organization",
        actions: ["find", "findOne"],
        roles: ["public", "authenticated"],
    },
    {
        contentType: "api::organization.organization",
        actions: ["create", "update", "delete"],
        roles: ["admin"],
    },

    // Geographic zone permissions
    {
        contentType: "api::geographic-zone.geographic-zone",
        actions: ["find", "findOne"],
        roles: ["public", "authenticated"],
    },
    {
        contentType: "api::geographic-zone.geographic-zone",
        actions: ["create", "update", "delete"],
        roles: ["admin"],
    },

    // User location permissions
    {
        contentType: "api::user-location.user-location",
        actions: ["create", "update"],
        roles: ["authenticated"],
        conditions: [{ "user.id": { $eq: "$user.id" } }],
    },
    {
        contentType: "api::user-location.user-location",
        actions: ["find", "findOne", "delete"],
        roles: ["admin"],
    },
];

/**
 * Role configurations for CivicPoll
 */
export const CIVICPOLL_ROLES: RoleConfig[] = [
    {
        name: "organization_admin",
        description: "Administrator of an organization who can manage polls",
        type: "authenticated",
        permissions: DEFAULT_PERMISSIONS.filter((p) => p.roles.includes("organization_admin")),
    },
    {
        name: "validator",
        description: "User who can validate poll responses and moderate content",
        type: "authenticated",
        permissions: DEFAULT_PERMISSIONS.filter((p) => p.roles.includes("validator")),
    },
    {
        name: "regional_admin",
        description: "Regional administrator who can manage geographic zones",
        type: "authenticated",
        permissions: DEFAULT_PERMISSIONS.filter((p) => p.roles.includes("regional_admin")),
    },
];

/**
 * Setup permissions for a specific content type
 */
export async function setupContentTypePermissions(
    strapi: any,
    contentType: string,
    permissions: PermissionConfig[],
): Promise<void> {
    try {
        const relevantPermissions = permissions.filter((p) => p.contentType === contentType);

        for (const permission of relevantPermissions) {
            await setupPermissionForRoles(strapi, permission);
        }

        console.log(`[Permissions] Setup completed for ${contentType}`);
    } catch (error) {
        console.error(`[Permissions] Error setting up permissions for ${contentType}:`, error);
    }
}

/**
 * Setup permission for specific roles
 */
async function setupPermissionForRoles(strapi: any, config: PermissionConfig): Promise<void> {
    const { contentType, actions, roles, conditions } = config;

    for (const roleName of roles) {
        try {
            const role = await findOrCreateRole(strapi, roleName);

            for (const action of actions) {
                await createPermission(strapi, {
                    action: `${contentType}.${action}`,
                    role: role.id,
                    conditions: conditions || [],
                });
            }
        } catch (error) {
            console.error(`[Permissions] Error setting up permission for role ${roleName}:`, error);
        }
    }
}

/**
 * Find or create a role
 */
async function findOrCreateRole(strapi: any, roleName: string): Promise<any> {
    let role = await strapi.db.query("plugin::users-permissions.role").findOne({
        where: { name: roleName },
    });

    if (!role) {
        role = await strapi.db.query("plugin::users-permissions.role").create({
            data: {
                name: roleName,
                description: getRoleDescription(roleName),
                type: "authenticated",
            },
        });
        console.log(`[Permissions] Created role: ${roleName}`);
    }

    return role;
}

/**
 * Create a permission
 */
async function createPermission(strapi: any, permissionData: any): Promise<void> {
    const existingPermission = await strapi.db
        .query("plugin::users-permissions.permission")
        .findOne({
            where: {
                action: permissionData.action,
                role: permissionData.role,
            },
        });

    if (!existingPermission) {
        await strapi.db.query("plugin::users-permissions.permission").create({
            data: permissionData,
        });
        console.log(
            `[Permissions] Created permission: ${permissionData.action} for role ${permissionData.role}`,
        );
    }
}

/**
 * Get role description
 */
function getRoleDescription(roleName: string): string {
    const descriptions: Record<string, string> = {
        organization_admin: "Administrator of an organization who can manage polls",
        validator: "User who can validate poll responses and moderate content",
        regional_admin: "Regional administrator who can manage geographic zones",
        public: "Public access for anonymous users",
        authenticated: "Authenticated user access",
    };

    return descriptions[roleName] || `Custom role: ${roleName}`;
}

/**
 * Setup all default permissions
 */
export async function setupAllPermissions(strapi: any): Promise<void> {
    console.log("[Permissions] Setting up all default permissions...");

    try {
        // Create custom roles
        for (const roleConfig of CIVICPOLL_ROLES) {
            await findOrCreateRole(strapi, roleConfig.name);
        }

        // Setup permissions for each content type
        const contentTypes = [
            "api::poll.poll",
            "api::question.question",
            "api::participation.participation",
            "api::response.response",
            "api::organization.organization",
            "api::geographic-zone.geographic-zone",
            "api::user-location.user-location",
        ];

        for (const contentType of contentTypes) {
            await setupContentTypePermissions(strapi, contentType, DEFAULT_PERMISSIONS);
        }

        console.log("[Permissions] All permissions setup completed successfully");
    } catch (error) {
        console.error("[Permissions] Error setting up permissions:", error);
    }
}

/**
 * Check if user has permission for a specific action
 */
export async function checkUserPermission(
    strapi: any,
    userId: number,
    action: string,
    resource?: any,
): Promise<boolean> {
    try {
        const user = await strapi.db.query("plugin::users-permissions.user").findOne({
            where: { id: userId },
            populate: { role: true },
        });

        if (!user) return false;

        const permission = await strapi.db.query("plugin::users-permissions.permission").findOne({
            where: {
                action,
                role: user.role.id,
            },
        });

        if (!permission) return false;

        // Check conditions if resource is provided
        if (resource && permission.conditions?.length > 0) {
            return evaluateConditions(permission.conditions, user, resource);
        }

        return true;
    } catch (error) {
        console.error("[Permissions] Error checking user permission:", error);
        return false;
    }
}

/**
 * Evaluate permission conditions
 */
function evaluateConditions(conditions: any[], user: any, resource: any): boolean {
    // Simple condition evaluation - can be extended for more complex logic
    for (const condition of conditions) {
        if (condition["user.id"] && condition["user.id"]["$eq"] === "$user.id") {
            if (resource.user?.id !== user.id) {
                return false;
            }
        }
    }

    return true;
}

/**
 * Get user permissions summary
 */
export async function getUserPermissions(strapi: any, userId: number): Promise<any> {
    try {
        const user = await strapi.db.query("plugin::users-permissions.user").findOne({
            where: { id: userId },
            populate: { role: true },
        });

        if (!user) return null;

        const permissions = await strapi.db.query("plugin::users-permissions.permission").findMany({
            where: { role: user.role.id },
        });

        return {
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                role: user.role.name,
            },
            permissions: permissions.map((p) => ({
                action: p.action,
                conditions: p.conditions,
            })),
        };
    } catch (error) {
        console.error("[Permissions] Error getting user permissions:", error);
        return null;
    }
}
