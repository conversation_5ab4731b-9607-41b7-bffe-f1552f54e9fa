/**
 * Logging Utilities
 * Comprehensive logging system for CivicPoll backend
 */

export interface LogEntry {
    level: "info" | "warn" | "error" | "debug";
    message: string;
    context?: string;
    metadata?: Record<string, any>;
    timestamp?: Date;
    userId?: number;
    ip?: string;
    userAgent?: string;
}

export interface LoggerConfig {
    enableConsole: boolean;
    enableFile: boolean;
    enableDatabase: boolean;
    logLevel: "debug" | "info" | "warn" | "error";
    maxFileSize?: number;
    maxFiles?: number;
}

class Logger {
    private config: LoggerConfig;

    constructor(config: LoggerConfig) {
        this.config = config;
    }

    /**
     * Log an info message
     */
    info(message: string, context?: string, metadata?: Record<string, any>): void {
        this.log({
            level: "info",
            message,
            context,
            metadata,
        });
    }

    /**
     * Log a warning message
     */
    warn(message: string, context?: string, metadata?: Record<string, any>): void {
        this.log({
            level: "warn",
            message,
            context,
            metadata,
        });
    }

    /**
     * Log an error message
     */
    error(message: string, context?: string, metadata?: Record<string, any>): void {
        this.log({
            level: "error",
            message,
            context,
            metadata,
        });
    }

    /**
     * Log a debug message
     */
    debug(message: string, context?: string, metadata?: Record<string, any>): void {
        this.log({
            level: "debug",
            message,
            context,
            metadata,
        });
    }

    /**
     * Log with request context
     */
    logRequest(ctx: any, message: string, level: LogEntry["level"] = "info"): void {
        this.log({
            level,
            message,
            context: "request",
            metadata: {
                method: ctx.request.method,
                url: ctx.request.url,
                ip: ctx.request.ip,
                userAgent: ctx.request.headers["user-agent"],
            },
            userId: ctx.state.user?.id,
            ip: ctx.request.ip,
            userAgent: ctx.request.headers["user-agent"],
        });
    }

    /**
     * Log API action
     */
    logAction(action: string, userId?: number, metadata?: Record<string, any>): void {
        this.log({
            level: "info",
            message: `Action performed: ${action}`,
            context: "action",
            metadata,
            userId,
        });
    }

    /**
     * Log security event
     */
    logSecurity(
        event: string,
        severity: "low" | "medium" | "high",
        metadata?: Record<string, any>,
    ): void {
        this.log({
            level: severity === "high" ? "error" : "warn",
            message: `Security event: ${event}`,
            context: "security",
            metadata: {
                severity,
                ...metadata,
            },
        });
    }

    /**
     * Core logging method
     */
    private log(entry: LogEntry): void {
        const logEntry: LogEntry = {
            ...entry,
            timestamp: new Date(),
        };

        // Check if log level is enabled
        if (!this.shouldLog(entry.level)) {
            return;
        }

        // Log to console if enabled
        if (this.config.enableConsole) {
            this.logToConsole(logEntry);
        }

        // Log to file if enabled
        if (this.config.enableFile) {
            this.logToFile(logEntry);
        }

        // Log to database if enabled
        if (this.config.enableDatabase) {
            this.logToDatabase(logEntry);
        }
    }

    /**
     * Check if log level should be logged
     */
    private shouldLog(level: LogEntry["level"]): boolean {
        const levels = ["debug", "info", "warn", "error"];
        const configLevel = levels.indexOf(this.config.logLevel);
        const messageLevel = levels.indexOf(level);

        return messageLevel >= configLevel;
    }

    /**
     * Log to console
     */
    private logToConsole(entry: LogEntry): void {
        const timestamp = entry.timestamp?.toISOString();
        const context = entry.context ? `[${entry.context}]` : "";
        const metadata = entry.metadata ? JSON.stringify(entry.metadata) : "";

        const message = `${timestamp} ${context} ${entry.message} ${metadata}`;

        switch (entry.level) {
            case "debug":
                console.debug(message);
                break;
            case "info":
                console.info(message);
                break;
            case "warn":
                console.warn(message);
                break;
            case "error":
                console.error(message);
                break;
        }
    }

    /**
     * Log to file (placeholder - would need file system implementation)
     */
    private logToFile(entry: LogEntry): void {
        // File logging implementation would go here
        // For now, just a placeholder
    }

    /**
     * Log to database (placeholder - would need database implementation)
     */
    private logToDatabase(entry: LogEntry): void {
        // Database logging implementation would go here
        // This could integrate with the audit-log plugin
    }
}

// Default logger instance
const defaultConfig: LoggerConfig = {
    enableConsole: true,
    enableFile: false,
    enableDatabase: false,
    logLevel: process.env.NODE_ENV === "development" ? "debug" : "info",
};

export const logger = new Logger(defaultConfig);

/**
 * Create a custom logger with specific configuration
 */
export function createLogger(config: Partial<LoggerConfig>): Logger {
    return new Logger({ ...defaultConfig, ...config });
}

/**
 * Middleware for automatic request logging
 */
export function createLoggingMiddleware(customLogger?: Logger) {
    const loggerInstance = customLogger || logger;

    return async (ctx: any, next: any) => {
        const start = Date.now();

        try {
            await next();

            const duration = Date.now() - start;
            loggerInstance.logRequest(ctx, `Request completed in ${duration}ms`, "info");
        } catch (error) {
            const duration = Date.now() - start;
            loggerInstance.logRequest(
                ctx,
                `Request failed in ${duration}ms: ${error.message}`,
                "error",
            );
            throw error;
        }
    };
}

/**
 * Log database queries for debugging
 */
export function logQuery(query: string, params?: any[], duration?: number): void {
    if (process.env.NODE_ENV === "development") {
        logger.debug("Database query executed", "database", {
            query,
            params,
            duration,
        });
    }
}

/**
 * Log API errors with proper context
 */
export function logApiError(error: Error, context: string, metadata?: Record<string, any>): void {
    logger.error(`API Error: ${error.message}`, context, {
        stack: error.stack,
        ...metadata,
    });
}
