/**
 * Permission Setup Utility for CivicPoll
 * Comprehensive permission management system based on PillarScan patterns
 */

interface PermissionConfig {
    action: string;
    roles: string[];
}

interface ApiPermissions {
    [apiName: string]: {
        [controllerName: string]: {
            [actionName: string]: PermissionConfig;
        };
    };
}

interface PluginPermissions {
    [pluginName: string]: {
        [controllerName: string]: {
            [actionName: string]: PermissionConfig;
        };
    };
}

// Define API permissions for all CivicPoll content types
const API_PERMISSIONS: ApiPermissions = {
    // Geographic Zone permissions
    "geographic-zone": {
        "geographic-zone": {
            find: { action: "find", roles: ["public", "authenticated"] },
            findOne: { action: "findOne", roles: ["public", "authenticated"] },
            create: { action: "create", roles: ["admin"] },
            update: { action: "update", roles: ["admin"] },
            delete: { action: "delete", roles: ["admin"] },
        },
    },

    // Poll permissions
    poll: {
        poll: {
            find: { action: "find", roles: ["public", "authenticated"] },
            findOne: { action: "findOne", roles: ["public", "authenticated"] },
            create: { action: "create", roles: ["organization_admin", "admin"] },
            update: { action: "update", roles: ["organization_admin", "admin"] },
            delete: { action: "delete", roles: ["organization_admin", "admin"] },
            validateParticipation: { action: "validateParticipation", roles: ["authenticated"] },
        },
        analytics: {
            getAnalytics: { action: "getAnalytics", roles: ["organization_admin", "admin"] },
            getResults: { action: "getResults", roles: ["public", "authenticated"] },
        },
    },

    // Question permissions
    question: {
        question: {
            find: { action: "find", roles: ["public", "authenticated"] },
            findOne: { action: "findOne", roles: ["public", "authenticated"] },
            create: { action: "create", roles: ["organization_admin", "admin"] },
            update: { action: "update", roles: ["organization_admin", "admin"] },
            delete: { action: "delete", roles: ["organization_admin", "admin"] },
        },
    },

    // Option permissions
    option: {
        option: {
            find: { action: "find", roles: ["public", "authenticated"] },
            findOne: { action: "findOne", roles: ["public", "authenticated"] },
            create: { action: "create", roles: ["organization_admin", "admin"] },
            update: { action: "update", roles: ["organization_admin", "admin"] },
            delete: { action: "delete", roles: ["organization_admin", "admin"] },
        },
    },

    // Participation permissions
    participation: {
        participation: {
            find: { action: "find", roles: ["admin"] },
            findOne: { action: "findOne", roles: ["authenticated"] },
            create: { action: "create", roles: ["authenticated"] },
            update: { action: "update", roles: ["authenticated"] },
            delete: { action: "delete", roles: ["admin"] },
        },
    },

    // Response permissions
    response: {
        response: {
            find: { action: "find", roles: ["admin"] },
            findOne: { action: "findOne", roles: ["authenticated"] },
            create: { action: "create", roles: ["authenticated"] },
            update: { action: "update", roles: ["authenticated"] },
            delete: { action: "delete", roles: ["admin"] },
        },
    },

    // Organization permissions
    organization: {
        organization: {
            find: { action: "find", roles: ["public", "authenticated"] },
            findOne: { action: "findOne", roles: ["public", "authenticated"] },
            create: { action: "create", roles: ["admin"] },
            update: { action: "update", roles: ["organization_admin", "admin"] },
            delete: { action: "delete", roles: ["admin"] },
        },
    },

    // User Location permissions
    "user-location": {
        "user-location": {
            find: { action: "find", roles: ["admin"] },
            findOne: { action: "findOne", roles: ["authenticated"] },
            create: { action: "create", roles: ["authenticated"] },
            update: { action: "update", roles: ["authenticated"] },
            delete: { action: "delete", roles: ["admin"] },
            validate: { action: "validate", roles: ["authenticated"] },
            mine: { action: "mine", roles: ["authenticated"] },
        },
    },

    // Poll Template permissions
    "poll-template": {
        "poll-template": {
            find: { action: "find", roles: ["authenticated"] },
            findOne: { action: "findOne", roles: ["authenticated"] },
            create: { action: "create", roles: ["organization_admin", "admin"] },
            update: { action: "update", roles: ["organization_admin", "admin"] },
            delete: { action: "delete", roles: ["organization_admin", "admin"] },
        },
    },

    // Health check permissions
    health: {
        health: {
            check: { action: "check", roles: ["public"] },
        },
    },
};

// Define plugin permissions
const PLUGIN_PERMISSIONS: PluginPermissions = {
    // Users-permissions plugin
    "users-permissions": {
        auth: {
            callback: { action: "callback", roles: ["public"] },
            register: { action: "register", roles: ["public"] },
            emailConfirmation: { action: "emailConfirmation", roles: ["public"] },
            sendEmailConfirmation: { action: "sendEmailConfirmation", roles: ["public"] },
            resetPassword: { action: "resetPassword", roles: ["public"] },
            changePassword: { action: "changePassword", roles: ["authenticated"] },
            forgotPassword: { action: "forgotPassword", roles: ["public"] },
        },
        user: {
            find: { action: "find", roles: ["admin"] },
            findOne: { action: "findOne", roles: ["admin"] },
            create: { action: "create", roles: ["admin"] },
            update: { action: "update", roles: ["admin"] },
            delete: { action: "delete", roles: ["admin"] },
            me: { action: "me", roles: ["authenticated"] },
            count: { action: "count", roles: ["admin"] },
            stats: { action: "stats", roles: ["authenticated"] },
            participations: { action: "participations", roles: ["authenticated"] },
        },
        role: {
            find: { action: "find", roles: ["admin"] },
            findOne: { action: "findOne", roles: ["admin"] },
            create: { action: "create", roles: ["admin"] },
            update: { action: "update", roles: ["admin"] },
            delete: { action: "delete", roles: ["admin"] },
        },
    },

    // Upload plugin
    upload: {
        upload: {
            upload: { action: "upload", roles: ["authenticated"] },
            uploadFiles: { action: "uploadFiles", roles: ["authenticated"] },
            removeFile: { action: "removeFile", roles: ["authenticated"] },
        },
    },

    // GDPR plugin (custom)
    gdpr: {
        gdpr: {
            requestData: { action: "requestData", roles: ["authenticated"] },
            deleteData: { action: "deleteData", roles: ["authenticated"] },
            exportData: { action: "exportData", roles: ["authenticated"] },
            manageRequests: { action: "manageRequests", roles: ["admin"] },
        },
    },

    // Audit Log plugin (custom)
    "audit-log": {
        "audit-log": {
            find: { action: "find", roles: ["admin"] },
            findOne: { action: "findOne", roles: ["admin"] },
            export: { action: "export", roles: ["admin"] },
        },
    },
};

// Custom roles for CivicPoll
const CUSTOM_ROLES = [
    {
        name: "organization_admin",
        description: "Administrator of an organization who can manage polls",
        type: "authenticated",
    },
    {
        name: "validator",
        description: "User who can validate poll responses and moderate content",
        type: "authenticated",
    },
    {
        name: "regional_admin",
        description: "Regional administrator who can manage geographic zones",
        type: "authenticated",
    },
];

/**
 * Create custom roles if they don't exist
 */
async function createCustomRoles(strapi: any): Promise<void> {
    console.log("📝 Creating custom roles...");

    for (const roleData of CUSTOM_ROLES) {
        try {
            const existingRole = await strapi.db.query("plugin::users-permissions.role").findOne({
                where: { name: roleData.name },
            });

            if (!existingRole) {
                await strapi.db.query("plugin::users-permissions.role").create({
                    data: roleData,
                });
                console.log(`✅ Created role: ${roleData.name}`);
            } else {
                console.log(`✅ Role already exists: ${roleData.name}`);
            }
        } catch (error) {
            console.error(`❌ Failed to create role ${roleData.name}:`, error);
        }
    }
}

/**
 * Wait for default roles to be created
 */
async function waitForDefaultRoles(strapi: any, maxAttempts = 10): Promise<boolean> {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        console.log(`🔍 Checking for default roles (attempt ${attempt}/${maxAttempts})...`);

        const publicRole = await strapi.db.query("plugin::users-permissions.role").findOne({
            where: {
                name: { $containsi: "public" },
            },
        });

        const authenticatedRole = await strapi.db.query("plugin::users-permissions.role").findOne({
            where: {
                name: { $containsi: "authenticated" },
            },
        });

        if (publicRole && authenticatedRole) {
            console.log("✅ Default roles found!");
            return true;
        }

        if (attempt < maxAttempts) {
            console.log("⏳ Default roles not found yet, waiting 1 second...");
            await new Promise((resolve) => setTimeout(resolve, 1000));
        }
    }

    return false;
}

/**
 * Setup permissions for an API
 */
async function setupApiPermissions(strapi: any, apiPermissions: ApiPermissions): Promise<void> {
    console.log("🔐 Setting up API permissions...");

    // Wait for default roles to be available
    const rolesReady = await waitForDefaultRoles(strapi);
    if (!rolesReady) {
        throw new Error("Default roles (public, authenticated) not found after waiting");
    }

    // Get roles
    const publicRole = await strapi.db.query("plugin::users-permissions.role").findOne({
        where: {
            name: { $containsi: "public" },
        },
    });

    const authenticatedRole = await strapi.db.query("plugin::users-permissions.role").findOne({
        where: {
            name: { $containsi: "authenticated" },
        },
    });

    if (!publicRole || !authenticatedRole) {
        throw new Error("Default roles (public, authenticated) not found");
    }

    // Get all roles
    const allRoles = await strapi.db.query("plugin::users-permissions.role").findMany();
    const roleMap = Object.fromEntries(
        allRoles.map((role: any) => [role.name.toLowerCase(), role]),
    );

    for (const [apiName, controllers] of Object.entries(apiPermissions)) {
        console.log(`🔧 Processing API: ${apiName}`);

        for (const [controllerName, actions] of Object.entries(controllers)) {
            for (const [actionName, config] of Object.entries(actions)) {
                const actionIdentifier = `api::${apiName}.${controllerName}.${actionName}`;

                try {
                    await updatePermission(strapi, actionIdentifier, config.roles, roleMap);
                } catch (error) {
                    console.error(`❌ Failed to update permission ${actionIdentifier}:`, error);
                }
            }
        }
    }
}

/**
 * Setup permissions for plugins
 */
async function setupPluginPermissions(
    strapi: any,
    pluginPermissions: PluginPermissions,
): Promise<void> {
    console.log("🔌 Setting up plugin permissions...");

    // Get all roles
    const allRoles = await strapi.db.query("plugin::users-permissions.role").findMany();
    const roleMap = Object.fromEntries(
        allRoles.map((role: any) => [role.name.toLowerCase(), role]),
    );

    for (const [pluginName, controllers] of Object.entries(pluginPermissions)) {
        console.log(`🔧 Processing plugin: ${pluginName}`);

        for (const [controllerName, actions] of Object.entries(controllers)) {
            for (const [actionName, config] of Object.entries(actions)) {
                const actionIdentifier = `plugin::${pluginName}.${controllerName}.${actionName}`;

                try {
                    await updatePermission(strapi, actionIdentifier, config.roles, roleMap);
                } catch (error) {
                    console.error(
                        `❌ Failed to update plugin permission ${actionIdentifier}:`,
                        error,
                    );
                }
            }
        }
    }
}

/**
 * Update a specific permission
 */
async function updatePermission(
    strapi: any,
    actionIdentifier: string,
    allowedRoles: string[],
    roleMap: Record<string, any>,
): Promise<void> {
    for (const [roleName, roleData] of Object.entries(roleMap)) {
        const shouldHavePermission = allowedRoles.includes(roleName);

        try {
            // Check if permission exists
            const existingPermission = await strapi.db
                .query("plugin::users-permissions.permission")
                .findOne({
                    where: {
                        action: actionIdentifier,
                        role: roleData.id,
                    },
                });

            if (shouldHavePermission) {
                if (!existingPermission) {
                    // Create permission
                    await strapi.db.query("plugin::users-permissions.permission").create({
                        data: {
                            action: actionIdentifier,
                            role: roleData.id,
                            enabled: true,
                            policy: "",
                        },
                    });
                    console.log(`✅ Enabled ${actionIdentifier} for ${roleName}`);
                } else if (!existingPermission.enabled) {
                    // Enable existing permission
                    await strapi.db.query("plugin::users-permissions.permission").update({
                        where: { id: existingPermission.id },
                        data: { enabled: true },
                    });
                    console.log(`✅ Enabled ${actionIdentifier} for ${roleName}`);
                }
            } else {
                if (existingPermission && existingPermission.enabled) {
                    // Disable permission
                    await strapi.db.query("plugin::users-permissions.permission").update({
                        where: { id: existingPermission.id },
                        data: { enabled: false },
                    });
                    console.log(`❌ Disabled ${actionIdentifier} for ${roleName}`);
                }
            }
        } catch (error) {
            console.error(
                `❌ Error updating permission ${actionIdentifier} for ${roleName}:`,
                error,
            );
        }
    }
}

/**
 * Main permission setup function
 */
export async function setupPermissions(strapi: any): Promise<void> {
    try {
        console.log("🚀 Starting permission setup...");

        // Create custom roles first
        await createCustomRoles(strapi);

        // Setup API permissions
        await setupApiPermissions(strapi, API_PERMISSIONS);

        // Setup plugin permissions
        await setupPluginPermissions(strapi, PLUGIN_PERMISSIONS);

        console.log("✅ Permission setup completed successfully!");
    } catch (error) {
        console.error("❌ Permission setup failed:", error);
        throw error;
    }
}

/**
 * Check current permission status
 */
export async function checkPermissionStatus(strapi: any): Promise<void> {
    console.log("🔍 Checking permission status...");

    try {
        const roles = await strapi.db.query("plugin::users-permissions.role").findMany();
        console.log(`📋 Found ${roles.length} roles:`);

        for (const role of roles) {
            const permissions = await strapi.db
                .query("plugin::users-permissions.permission")
                .findMany({
                    where: { role: role.id, enabled: true },
                });
            console.log(`  - ${role.name}: ${permissions.length} permissions`);
        }

        // Check critical permissions
        const criticalPermissions = [
            "api::poll.poll.find",
            "api::poll.poll.create",
            "api::participation.participation.create",
            "plugin::users-permissions.auth.callback",
            "plugin::users-permissions.user.me",
        ];

        console.log("\n🔒 Critical permissions check:");
        for (const permission of criticalPermissions) {
            const enabled = await strapi.db.query("plugin::users-permissions.permission").findMany({
                where: { action: permission, enabled: true },
            });
            console.log(
                `  ${permission}: ${enabled.length > 0 ? "✅" : "❌"} (${enabled.length} roles)`,
            );
        }
    } catch (error) {
        console.error("❌ Permission status check failed:", error);
    }
}
