/**
 * Dynamic middleware to format API request data from frontend
 * Automatically handles the conversion of stringified JSON values
 */

interface MiddlewareConfig {
    debug?: boolean;
}

export default (config: MiddlewareConfig = {}) => {
    return async (ctx: any, next: any) => {
        if (ctx.request.path.startsWith("/admin/")) {
            return next();
        }

        try {
            // Process POST and PUT requests to API endpoints
            const isApiPostRequest =
                (ctx.request.method === "POST" || ctx.request.method === "PUT") &&
                ctx.request.path.startsWith("/api/");

            if (isApiPostRequest) {
                // Handle both data wrapper and direct data
                let data = ctx.request.body?.data || ctx.request.body;

                if (data && typeof data === "object") {
                    // Recursive function to process nested objects
                    const processValue = (value: any): any => {
                        // Handle null/undefined
                        if (value === null || value === undefined) {
                            return value;
                        }

                        // Handle arrays
                        if (Array.isArray(value)) {
                            // Check if it's a single-element array with a JSON string
                            if (value.length === 1 && typeof value[0] === "string") {
                                const str = value[0].trim();
                                // Check if it looks like JSON (starts with [ or {)
                                if (
                                    (str.startsWith("[") && str.endsWith("]")) ||
                                    (str.startsWith("{") && str.endsWith("}"))
                                ) {
                                    try {
                                        const parsed = JSON.parse(str);
                                        // Recursively process the parsed value
                                        return processValue(parsed);
                                    } catch (e) {
                                        // Not valid JSON, return as-is
                                        return value;
                                    }
                                }
                            }
                            // Process each element in the array
                            return value.map((item) => processValue(item));
                        }

                        // Handle strings that might be JSON
                        if (typeof value === "string") {
                            const trimmed = value.trim();
                            // Check if it looks like JSON
                            if (
                                (trimmed.startsWith("[") && trimmed.endsWith("]")) ||
                                (trimmed.startsWith("{") && trimmed.endsWith("}"))
                            ) {
                                try {
                                    const parsed = JSON.parse(trimmed);
                                    // Recursively process the parsed value
                                    return processValue(parsed);
                                } catch (e) {
                                    // Not valid JSON, return as-is
                                    return value;
                                }
                            }
                            // Handle special string values
                            if (value === "current" || value === "null" || value === "undefined") {
                                return null;
                            }
                            return value;
                        }

                        // Handle objects - recursively process each property
                        if (typeof value === "object") {
                            const processed: any = {};
                            for (const [key, val] of Object.entries(value)) {
                                processed[key] = processValue(val);
                            }
                            return processed;
                        }

                        // Return other types as-is (numbers, booleans, etc.)
                        return value;
                    };

                    // Process all fields in the data object
                    const processedData: any = {};
                    for (const [key, value] of Object.entries(data)) {
                        const processed = processValue(value);

                        // Post-processing rules for specific field types
                        if (processed !== null && processed !== undefined) {
                            processedData[key] = processed;
                        }
                    }

                    if (config.debug) {
                        console.log("Processed data:", JSON.stringify(processedData, null, 2));
                    }

                    // Update the request body with the processed data
                    // Maintain the data wrapper if it was originally present
                    if (ctx.request.body?.data) {
                        ctx.request.body.data = processedData;
                    } else {
                        ctx.request.body = processedData;
                    }
                }
            }

            return next();
        } catch (error) {
            console.error("Error in format-request-data middleware:", error);
            // Continue with the request even if formatting fails
            return next();
        }
    };
};
