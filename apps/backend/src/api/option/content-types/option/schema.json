{"kind": "collectionType", "collectionName": "options", "info": {"singularName": "option", "pluralName": "options", "displayName": "Option", "description": "Options for poll questions"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"question": {"type": "relation", "relation": "manyToOne", "target": "api::question.question", "inversedBy": "options"}, "text": {"type": "string", "required": true, "maxLength": 200}, "value": {"type": "string", "description": "Internal value for the option"}, "order": {"type": "integer", "required": true, "min": 0}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}}}