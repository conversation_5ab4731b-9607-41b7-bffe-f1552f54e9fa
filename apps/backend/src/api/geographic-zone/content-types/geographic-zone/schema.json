{"kind": "collectionType", "collectionName": "geographic_zones", "info": {"singularName": "geographic-zone", "pluralName": "geographic-zones", "displayName": "Geographic Zone", "description": "Represents geographic zones (country, region, department, city)"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "code": {"type": "string", "required": true, "unique": true}, "type": {"type": "enumeration", "enum": ["country", "region", "department", "city"], "required": true}, "parent": {"type": "relation", "relation": "manyToOne", "target": "api::geographic-zone.geographic-zone"}, "children": {"type": "relation", "relation": "oneToMany", "target": "api::geographic-zone.geographic-zone", "mappedBy": "parent"}, "boundary": {"type": "json", "description": "GeoJSON polygon for zone boundaries"}, "center": {"type": "json", "description": "Center coordinates {lat, lng}"}, "population": {"type": "integer"}, "polls": {"type": "relation", "relation": "oneToMany", "target": "api::poll.poll", "mappedBy": "geographicZone"}, "userParticipations": {"type": "relation", "relation": "oneToMany", "target": "api::participation.participation", "mappedBy": "userGeographicZone"}}}