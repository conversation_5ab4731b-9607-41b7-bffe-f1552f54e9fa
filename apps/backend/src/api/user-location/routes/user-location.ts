export default {
    routes: [
        {
            method: "POST",
            path: "/user-locations/validate",
            handler: "user-location.validate",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "GET",
            path: "/user-locations/mine",
            handler: "user-location.mine",
            config: {
                policies: [],
                middlewares: [],
            },
        },
    ],
};
