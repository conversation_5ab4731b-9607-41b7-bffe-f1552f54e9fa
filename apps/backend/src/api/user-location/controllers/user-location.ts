import { factories } from "@strapi/strapi";
import axios from "axios";

export default factories.createCoreController("api::user-location.user-location", ({ strapi }) => ({
    async validate(ctx) {
        try {
            // Check if user is authenticated
            if (!ctx.state.user || !ctx.state.user.id) {
                console.log("[User Location Validate] No authenticated user found");
                return ctx.unauthorized("User not authenticated");
            }

            const userId = ctx.state.user.id;

            // Handle both wrapped and unwrapped data
            const data = ctx.request.body.data || ctx.request.body;
            const { address, postalCode, city, latitude, longitude } = data;

            // Validate required fields
            if (!postalCode || !city) {
                return ctx.badRequest("Missing required fields: postalCode and city are required");
            }

            // Find existing user location
            let userLocation = await strapi.query("api::user-location.user-location").findOne({
                where: { user: userId },
                populate: ["geographicZone"],
            });

            let coords = { lat: latitude, lng: longitude };

            // If coordinates not provided, geocode the address
            if (!latitude || !longitude) {
                const geocodeResponse = await axios.get(
                    "https://api-adresse.data.gouv.fr/search/",
                    {
                        params: {
                            q: `${address} ${postalCode} ${city}`,
                            limit: 1,
                        },
                    },
                );

                if (geocodeResponse.data.features.length === 0) {
                    return ctx.badRequest("Address not found");
                }

                const feature = geocodeResponse.data.features[0];
                coords = {
                    lat: feature.geometry.coordinates[1],
                    lng: feature.geometry.coordinates[0],
                };
            }

            // Find the geographic zone for this location
            // First, try to find the city
            let geographicZone = await strapi
                .query("api::geographic-zone.geographic-zone")
                .findOne({
                    where: {
                        type: "city",
                        $or: [
                            { name: { $containsi: city } },
                            { code: postalCode.substring(0, 2) }, // Department code from postal code
                        ],
                    },
                });

            // If no city found, try to find the department
            if (!geographicZone) {
                const deptCode = postalCode.substring(0, 2);
                geographicZone = await strapi
                    .query("api::geographic-zone.geographic-zone")
                    .findOne({
                        where: {
                            type: "department",
                            code: deptCode,
                        },
                    });
            }

            // Check if any geographic zones exist
            const zoneCount = await strapi.query("api::geographic-zone.geographic-zone").count();

            if (!geographicZone && zoneCount > 0) {
                // Only fail if zones exist but we couldn't find one for this location
                console.warn(
                    `[User Location Validate] WARNING: Geographic zone not found for location: ${city}, ${postalCode}. Total zones in DB: ${zoneCount}`,
                );
                return ctx.badRequest(
                    "Geographic zone not found for this location. Please ensure your location is within a supported zone.",
                );
            } else if (!geographicZone && zoneCount === 0) {
                // No zones in database, proceed without zone
                console.info(
                    "[User Location Validate] INFO: No geographic zones in database. Location will be validated without zone assignment.",
                );
            }

            // Update or create user location
            if (userLocation) {
                userLocation = await strapi.query("api::user-location.user-location").update({
                    where: { id: userLocation.id },
                    data: {
                        address: address || `${postalCode} ${city}`,
                        postalCode,
                        city,
                        latitude: coords.lat,
                        longitude: coords.lng,
                        geographicZone: geographicZone?.id || null,
                        validated: true,
                        validatedAt: new Date(),
                        validationRequired: false,
                    },
                });
            } else {
                userLocation = await strapi.query("api::user-location.user-location").create({
                    data: {
                        user: userId,
                        address: address || `${postalCode} ${city}`,
                        postalCode,
                        city,
                        latitude: coords.lat,
                        longitude: coords.lng,
                        geographicZone: geographicZone?.id || null,
                        validated: true,
                        validatedAt: new Date(),
                        validationRequired: false,
                    },
                });
            }

            // Log the validation
            console.log("User location validated:", {
                userId,
                city,
                postalCode,
                validated: true,
                zone: geographicZone?.name || "No zone assigned",
                zonesAvailable: zoneCount > 0,
            });

            // Prepare response message
            let message = "Location validated successfully";
            if (!geographicZone && zoneCount === 0) {
                message = "Location validated successfully (no geographic zones configured)";
            }

            return {
                data: {
                    documentId: userLocation.documentId,
                    validated: true,
                    message,
                    geographicZone: geographicZone
                        ? {
                              documentId: geographicZone.documentId,
                              name: geographicZone.name,
                              type: geographicZone.type,
                          }
                        : null,
                },
            };
        } catch (error) {
            strapi.log.error("User location validation error:", error);
            return ctx.internalServerError("Failed to validate location");
        }
    },

    async mine(ctx) {
        try {
            const userId = ctx.state.user.id;

            // Handle populate parameter - convert string to proper format
            let populate = {};
            if (ctx.query.populate) {
                if (ctx.query.populate === "*") {
                    populate = { geographicZone: true };
                } else if (ctx.query.populate === "geographicZone") {
                    populate = { geographicZone: true };
                } else {
                    // Handle other populate formats if needed
                    populate = { geographicZone: true };
                }
            } else {
                // Default populate
                populate = { geographicZone: true };
            }

            const userLocation = await strapi.query("api::user-location.user-location").findOne({
                where: { user: userId },
                populate: populate,
            });

            if (!userLocation) {
                return ctx.notFound("User location not found");
            }

            return {
                data: userLocation,
            };
        } catch (error) {
            strapi.log.error("Find user location error:", error);
            return ctx.internalServerError("Failed to retrieve user location");
        }
    },
}));
