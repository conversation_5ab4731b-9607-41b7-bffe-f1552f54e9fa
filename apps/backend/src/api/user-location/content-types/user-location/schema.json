{"kind": "collectionType", "collectionName": "user_locations", "info": {"singularName": "user-location", "pluralName": "user-locations", "displayName": "User Location", "description": "Stores and validates user geographic locations"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "unique": true}, "address": {"type": "string", "required": false}, "postalCode": {"type": "string", "required": true}, "city": {"type": "string", "required": true}, "latitude": {"type": "decimal", "required": false}, "longitude": {"type": "decimal", "required": false}, "geographicZone": {"type": "relation", "relation": "manyToOne", "target": "api::geographic-zone.geographic-zone"}, "validated": {"type": "boolean", "default": false}, "validatedAt": {"type": "datetime"}, "validationToken": {"type": "string", "private": true}, "validationRequired": {"type": "boolean", "default": true}}}