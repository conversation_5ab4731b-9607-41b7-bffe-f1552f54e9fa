{"kind": "collectionType", "collectionName": "poll_templates", "info": {"singularName": "poll-template", "pluralName": "poll-templates", "displayName": "Poll Template", "description": "Reusable poll templates"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true, "maxLength": 200}, "description": {"type": "text"}, "category": {"type": "enumeration", "enum": ["satisfaction", "opinion", "voting", "consultation"], "required": true}, "questions": {"type": "json", "description": "Template questions structure"}, "organization": {"type": "relation", "relation": "manyToOne", "target": "api::organization.organization"}, "isPublic": {"type": "boolean", "default": false, "description": "Whether this template can be used by other organizations"}}}