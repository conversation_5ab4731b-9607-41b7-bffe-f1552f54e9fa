/**
 * participation controller
 */

import { factories } from "@strapi/strapi";
import type { Core } from "@strapi/strapi";

export default factories.createCoreController(
    "api::participation.participation",
    ({ strapi }: { strapi: Core.Strapi }) => ({
        // Override create to handle custom participation submission
        async create(ctx) {
            try {
                const userId = ctx.state.user?.id;
                if (!userId) {
                    return ctx.unauthorized("User not authenticated");
                }

                const {
                    poll: pollDocumentId,
                    responses,
                    completed,
                    startedAt,
                    completedAt,
                } = ctx.request.body.data;

                // Get the poll by documentId
                const poll = await strapi.db.query("api::poll.poll").findOne({
                    where: { documentId: pollDocumentId },
                    populate: {
                        questions: {
                            populate: ["options"],
                        },
                        geographicZone: true,
                    },
                });

                if (!poll) {
                    return ctx.notFound("Poll not found");
                }

                // Check for existing incomplete participation
                let existingParticipation = await strapi.db
                    .query("api::participation.participation")
                    .findOne({
                        where: {
                            user: { id: userId },
                            poll: { id: poll.id },
                            completed: false,
                        },
                    });

                // If there's a completed participation, validate
                if (!existingParticipation) {
                    const pollService = strapi.service("api::poll.poll");
                    const validationResult = await pollService.management.validateUserParticipation(
                        userId,
                        poll.id,
                    );

                    if (!validationResult.canParticipate) {
                        return ctx.badRequest(
                            validationResult.reason || "Cannot participate in this poll",
                        );
                    }
                }

                // Get user location for geographic zone tracking
                const userLocation = await strapi.db
                    .query("api::user-location.user-location")
                    .findOne({
                        where: { user: { id: userId } },
                        populate: ["geographicZone"],
                    });

                // Create or update the participation
                let participation;
                if (existingParticipation) {
                    // Update existing participation
                    participation = await strapi.db
                        .query("api::participation.participation")
                        .update({
                            where: { id: existingParticipation.id },
                            data: {
                                completedAt: completed
                                    ? completedAt || new Date().toISOString()
                                    : null,
                                completed: completed || false,
                                ipAddress: ctx.request.ip,
                                userAgent: ctx.request.headers["user-agent"],
                            },
                        });

                    // Delete existing responses to replace with new ones
                    const existingResponses = await strapi.db
                        .query("api::response.response")
                        .findMany({
                            where: { participation: { id: existingParticipation.id } },
                        });

                    for (const response of existingResponses) {
                        await strapi.db.query("api::response.response").delete({
                            where: { id: response.id },
                        });
                    }
                } else {
                    // Create new participation using entity service for better relation handling
                    participation = await strapi.entityService.create(
                        "api::participation.participation",
                        {
                            data: {
                                user: userId,
                                poll: poll.id,
                                startedAt: startedAt || new Date().toISOString(),
                                completedAt: completed
                                    ? completedAt || new Date().toISOString()
                                    : null,
                                completed: completed || false,
                                userGeographicZone: userLocation?.geographicZone?.id || null,
                                ipAddress: ctx.request.ip,
                                userAgent: ctx.request.headers["user-agent"],
                            },
                        },
                    );
                }

                // Validate responses before creating them
                if (responses && typeof responses === "object") {
                    strapi.log.info("Creating responses for participation:", participation.id);
                    strapi.log.info("Responses received:", responses);

                    try {
                        // Pre-validate all responses to catch issues early
                        for (const [questionDocumentId, responseValue] of Object.entries(
                            responses,
                        )) {
                            const question = poll.questions.find(
                                (q: any) => q.documentId === questionDocumentId,
                            );

                            if (!question) {
                                throw new Error(`Question non trouvée: ${questionDocumentId}`);
                            }

                            // Validate choice questions have valid options
                            if (
                                question.type === "single_choice" ||
                                question.type === "multiple_choice"
                            ) {
                                const optionDocumentIds =
                                    question.type === "single_choice"
                                        ? [responseValue]
                                        : responseValue;

                                if (Array.isArray(optionDocumentIds)) {
                                    for (const optionDocId of optionDocumentIds) {
                                        if (!optionDocId || typeof optionDocId !== "string") {
                                            throw new Error(
                                                `Option invalide pour la question '${question.text}'`,
                                            );
                                        }
                                        const optionExists = question.options?.some(
                                            (opt: any) => opt.documentId === optionDocId,
                                        );
                                        if (!optionExists) {
                                            throw new Error(
                                                `Option '${optionDocId}' non trouvée pour la question '${question.text}'`,
                                            );
                                        }
                                    }
                                } else if (
                                    question.type === "single_choice" &&
                                    typeof responseValue === "string"
                                ) {
                                    const optionExists = question.options?.some(
                                        (opt: any) => opt.documentId === responseValue,
                                    );
                                    if (!optionExists) {
                                        throw new Error(
                                            `Option '${responseValue}' non trouvée pour la question '${question.text}'`,
                                        );
                                    }
                                }
                            }

                            // Validate multiple choice doesn't exceed maxSelections
                            if (
                                question.type === "multiple_choice" &&
                                question.maxSelections &&
                                Array.isArray(responseValue)
                            ) {
                                if (responseValue.length > question.maxSelections) {
                                    throw new Error(
                                        `Trop de sélections pour la question '${question.text}'. Maximum autorisé: ${question.maxSelections}`,
                                    );
                                }
                            }

                            // Validate multiple choice meets minSelections
                            if (
                                question.type === "multiple_choice" &&
                                question.minSelections &&
                                Array.isArray(responseValue)
                            ) {
                                if (responseValue.length < question.minSelections) {
                                    throw new Error(
                                        `Pas assez de sélections pour la question '${question.text}'. Minimum requis: ${question.minSelections}`,
                                    );
                                }
                            }

                            // Validate ranking questions have proper format
                            if (question.type === "ranking" && responseValue) {
                                if (!Array.isArray(responseValue)) {
                                    throw new Error(
                                        `Format de classement invalide pour la question '${question.text}'`,
                                    );
                                }

                                // Validate each ranking item has valid optionId
                                for (const rankItem of responseValue) {
                                    if (
                                        !rankItem.optionId ||
                                        typeof rankItem.optionId !== "string"
                                    ) {
                                        throw new Error(
                                            `Option de classement invalide pour la question '${question.text}'`,
                                        );
                                    }
                                    const optionExists = question.options?.some(
                                        (opt: any) => opt.documentId === rankItem.optionId,
                                    );
                                    if (!optionExists) {
                                        throw new Error(
                                            `Option '${rankItem.optionId}' non trouvée pour le classement de la question '${question.text}'`,
                                        );
                                    }
                                }
                            }

                            // Validate required questions have responses
                            if (
                                question.required &&
                                (responseValue === null ||
                                    responseValue === undefined ||
                                    responseValue === "" ||
                                    (Array.isArray(responseValue) && responseValue.length === 0))
                            ) {
                                throw new Error(`La question '${question.text}' est obligatoire`);
                            }
                        }

                        // Use custom response service to handle all responses
                        const responseService = strapi.service("api::response.response");
                        const createdResponses =
                            await responseService.createResponsesForParticipation(
                                participation.id,
                                poll.id,
                                responses,
                                poll.questions,
                            );

                        strapi.log.info(
                            `Successfully created ${createdResponses.length} responses`,
                        );
                    } catch (error: any) {
                        strapi.log.error("Error creating responses:", error);
                        // For validation errors, fail the entire participation
                        if (
                            error.message.includes("non trouvée") ||
                            error.message.includes("Trop de") ||
                            error.message.includes("Pas assez") ||
                            error.message.includes("obligatoire")
                        ) {
                            return ctx.badRequest(
                                `Erreur de validation des réponses: ${error.message}`,
                            );
                        }
                        // For other errors, don't fail the entire participation
                        strapi.log.warn("Participation created but some responses may have failed");
                    }
                }

                // Log the participation
                try {
                    if (
                        strapi.plugin("audit-log") &&
                        strapi.plugin("audit-log").service("auditLog")
                    ) {
                        await strapi
                            .plugin("audit-log")
                            .service("auditLog")
                            .create({
                                event: "PARTICIPATION_SUBMITTED",
                                entityId: participation.id,
                                entityType: "participation",
                                performedBy: userId,
                                details: {
                                    pollId: poll.id,
                                    pollTitle: poll.title,
                                    completed,
                                },
                            });
                    }
                } catch (auditError) {
                    strapi.log.warn("Could not create audit log:", auditError);
                }

                // Return the created participation
                // Note: Since there's an issue with populate syntax, we'll return the basic participation
                // The frontend can fetch additional data if needed
                return {
                    data: {
                        id: participation.id,
                        documentId: participation.documentId,
                        completed: participation.completed,
                        startedAt: participation.startedAt,
                        completedAt: participation.completedAt,
                        poll: {
                            id: poll.id,
                            documentId: poll.documentId,
                            title: poll.title,
                        },
                    },
                };
            } catch (error: any) {
                strapi.log.error("Participation creation error:", error);
                strapi.log.error("Error stack:", error.stack);
                return ctx.internalServerError("Failed to create participation: " + error.message);
            }
        },

        // Override findOne to add computed fields
        async findOne(ctx) {
            const { id } = ctx.params;

            const participation = await strapi
                .service("api::participation.participation")
                .findOne(id, ctx.query);

            if (!participation) {
                return ctx.notFound("Participation not found");
            }

            // Get the poll's questions count
            let totalQuestions = 0;
            let questionsAnswered = 0;

            if (participation.poll) {
                const poll = await strapi.db.query("api::poll.poll").findOne({
                    where: { id: participation.poll.id },
                    populate: ["questions"],
                });

                totalQuestions = poll?.questions?.length || 0;

                // Get the number of questions answered
                const responses = await strapi.db.query("api::response.response").findMany({
                    where: {
                        participation: { id: participation.id },
                    },
                });

                questionsAnswered = responses.length;
            }

            return {
                data: {
                    ...participation,
                    questionsAnswered,
                    totalQuestions,
                },
            };
        },
    }),
);
