{"kind": "collectionType", "collectionName": "participations", "info": {"singularName": "participation", "pluralName": "participations", "displayName": "Participation", "description": "User participations in polls"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "required": true}, "poll": {"type": "relation", "relation": "manyToOne", "target": "api::poll.poll", "inversedBy": "participations", "required": true}, "responses": {"type": "relation", "relation": "oneToMany", "target": "api::response.response", "mappedBy": "participation"}, "startedAt": {"type": "datetime", "required": true}, "completedAt": {"type": "datetime"}, "completed": {"type": "boolean", "default": false}, "userGeographicZone": {"type": "relation", "relation": "manyToOne", "target": "api::geographic-zone.geographic-zone", "inversedBy": "userParticipations", "description": "User's geographic zone at time of participation"}, "ipAddress": {"type": "string", "private": true}, "userAgent": {"type": "string", "private": true}}}