{"kind": "collectionType", "collectionName": "organizations", "info": {"singularName": "organization", "pluralName": "organizations", "displayName": "Organization", "description": "Organizations that can create polls"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true, "unique": true}, "smatflowId": {"type": "string", "unique": true, "private": false}, "type": {"type": "enumeration", "enum": ["municipality", "department", "region", "association", "public_service"], "required": true}, "description": {"type": "text"}, "logo": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "geographicZone": {"type": "relation", "relation": "manyToOne", "target": "api::geographic-zone.geographic-zone"}, "polls": {"type": "relation", "relation": "oneToMany", "target": "api::poll.poll", "mappedBy": "organization"}, "administrators": {"type": "relation", "relation": "manyToMany", "target": "plugin::users-permissions.user"}, "contactEmail": {"type": "email", "required": true}, "active": {"type": "boolean", "default": true}}}