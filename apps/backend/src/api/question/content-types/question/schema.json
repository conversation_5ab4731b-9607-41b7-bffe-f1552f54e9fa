{"kind": "collectionType", "collectionName": "questions", "info": {"singularName": "question", "pluralName": "questions", "displayName": "Question", "description": "Poll questions"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"poll": {"type": "relation", "relation": "manyToOne", "target": "api::poll.poll", "inversedBy": "questions"}, "text": {"type": "string", "required": true, "maxLength": 500}, "type": {"type": "enumeration", "enum": ["single_choice", "multiple_choice", "rating", "text", "ranking"], "required": true}, "required": {"type": "boolean", "default": true}, "order": {"type": "integer", "required": true, "min": 0}, "options": {"type": "relation", "relation": "oneToMany", "target": "api::option.option", "mappedBy": "question"}, "responses": {"type": "relation", "relation": "oneToMany", "target": "api::response.response", "mappedBy": "question"}, "ratingScale": {"type": "json", "description": "For rating questions: {min: 1, max: 5, labels: {...}}"}, "maxSelections": {"type": "integer", "description": "For multiple choice questions"}, "minSelections": {"type": "integer", "description": "For multiple choice questions", "default": 1}}}