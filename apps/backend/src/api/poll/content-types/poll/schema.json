{"kind": "collectionType", "collectionName": "polls", "info": {"singularName": "poll", "pluralName": "polls", "displayName": "Poll", "description": "Geographic-based polls"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true, "maxLength": 200}, "description": {"type": "richtext", "required": true}, "category": {"type": "enumeration", "enum": ["satisfaction", "opinion", "voting", "consultation"], "required": true}, "organization": {"type": "relation", "relation": "manyToOne", "target": "api::organization.organization", "inversedBy": "polls"}, "geographicZone": {"type": "relation", "relation": "manyToOne", "target": "api::geographic-zone.geographic-zone", "inversedBy": "polls"}, "startDate": {"type": "datetime", "required": true}, "endDate": {"type": "datetime", "required": true}, "isAnonymous": {"type": "boolean", "default": true}, "requiresValidatedLocation": {"type": "boolean", "default": true}, "pollStatus": {"type": "enumeration", "enum": ["draft", "scheduled", "active", "closed", "archived"], "default": "draft"}, "questions": {"type": "relation", "relation": "oneToMany", "target": "api::question.question", "mappedBy": "poll"}, "participations": {"type": "relation", "relation": "oneToMany", "target": "api::participation.participation", "mappedBy": "poll"}, "responses": {"type": "relation", "relation": "oneToMany", "target": "api::response.response", "mappedBy": "poll"}, "template": {"type": "relation", "relation": "manyToOne", "target": "api::poll-template.poll-template"}}}