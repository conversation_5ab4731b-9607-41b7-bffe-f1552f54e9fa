import type { Core } from "@strapi/strapi";

interface ValidationResult {
    canParticipate: boolean;
    reason?: string;
}

interface PollManagementService {
    createPoll(data: any, organizationId: number): Promise<any>;
    validateUserParticipation(userId: number, pollId: number): Promise<ValidationResult>;
    checkUserInZone(userZone: any, pollZone: any): Promise<boolean>;
    schedulePollActivation(pollId: number, startDate: string): Promise<void>;
    calculateCompletionRate(pollId: number): Promise<number>;
}

export default ({ strapi }: { strapi: Core.Strapi }): PollManagementService => ({
    async createPoll(data: any, organizationId: number) {
        // Validate geographic zone
        const zone = await strapi.db.query("api::geographic-zone.geographic-zone").findOne({
            where: { id: data.geographicZoneId },
        });

        if (!zone) {
            throw new Error("Invalid geographic zone");
        }

        // Create poll
        const poll = await strapi.db.query("api::poll.poll").create({
            data: {
                ...data,
                organization: organizationId,
                geographicZone: zone.id,
                pollStatus: "draft",
                publishedAt: null,
            },
        });

        // Schedule activation if start date is in the future
        if (new Date(data.startDate) > new Date()) {
            await this.schedulePollActivation(poll.id, data.startDate);
        }

        // Log creation event
        if (strapi.plugin("audit-log")) {
            await strapi
                .plugin("audit-log")
                .service("auditLog")
                .create({
                    event: "POLL_CREATED",
                    entityId: poll.id,
                    entityType: "poll",
                    performedBy: data.createdBy,
                    details: {
                        title: poll.title,
                        zone: zone.name,
                        zoneType: zone.type,
                    },
                });
        }

        return poll;
    },

    async validateUserParticipation(userId: number, pollId: number) {
        // Get poll with zone
        const poll = await strapi.db.query("api::poll.poll").findOne({
            where: { id: pollId },
            populate: ["geographicZone"],
        });

        if (!poll) {
            return {
                canParticipate: false,
                reason: "Poll not found",
            };
        }

        // Check if poll is active
        const now = new Date();
        if (now < new Date(poll.startDate) || now > new Date(poll.endDate)) {
            return {
                canParticipate: false,
                reason: "Poll is not active",
            };
        }

        // Get user location
        const userLocation = await strapi.db.query("api::user-location.user-location").findOne({
            where: { user: { id: userId } },
            populate: ["geographicZone"],
        });

        if (!userLocation || !userLocation.validated) {
            return {
                canParticipate: false,
                reason: "User location not validated",
            };
        }

        // Check if user is in the correct zone
        const isInZone = await this.checkUserInZone(
            userLocation.geographicZone,
            poll.geographicZone,
        );

        if (!isInZone) {
            return {
                canParticipate: false,
                reason: "User is not in the poll geographic zone",
            };
        }

        // Check if user already participated
        const existingParticipation = await strapi.db
            .query("api::participation.participation")
            .findOne({
                where: {
                    user: { id: userId },
                    poll: { id: pollId },
                },
            });

        if (existingParticipation && existingParticipation.completed) {
            return {
                canParticipate: false,
                reason: "User already participated in this poll",
            };
        }

        return {
            canParticipate: true,
            reason: undefined,
        };
    },

    async checkUserInZone(userZone: any, pollZone: any): Promise<boolean> {
        // Check if zones match or if user zone is a child of poll zone
        if (userZone.id === pollZone.id) {
            return true;
        }

        // Check hierarchy (e.g., user in a city that belongs to a department)
        let currentZone = userZone;
        while (currentZone.parent) {
            if (currentZone.parent === pollZone.id) {
                return true;
            }
            currentZone = await strapi.db.query("api::geographic-zone.geographic-zone").findOne({
                where: { id: currentZone.parent },
            });
        }

        return false;
    },

    async schedulePollActivation(pollId: number, startDate: string): Promise<void> {
        // In a production environment, this would schedule a job using a queue system
        // For now, we'll just log it
        strapi.log.info(`Poll ${pollId} scheduled for activation at ${startDate}`);

        // You could integrate with a job queue like Bull or similar
        // Example:
        // await strapi.service('api::job-queue.job-queue').schedule({
        //     type: 'ACTIVATE_POLL',
        //     data: { pollId },
        //     runAt: new Date(startDate)
        // });
    },

    async calculateCompletionRate(pollId: number): Promise<number> {
        const totalParticipations = await strapi.db
            .query("api::participation.participation")
            .count({
                where: { poll: pollId },
            });

        const completedParticipations = await strapi.db
            .query("api::participation.participation")
            .count({
                where: {
                    poll: pollId,
                    completed: true,
                },
            });

        return totalParticipations > 0 ? (completedParticipations / totalParticipations) * 100 : 0;
    },
});
