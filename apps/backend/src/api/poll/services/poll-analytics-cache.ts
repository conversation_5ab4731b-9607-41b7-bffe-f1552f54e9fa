import type { Core } from "@strapi/strapi";

interface PollAnalyticsCacheService {
    getCachedAnalytics(pollId: number): Promise<any>;
    setCachedAnalytics(pollId: number, data: any): Promise<void>;
    invalidatePollCache(pollId: number): Promise<void>;
    getCachedResults(pollId: number): Promise<any>;
    setCachedResults(pollId: number, data: any): Promise<void>;
    getActivePolls(zoneId?: number): Promise<any[]>;
    setActivePolls(zoneId: number | undefined, polls: any[]): Promise<void>;
}

export default ({ strapi }: { strapi: Core.Strapi }): PollAnalyticsCacheService => {
    const cache = strapi.service("api::poll.cache");

    return {
        // Analytics caching
        async getCachedAnalytics(pollId: number): Promise<any> {
            if (!cache.isEnabled()) return null;

            const key = `analytics:poll:${pollId}`;
            return await cache.get(key);
        },

        async setCachedAnalytics(pollId: number, data: any): Promise<void> {
            if (!cache.isEnabled()) return;

            const key = `analytics:poll:${pollId}`;
            // Cache analytics for 1 minute (60 seconds)
            await cache.set(key, data, 60);
        },

        // Results caching
        async getCachedResults(pollId: number): Promise<any> {
            if (!cache.isEnabled()) return null;

            const key = `results:poll:${pollId}`;
            return await cache.get(key);
        },

        async setCachedResults(pollId: number, data: any): Promise<void> {
            if (!cache.isEnabled()) return;

            const key = `results:poll:${pollId}`;
            // Cache results for 5 minutes (300 seconds)
            await cache.set(key, data, 300);
        },

        // Active polls caching
        async getActivePolls(zoneId?: number): Promise<any[]> {
            if (!cache.isEnabled()) return null;

            const key = zoneId ? `polls:active:zone:${zoneId}` : "polls:active:all";
            return await cache.get(key);
        },

        async setActivePolls(zoneId: number | undefined, polls: any[]): Promise<void> {
            if (!cache.isEnabled()) return;

            const key = zoneId ? `polls:active:zone:${zoneId}` : "polls:active:all";
            // Cache active polls for 5 minutes
            await cache.set(key, polls, 300);
        },

        // Cache invalidation
        async invalidatePollCache(pollId: number): Promise<void> {
            if (!cache.isEnabled()) return;

            // Get poll to find its zone
            const poll = await strapi.db.query("api::poll.poll").findOne({
                where: { id: pollId },
                populate: ["geographicZone"],
            });

            if (poll) {
                // Invalidate all related caches
                await Promise.all([
                    cache.del(`analytics:poll:${pollId}`),
                    cache.del(`results:poll:${pollId}`),
                    cache.del(`polls:active:zone:${poll.geographicZone.id}`),
                    cache.del("polls:active:all"),
                ]);

                strapi.log.info(`Cache invalidated for poll ${pollId}`);
            }
        },
    };
};
