import type { Core } from "@strapi/strapi";

interface CacheConfig {
    host: string;
    port: number;
    password?: string;
    enabled: boolean;
}

interface CacheService {
    get(key: string): Promise<any>;
    set(key: string, value: any, ttl?: number): Promise<void>;
    del(key: string): Promise<void>;
    flush(): Promise<void>;
    isEnabled(): boolean;
}

class MemoryCache implements CacheService {
    private cache: Map<string, { value: any; expires: number }> = new Map();

    async get(key: string): Promise<any> {
        const item = this.cache.get(key);
        if (!item) return null;

        if (item.expires < Date.now()) {
            this.cache.delete(key);
            return null;
        }

        return item.value;
    }

    async set(key: string, value: any, ttl: number = 300): Promise<void> {
        this.cache.set(key, {
            value,
            expires: Date.now() + ttl * 1000,
        });
    }

    async del(key: string): Promise<void> {
        this.cache.delete(key);
    }

    async flush(): Promise<void> {
        this.cache.clear();
    }

    isEnabled(): boolean {
        return true;
    }
}

export default ({ strapi }: { strapi: Core.Strapi }): CacheService => {
    // For now, use in-memory cache
    // TODO: Replace with Redis when available
    const cache = new MemoryCache();

    return {
        async get(key: string): Promise<any> {
            try {
                const value = await cache.get(key);
                if (value) {
                    strapi.log.debug(`Cache hit: ${key}`);
                }
                return value;
            } catch (error) {
                strapi.log.error("Cache get error:", error);
                return null;
            }
        },

        async set(key: string, value: any, ttl: number = 300): Promise<void> {
            try {
                await cache.set(key, value, ttl);
                strapi.log.debug(`Cache set: ${key} (TTL: ${ttl}s)`);
            } catch (error) {
                strapi.log.error("Cache set error:", error);
            }
        },

        async del(key: string): Promise<void> {
            try {
                await cache.del(key);
                strapi.log.debug(`Cache delete: ${key}`);
            } catch (error) {
                strapi.log.error("Cache delete error:", error);
            }
        },

        async flush(): Promise<void> {
            try {
                await cache.flush();
                strapi.log.info("Cache flushed");
            } catch (error) {
                strapi.log.error("Cache flush error:", error);
            }
        },

        isEnabled(): boolean {
            return cache.isEnabled();
        },
    };
};
