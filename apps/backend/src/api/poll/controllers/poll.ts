import { factories } from "@strapi/strapi";

interface PopulateConfig {
    geographicZone?: {
        fields?: string[];
    };
    organization?: {
        fields?: string[];
    };
    questions?: {
        count?: boolean;
        populate?: any;
    };
    participations?: {
        count?: boolean;
    };
    template?: boolean;
}

export default factories.createCoreController("api::poll.poll", ({ strapi }) => ({
    // Override the default find method to ensure proper population
    async find(ctx) {
        // Parse the query parameters
        const { query } = ctx;

        // Build populate object based on what's requested
        let populate: PopulateConfig | string = {};

        // If populate is a string (like "*" or specific fields), convert to object
        if (typeof query.populate === "string") {
            if (query.populate === "*") {
                populate = {
                    geographicZone: {
                        fields: [
                            "id",
                            "documentId",
                            "name",
                            "code",
                            "type",
                            "population",
                            "center",
                        ],
                    },
                    organization: {
                        fields: ["id", "documentId", "name", "type"],
                    },
                    questions: {
                        count: true,
                    },
                    participations: {
                        count: true,
                    },
                };
            } else {
                // Handle specific field population
                populate = query.populate;
            }
        } else if (typeof query.populate === "object" && query.populate !== null) {
            // Merge with existing populate object
            const existingPopulate = query.populate as PopulateConfig;
            populate = {
                ...existingPopulate,
                geographicZone: existingPopulate.geographicZone || {
                    fields: ["id", "documentId", "name", "code", "type", "population", "center"],
                },
            };
        } else {
            // Default population
            populate = {
                geographicZone: {
                    fields: ["id", "documentId", "name", "code", "type", "population", "center"],
                },
            };
        }

        // Ensure geographicZone is always populated with required fields
        const modifiedQuery = {
            ...query,
            populate,
        };

        // Use the modified query
        ctx.query = modifiedQuery;

        // Call the default find method
        const { data, meta } = await super.find(ctx);

        // Return the response
        return { data, meta };
    },

    // Override findOne to ensure proper population
    async findOne(ctx) {
        const { id } = ctx.params;
        const { query } = ctx;

        // Build populate object
        let populate: PopulateConfig = {};

        if (typeof query.populate === "string" && query.populate === "*") {
            populate = {
                geographicZone: {
                    fields: ["id", "documentId", "name", "code", "type", "population", "center"],
                },
                organization: {
                    fields: ["id", "documentId", "name", "type"],
                },
                questions: {
                    populate: {
                        options: true,
                    },
                },
                participations: {
                    count: true,
                },
                template: true,
            };
        } else {
            populate = {
                geographicZone: {
                    fields: ["id", "documentId", "name", "code", "type", "population", "center"],
                },
            };
        }

        // Ensure all relations are properly populated
        const modifiedQuery = {
            ...query,
            populate,
        };

        ctx.query = modifiedQuery;

        // Call the default findOne method
        return super.findOne(ctx);
    },

    // Debug endpoint to check data structure
    async debug(ctx) {
        try {
            const polls = await strapi.db.query("api::poll.poll").findMany({
                populate: {
                    geographicZone: true,
                    organization: true,
                    questions: {
                        count: true,
                    },
                },
                limit: 1,
            });

            return {
                data: {
                    count: polls.length,
                    sample: polls[0] || null,
                    structure: polls[0] ? Object.keys(polls[0]) : [],
                    geographicZoneStructure: polls[0]?.geographicZone
                        ? Object.keys(polls[0].geographicZone)
                        : [],
                },
            };
        } catch (error) {
            return ctx.internalServerError("Debug error: " + error.message);
        }
    },

    async validateParticipation(ctx) {
        try {
            const { id: pollDocumentId } = ctx.params;
            const userId = ctx.state.user?.id;

            if (!userId) {
                return {
                    data: {
                        canParticipate: false,
                        reason: "Authentication required",
                    },
                };
            }

            // Get the poll to find its numeric ID
            const poll = await strapi.db.query("api::poll.poll").findOne({
                where: { documentId: pollDocumentId },
            });

            if (!poll) {
                return {
                    data: {
                        canParticipate: false,
                        reason: "Poll not found",
                    },
                };
            }

            const pollService = strapi.service("api::poll.poll");
            const result = await pollService.management.validateUserParticipation(userId, poll.id);

            return {
                data: {
                    canParticipate: result.canParticipate,
                    reason: result.reason,
                },
            };
        } catch (error: any) {
            strapi.log.error("Poll participation validation error:", error);
            return {
                data: {
                    canParticipate: false,
                    reason: "An error occurred while validating participation",
                },
            };
        }
    },
}));
