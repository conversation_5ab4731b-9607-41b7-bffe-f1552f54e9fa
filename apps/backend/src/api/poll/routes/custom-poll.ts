export default {
    routes: [
        {
            method: "GET",
            path: "/polls",
            handler: "poll.find",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "GET",
            path: "/polls/:id",
            handler: "poll.findOne",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "POST",
            path: "/polls/:id/validate-participation",
            handler: "poll.validateParticipation",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "GET",
            path: "/polls/:id/analytics",
            handler: "analytics.getAnalytics",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "GET",
            path: "/polls/:id/results",
            handler: "analytics.getResults",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "GET",
            path: "/polls/debug",
            handler: "poll.debug",
            config: {
                auth: false,
                policies: [],
                middlewares: [],
            },
        },
    ],
};
