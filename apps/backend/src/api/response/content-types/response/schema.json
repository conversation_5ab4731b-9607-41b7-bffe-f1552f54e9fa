{"kind": "collectionType", "collectionName": "responses", "info": {"singularName": "response", "pluralName": "responses", "displayName": "Response", "description": "User responses to poll questions"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"participation": {"type": "relation", "relation": "manyToOne", "target": "api::participation.participation", "inversedBy": "responses", "required": true}, "question": {"type": "relation", "relation": "manyToOne", "target": "api::question.question", "inversedBy": "responses", "required": true}, "poll": {"type": "relation", "relation": "manyToOne", "target": "api::poll.poll", "inversedBy": "responses", "required": true}, "selectedOptions": {"type": "relation", "relation": "manyToMany", "target": "api::option.option", "description": "For single_choice and multiple_choice questions: selected option(s)"}, "textValue": {"type": "text", "description": "For text questions: free text response"}, "ratingValue": {"type": "integer", "description": "For rating questions: numeric rating value"}, "rankingOrder": {"type": "json", "description": "For ranking questions: [{optionId: number, rank: number}] - ordered list of options with their ranks"}, "answeredAt": {"type": "datetime", "required": true}}}