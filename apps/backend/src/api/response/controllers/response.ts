/**
 * response controller
 */

import { factories } from "@strapi/strapi";
import type { Core } from "@strapi/strapi";
import migration from "../services/migration";

export default factories.createCoreController(
    "api::response.response",
    ({ strapi }: { strapi: Core.Strapi }) => ({
        /**
         * Migrate legacy textValue responses to selectedOptions relations
         * POST /api/responses/migrate-legacy
         */
        async migrateLegacy(ctx) {
            try {
                // Only allow admin users to run migration
                if (!ctx.state.user || ctx.state.user.role?.type !== "admin") {
                    return ctx.forbidden("Only admin users can run migrations");
                }

                const result = await migration.migrateLegacyResponses(strapi);

                return ctx.send({
                    message: "Migration completed successfully",
                    result,
                });
            } catch (error: any) {
                strapi.log.error("Migration endpoint error:", error);
                return ctx.internalServerError(`Migration failed: ${error.message}`);
            }
        },

        /**
         * Rollback migration - convert selectedOptions back to textValue
         * POST /api/responses/rollback-migration
         */
        async rollbackMigration(ctx) {
            try {
                // Only allow admin users to run rollback
                if (!ctx.state.user || ctx.state.user.role?.type !== "admin") {
                    return ctx.forbidden("Only admin users can run rollback");
                }

                const result = await migration.rollbackMigration(strapi);

                return ctx.send({
                    message: "Rollback completed successfully",
                    result,
                });
            } catch (error: any) {
                strapi.log.error("Rollback endpoint error:", error);
                return ctx.internalServerError(`Rollback failed: ${error.message}`);
            }
        },
    }),
);
