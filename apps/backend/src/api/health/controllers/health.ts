/**
 * Health check controller
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreController("api::health.health", ({ strapi }) => ({
    /**
     * Health check endpoint
     */
    async check(ctx) {
        const startTime = Date.now();

        const health: any = {
            status: "ok",
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            environment: strapi.config.environment,
            version: strapi.config.info.strapi,
            services: {
                database: {
                    status: "unknown",
                    responseTime: 0,
                },
                redis: {
                    status: "unknown",
                    responseTime: 0,
                },
            },
            memory: {
                used: process.memoryUsage().heapUsed / 1024 / 1024,
                total: process.memoryUsage().heapTotal / 1024 / 1024,
                external: process.memoryUsage().external / 1024 / 1024,
            },
            responseTime: 0,
        };

        // Check database connectivity
        try {
            const dbStart = Date.now();
            await strapi.db.connection.raw("SELECT 1");
            health.services.database.status = "healthy";
            health.services.database.responseTime = Date.now() - dbStart;
        } catch (error) {
            health.services.database.status = "unhealthy";
            health.status = "degraded";
            strapi.log.error("Database health check failed:", error);
        }

        // Check Redis if configured
        if (strapi.config.get("cache.enabled")) {
            try {
                const redisStart = Date.now();
                // TODO: Add Redis health check when Redis is configured
                health.services.redis.status = "not_configured";
                health.services.redis.responseTime = Date.now() - redisStart;
            } catch (error) {
                health.services.redis.status = "unhealthy";
                health.status = "degraded";
                strapi.log.error("Redis health check failed:", error);
            }
        } else {
            health.services.redis.status = "not_configured";
        }

        health.responseTime = Date.now() - startTime;

        ctx.body = health;
    },

    /**
     * Prometheus metrics endpoint
     */
    async metrics(ctx) {
        const metrics = [];

        // Node.js metrics
        const memoryUsage = process.memoryUsage();
        metrics.push(`# HELP nodejs_heap_size_total_bytes Total size of the heap.`);
        metrics.push(`# TYPE nodejs_heap_size_total_bytes gauge`);
        metrics.push(`nodejs_heap_size_total_bytes ${memoryUsage.heapTotal}`);

        metrics.push(`# HELP nodejs_heap_size_used_bytes Used heap size.`);
        metrics.push(`# TYPE nodejs_heap_size_used_bytes gauge`);
        metrics.push(`nodejs_heap_size_used_bytes ${memoryUsage.heapUsed}`);

        metrics.push(`# HELP nodejs_external_memory_bytes External memory usage.`);
        metrics.push(`# TYPE nodejs_external_memory_bytes gauge`);
        metrics.push(`nodejs_external_memory_bytes ${memoryUsage.external}`);

        metrics.push(`# HELP process_uptime_seconds Process uptime in seconds.`);
        metrics.push(`# TYPE process_uptime_seconds counter`);
        metrics.push(`process_uptime_seconds ${process.uptime()}`);

        // GDPR metrics
        try {
            const pendingRequests = await strapi.db.query("plugin::gdpr.gdpr-request").count({
                where: { status: "pending" },
            });

            metrics.push(`# HELP gdpr_pending_requests Number of pending GDPR requests.`);
            metrics.push(`# TYPE gdpr_pending_requests gauge`);
            metrics.push(`gdpr_pending_requests ${pendingRequests}`);
        } catch (error) {
            // GDPR plugin might not be initialized
        }

        // Database connection pool metrics (if available)
        // Note: Knex pool metrics might not be directly accessible in Strapi v5
        try {
            const pool = (strapi.db.connection as any).pool;
            if (pool) {
                metrics.push(`# HELP database_pool_size Total size of the connection pool.`);
                metrics.push(`# TYPE database_pool_size gauge`);
                metrics.push(`database_pool_size ${pool.size || 0}`);

                metrics.push(`# HELP database_pool_used Number of used connections.`);
                metrics.push(`# TYPE database_pool_used gauge`);
                metrics.push(`database_pool_used ${pool.used || 0}`);
            }
        } catch (error) {
            // Pool metrics not available
        }

        // Custom application metrics
        metrics.push(`# HELP app_info Application information.`);
        metrics.push(`# TYPE app_info gauge`);
        metrics.push(
            `app_info{version="${strapi.config.info.strapi}",environment="${strapi.config.environment}",node_version="${process.version}"} 1`,
        );

        ctx.set("Content-Type", "text/plain; version=0.0.4");
        ctx.body = metrics.join("\n");
    },
}));
