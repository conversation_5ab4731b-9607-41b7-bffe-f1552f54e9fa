/**
 * Script runner for geographic data import
 * This creates a Strapi instance and runs the import script
 */

import { createStrapi } from "@strapi/strapi";
import type { Core } from "@strapi/strapi";
import { importGeographicData } from "./import-geographic-data";

async function run() {
    let strapi: Core.Strapi;

    try {
        // Create Strapi instance with TypeScript config support
        strapi = await createStrapi({
            autoReload: false,
            distDir: "./dist",
        }).load();

        console.log("Strapi loaded successfully");

        // Start Strapi to initialize database connections
        await strapi.start();

        console.log("Running geographic data import...");

        // Run the import
        await importGeographicData(strapi);

        console.log("Import completed successfully!");

        // Properly shut down
        await strapi.destroy();
        process.exit(0);
    } catch (error) {
        console.error("Import failed:", error);
        if (strapi) {
            await strapi.destroy();
        }
        process.exit(1);
    }
}

// Run the script
run();
