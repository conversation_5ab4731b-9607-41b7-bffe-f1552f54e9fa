/**
 * Manual Permission Setup Script
 * Can be run via Strapi console for manual permission configuration
 */

import { setupPermissions, checkPermissionStatus } from "../utils/setup-permissions";

async function runPermissionSetup(strapi: any) {
    console.log("🔧 Manual permission setup started...");

    try {
        await setupPermissions(strapi);
        console.log("\n📊 Current permission status:");
        await checkPermissionStatus(strapi);

        console.log("\n✅ Manual permission setup completed!");
    } catch (error) {
        console.error("❌ Manual permission setup failed:", error);
        throw error;
    }
}

export default runPermissionSetup;

// For use in Strapi console:
// const setupScript = require('./src/scripts/setup-permissions.ts').default;
// setupScript(strapi);
