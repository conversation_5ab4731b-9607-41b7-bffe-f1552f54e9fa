import type { Core } from "@strapi/strapi";
import axios from "axios";

const DATASETS = {
    regions: "https://geo.api.gouv.fr/regions",
    departments: "https://geo.api.gouv.fr/departements",
    communes: "https://geo.api.gouv.fr/communes",
};

export async function importGeographicData(strapi: Core.Strapi) {
    console.log("Starting geographic data import...");

    try {
        // 1. Create France (country level)
        const france = await strapi.db.query("api::geographic-zone.geographic-zone").create({
            data: {
                name: "France",
                code: "FR",
                type: "country",
                center: { lat: 46.603354, lng: 1.888334 },
            },
        });

        console.log("Created France zone");

        // 2. Import Regions
        const regionsResponse = await axios.get(DATASETS.regions);
        const regions = regionsResponse.data;

        console.log(`Importing ${regions.length} regions...`);

        for (const region of regions) {
            const createdRegion = await strapi.db
                .query("api::geographic-zone.geographic-zone")
                .create({
                    data: {
                        name: region.nom,
                        code: region.code,
                        type: "region",
                        parent: france.id,
                    },
                });

            console.log(`Created region: ${region.nom}`);

            // 3. Import Departments for each region
            const depsResponse = await axios.get(
                `${DATASETS.departments}?codeRegion=${region.code}`,
            );
            const departments = depsResponse.data;

            console.log(`  Importing ${departments.length} departments for ${region.nom}...`);

            for (const dept of departments) {
                const createdDept = await strapi.db
                    .query("api::geographic-zone.geographic-zone")
                    .create({
                        data: {
                            name: dept.nom,
                            code: dept.code,
                            type: "department",
                            parent: createdRegion.id,
                        },
                    });

                console.log(`    Created department: ${dept.nom}`);

                // 4. Import Cities (limit to cities > 5000 inhabitants for MVP)
                const citiesResponse = await axios.get(
                    `${DATASETS.communes}?codeDepartement=${dept.code}&fields=nom,code,codesPostaux,centre,population&format=json&geometry=centre`,
                );
                const cities = citiesResponse.data.filter((c: any) => c.population > 5000);

                console.log(
                    `      Importing ${cities.length} cities (>5000 inhabitants) for ${dept.nom}...`,
                );

                // Batch process cities to avoid overwhelming the database
                for (let i = 0; i < cities.length; i += 10) {
                    const batch = cities.slice(i, i + 10);

                    await Promise.all(
                        batch.map(async (city: any) => {
                            try {
                                await strapi.db
                                    .query("api::geographic-zone.geographic-zone")
                                    .create({
                                        data: {
                                            name: city.nom,
                                            code: city.code,
                                            type: "city",
                                            parent: createdDept.id,
                                            center: {
                                                lat: city.centre.coordinates[1],
                                                lng: city.centre.coordinates[0],
                                            },
                                            population: city.population,
                                        },
                                    });
                            } catch (error) {
                                console.error(`Failed to create city ${city.nom}:`, error.message);
                            }
                        }),
                    );

                    // Add a small delay between batches
                    if (i + 10 < cities.length) {
                        await new Promise((resolve) => setTimeout(resolve, 100));
                    }
                }

                console.log(`      Completed importing cities for ${dept.nom}`);
            }
        }

        console.log("Geographic data import completed successfully");

        // Generate summary
        const summary = await strapi.db.query("api::geographic-zone.geographic-zone").count({
            where: { type: { $in: ["country", "region", "department", "city"] } },
        });

        console.log(`\nImport Summary:`);
        console.log(`Total zones imported: ${summary}`);
    } catch (error) {
        console.error("Geographic data import failed:", error);
        throw error;
    }
}

// Script runner
export default async ({ strapi }: { strapi: Core.Strapi }) => {
    try {
        await importGeographicData(strapi);
        process.exit(0);
    } catch (error) {
        console.error("Script failed:", error);
        process.exit(1);
    }
};
