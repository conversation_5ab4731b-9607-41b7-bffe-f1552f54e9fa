/**
 * Permission Status Check Script
 * Comprehensive verification of all permission configurations
 */

import { checkPermissionStatus } from "../utils/setup-permissions";

async function checkAllPermissions(strapi: any) {
    console.log("🔍 Comprehensive Permission Status Check");
    console.log("=====================================\n");

    try {
        // Basic status check
        await checkPermissionStatus(strapi);

        // Detailed permission analysis
        console.log("\n📋 Detailed Permission Analysis:");
        console.log("================================");

        const roles = await strapi.db.query("plugin::users-permissions.role").findMany();

        for (const role of roles) {
            console.log(`\n🎭 Role: ${role.name} (${role.description || "No description"})`);

            const permissions = await strapi.db
                .query("plugin::users-permissions.permission")
                .findMany({
                    where: { role: role.id, enabled: true },
                });

            const apiPermissions = permissions.filter((p: any) => p.action.startsWith("api::"));
            const pluginPermissions = permissions.filter((p: any) =>
                p.action.startsWith("plugin::"),
            );

            console.log(`  📚 API Permissions: ${apiPermissions.length}`);
            apiPermissions.forEach((p: any) => {
                console.log(`    - ${p.action}`);
            });

            console.log(`  🔌 Plugin Permissions: ${pluginPermissions.length}`);
            pluginPermissions.forEach((p: any) => {
                console.log(`    - ${p.action}`);
            });
        }

        // Critical permission verification
        console.log("\n🚨 Critical Permission Verification:");
        console.log("===================================");

        const criticalChecks = [
            {
                name: "Poll Access",
                permissions: [
                    { action: "api::poll.poll.find", roles: ["public", "authenticated"] },
                    { action: "api::poll.poll.findOne", roles: ["public", "authenticated"] },
                ],
            },
            {
                name: "Poll Management",
                permissions: [
                    { action: "api::poll.poll.create", roles: ["organization_admin", "admin"] },
                    { action: "api::poll.poll.update", roles: ["organization_admin", "admin"] },
                ],
            },
            {
                name: "User Participation",
                permissions: [
                    { action: "api::participation.participation.create", roles: ["authenticated"] },
                    { action: "api::response.response.create", roles: ["authenticated"] },
                ],
            },
            {
                name: "Authentication",
                permissions: [
                    { action: "plugin::users-permissions.auth.callback", roles: ["public"] },
                    { action: "plugin::users-permissions.user.me", roles: ["authenticated"] },
                ],
            },
            {
                name: "Location Validation",
                permissions: [
                    { action: "api::user-location.user-location.create", roles: ["authenticated"] },
                    {
                        action: "api::user-location.user-location.validate",
                        roles: ["authenticated"],
                    },
                    { action: "api::user-location.user-location.mine", roles: ["authenticated"] },
                ],
            },
        ];

        let allCriticalPermissionsValid = true;

        for (const check of criticalChecks) {
            console.log(`\n  🔒 ${check.name}:`);

            for (const permCheck of check.permissions) {
                const roleNames = Array.isArray(permCheck.roles)
                    ? permCheck.roles
                    : [permCheck.roles];
                let hasPermission = false;

                for (const roleName of roleNames) {
                    const role = roles.find((r: any) => r.name === roleName);
                    if (role) {
                        const permission = await strapi.db
                            .query("plugin::users-permissions.permission")
                            .findOne({
                                where: {
                                    action: permCheck.action,
                                    role: role.id,
                                    enabled: true,
                                },
                            });

                        if (permission) {
                            hasPermission = true;
                            break;
                        }
                    }
                }

                const status = hasPermission ? "✅" : "❌";
                if (!hasPermission) allCriticalPermissionsValid = false;

                console.log(`    ${status} ${permCheck.action} (${roleNames.join(", ")})`);
            }
        }

        // Summary
        console.log("\n📊 Summary:");
        console.log("===========");
        console.log(`Total Roles: ${roles.length}`);
        console.log(
            `Critical Permissions: ${allCriticalPermissionsValid ? "✅ All Valid" : "❌ Issues Found"}`,
        );

        if (!allCriticalPermissionsValid) {
            console.log("\n⚠️  WARNING: Some critical permissions are missing!");
            console.log("   Run the setup-permissions script to fix this.");
        }

        console.log("\n✅ Permission status check completed!");
    } catch (error) {
        console.error("❌ Permission status check failed:", error);
        throw error;
    }
}

export default checkAllPermissions;

// For use in Strapi console:
// const checkScript = require('./src/scripts/check-permissions-status.ts').default;
// checkScript(strapi);
