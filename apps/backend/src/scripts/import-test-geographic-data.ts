import type { Core } from "@strapi/strapi";

export async function importTestGeographicData(strapi: Core.Strapi) {
    console.log("Starting test geographic data import...");

    try {
        // Check if data already exists
        const existingCount = await strapi.db.query("api::geographic-zone.geographic-zone").count();
        if (existingCount > 0) {
            console.log(
                `Geographic data already exists (${existingCount} zones). Skipping import.`,
            );
            return;
        }

        // 1. Create France (country level)
        const france = await strapi.db.query("api::geographic-zone.geographic-zone").create({
            data: {
                name: "France",
                code: "FR",
                type: "country",
                center: { lat: 46.603354, lng: 1.888334 },
            },
        });
        console.log("Created France zone");

        // 2. Create Île-de-France region
        const iledefrance = await strapi.db.query("api::geographic-zone.geographic-zone").create({
            data: {
                name: "Île-de-France",
                code: "11",
                type: "region",
                parent: france.id,
            },
        });
        console.log("Created Île-de-France region");

        // 3. Create Paris department
        const parisDept = await strapi.db.query("api::geographic-zone.geographic-zone").create({
            data: {
                name: "Paris",
                code: "75",
                type: "department",
                parent: iledefrance.id,
            },
        });
        console.log("Created Paris department");

        // 4. Create Paris city
        const parisCity = await strapi.db.query("api::geographic-zone.geographic-zone").create({
            data: {
                name: "Paris",
                code: "75056",
                type: "city",
                parent: parisDept.id,
                center: { lat: 48.8566, lng: 2.3522 },
                population: 2113705,
            },
        });
        console.log("Created Paris city");

        // 5. Create a few more test departments for other regions
        // Hauts-de-Seine
        const hautsdeseine = await strapi.db.query("api::geographic-zone.geographic-zone").create({
            data: {
                name: "Hauts-de-Seine",
                code: "92",
                type: "department",
                parent: iledefrance.id,
            },
        });
        console.log("Created Hauts-de-Seine department");

        // Val-de-Marne
        const valdemarne = await strapi.db.query("api::geographic-zone.geographic-zone").create({
            data: {
                name: "Val-de-Marne",
                code: "94",
                type: "department",
                parent: iledefrance.id,
            },
        });
        console.log("Created Val-de-Marne department");

        console.log("Test geographic data import completed successfully!");
        console.log("Total zones created: 6");
    } catch (error) {
        console.error("Test geographic data import failed:", error);
        throw error;
    }
}

// Allow running directly
export default importTestGeographicData;
