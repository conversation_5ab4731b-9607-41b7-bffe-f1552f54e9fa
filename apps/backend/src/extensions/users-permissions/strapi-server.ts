import userController from "./controllers/user";
import customRoutes from "./routes/custom-user";

export default (plugin: any) => {
    // Extend the user controller with custom methods
    plugin.controllers.user = {
        ...plugin.controllers.user,
        ...userController,
    };

    // Add custom routes
    customRoutes.routes.forEach((route: any) => {
        plugin.routes["content-api"].routes.push(route);
    });

    // Add lifecycle hooks for user creation

    plugin.contentTypes["user"].lifecycles = {
        async afterCreate(event: any) {
            const { result } = event;
            const { id, email, username } = result;

            try {
                // Create initial user location (unvalidated)
                await strapi.db.query("api::user-location.user-location").create({
                    data: {
                        user: id,
                        validated: false,
                        validationRequired: true,
                        validationToken: generateValidationToken(),
                    },
                });

                // Log user creation event
                await strapi
                    .plugin("audit-log")
                    ?.service("auditLog")
                    ?.create({
                        event: "USER_REGISTERED",
                        entityId: id,
                        entityType: "user",
                        performedBy: id,
                        details: {
                            username,
                            email,
                            provider: result.provider || "local",
                        },
                    });

                strapi.log.info(`New user registered: ${username} (${email})`);
            } catch (error) {
                // Don't fail user creation if ancillary operations fail
                strapi.log.error("Error in user afterCreate hook:", error);
            }
        },

        async beforeUpdate(event: any) {
            const { params } = event;

            // If user is being blocked, log it
            if (params.data?.blocked === true) {
                const userId = params.where?.id;
                if (userId) {
                    await strapi
                        .plugin("audit-log")
                        ?.service("auditLog")
                        ?.create({
                            event: "USER_BLOCKED",
                            entityId: userId,
                            entityType: "user",
                            performedBy: params.data?.updatedBy || 1,
                            details: {
                                reason: params.data?.blockReason || "No reason provided",
                            },
                        });
                }
            }
        },
    };

    return plugin;
};

// Helper function to generate validation token
function generateValidationToken(): string {
    return (
        Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
    );
}
