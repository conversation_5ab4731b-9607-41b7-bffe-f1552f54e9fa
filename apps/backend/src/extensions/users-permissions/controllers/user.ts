export default {
    async stats(ctx) {
        try {
            const { documentId: userDocumentId } = ctx.params;
            const requestingUserId = ctx.state.user?.id;

            if (!requestingUserId) {
                return ctx.unauthorized("Authentication required");
            }

            // Get user by documentId
            const user = await strapi.db.query("plugin::users-permissions.user").findOne({
                where: { documentId: userDocumentId },
            });

            if (!user) {
                return ctx.notFound("User not found");
            }

            // Check permissions - users can only see their own stats
            if (user.id !== requestingUserId) {
                // Check if requesting user has admin role
                const requestingUser = await strapi.db
                    .query("plugin::users-permissions.user")
                    .findOne({
                        where: { id: requestingUserId },
                        populate: ["role"],
                    });

                if (
                    !requestingUser?.role?.type ||
                    !["admin", "organization_admin"].includes(requestingUser.role.type)
                ) {
                    return ctx.forbidden("You can only view your own stats");
                }
            }

            // Get user's participations
            const participations = await strapi.db
                .query("api::participation.participation")
                .findMany({
                    where: {
                        user: user.id,
                    },
                    populate: ["poll"],
                });

            // Calculate stats
            const totalParticipations = participations.length;
            const completedPolls = participations.filter((p) => p.completedAt).length;
            const pendingPolls = participations.filter((p) => !p.completedAt).length;

            // Get last participation date
            const lastParticipation = participations
                .filter((p) => p.completedAt)
                .sort(
                    (a, b) => new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime(),
                )[0];

            const lastParticipationDate = lastParticipation?.completedAt || null;

            return {
                data: {
                    totalParticipations,
                    completedPolls,
                    pendingPolls,
                    lastParticipationDate,
                },
            };
        } catch (error) {
            strapi.log.error("Error fetching user stats:", error);
            return ctx.internalServerError("Failed to fetch user stats");
        }
    },

    async participations(ctx) {
        try {
            const { documentId: userDocumentId } = ctx.params;
            const requestingUserId = ctx.state.user?.id;

            if (!requestingUserId) {
                return ctx.unauthorized("Authentication required");
            }

            // Get user by documentId
            const user = await strapi.db.query("plugin::users-permissions.user").findOne({
                where: { documentId: userDocumentId },
            });

            if (!user) {
                return ctx.notFound("User not found");
            }

            // Check permissions - users can only see their own participations
            if (user.id !== requestingUserId) {
                // Check if requesting user has admin role
                const requestingUser = await strapi.db
                    .query("plugin::users-permissions.user")
                    .findOne({
                        where: { id: requestingUserId },
                        populate: ["role"],
                    });

                if (
                    !requestingUser?.role?.type ||
                    !["admin", "organization_admin"].includes(requestingUser.role.type)
                ) {
                    return ctx.forbidden("You can only view your own participations");
                }
            }

            // Get participations with populated data
            const participations = await strapi.db
                .query("api::participation.participation")
                .findMany({
                    where: {
                        user: user.id,
                    },
                    populate: {
                        poll: {
                            populate: ["questions", "geographicZone", "organization"],
                        },
                    },
                    orderBy: { createdAt: "desc" },
                    limit: ctx.query?.limit ? parseInt(ctx.query.limit as string) : 10,
                });

            // Add computed fields to each participation
            const participationsWithStats = await Promise.all(
                participations.map(async (participation) => {
                    const totalQuestions = participation.poll?.questions?.length || 0;

                    // Get the number of questions answered
                    const responses = await strapi.db.query("api::response.response").count({
                        where: {
                            participation: participation.id,
                        },
                    });

                    return {
                        ...participation,
                        questionsAnswered: responses,
                        totalQuestions,
                    };
                }),
            );

            return {
                data: participationsWithStats,
            };
        } catch (error) {
            strapi.log.error("Error fetching user participations:", error);
            return ctx.internalServerError("Failed to fetch participations");
        }
    },
};
