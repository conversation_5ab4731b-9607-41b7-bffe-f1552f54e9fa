import type { Core } from "@strapi/strapi";
import { setupPermissions } from "./utils/setup-permissions";

export default {
    /**
     * An asynchronous register function that runs before
     * your application is initialized.
     *
     * This gives you an opportunity to extend code.
     */
    register({ strapi }: { strapi: Core.Strapi }) {
        // Custom services are now registered automatically via the API structure
    },

    /**
     * An asynchronous bootstrap function that runs before
     * your application gets started.
     *
     * This gives you an opportunity to set up your data model,
     * run jobs, or perform some special logic.
     */
    async bootstrap({ strapi }: { strapi: Core.Strapi }) {
        console.log("🚀 Starting CivicPoll bootstrap process...");

        // Setup permissions after Strapi is fully loaded
        setTimeout(async () => {
            try {
                console.log("🔧 Setting up permissions after Strapi initialization...");
                await setupPermissions(strapi);
                console.log("✅ CivicPoll permission setup completed successfully!");
            } catch (error) {
                console.error("❌ CivicPoll permission setup failed:", error);
                // Log error but don't crash the application
            }
        }, 3000); // Wait 3 seconds for <PERSON>rap<PERSON> to fully initialize
        // Set up cache invalidation hooks
        strapi.db.lifecycles.subscribe({
            models: ["api::participation.participation"],
            async afterCreate(event) {
                // Invalidate poll cache when new participation is created
                const { result } = event;
                if (result.poll) {
                    const analyticsCache = strapi.service("api::poll.poll-analytics-cache");
                    await analyticsCache.invalidatePollCache(result.poll);
                }
            },
            async afterUpdate(event) {
                // Invalidate poll cache when participation is updated
                const { result } = event;
                if (result.poll) {
                    const analyticsCache = strapi.service("api::poll.poll-analytics-cache");
                    await analyticsCache.invalidatePollCache(result.poll);
                }
            },
        });

        strapi.db.lifecycles.subscribe({
            models: ["api::response.response"],
            async afterCreate(event) {
                // Invalidate poll cache when new response is created
                const { result } = event;
                const response = await strapi.db.query("api::response.response").findOne({
                    where: { id: result.id },
                    populate: ["participation.poll"],
                });

                if (response?.participation?.poll) {
                    const analyticsCache = strapi.service("api::poll.poll-analytics-cache");
                    await analyticsCache.invalidatePollCache(response.participation.poll);
                }
            },
        });
    },
};
