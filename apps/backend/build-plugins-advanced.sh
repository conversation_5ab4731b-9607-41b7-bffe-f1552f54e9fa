#!/bin/bash

# Advanced build script for all Strapi plugins
# Supports clean, watch, and parallel builds

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default options
CLEAN=false
WATCH=false
PARALLEL=false
MAX_PARALLEL=4

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -w|--watch)
            WATCH=true
            shift
            ;;
        -p|--parallel)
            PARALLEL=true
            shift
            ;;
        -j|--jobs)
            MAX_PARALLEL="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  -c, --clean     Clean before building"
            echo "  -w, --watch     Watch mode"
            echo "  -p, --parallel  Build in parallel"
            echo "  -j, --jobs N    Max parallel jobs (default: 4)"
            echo "  -h, --help      Show this help"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PLUGINS_DIR="${SCRIPT_DIR}/src/plugins"

echo -e "${BLUE}===============================================${NC}"
echo -e "${BLUE}       Strapi Plugins Build System${NC}"
echo -e "${BLUE}===============================================${NC}"
echo -e "Plugins directory: ${PLUGINS_DIR}"
echo -e "Clean build: ${CLEAN}"
echo -e "Watch mode: ${WATCH}"
echo -e "Parallel build: ${PARALLEL}"
if [ "$PARALLEL" = true ]; then
    echo -e "Max parallel jobs: ${MAX_PARALLEL}"
fi
echo -e "${BLUE}===============================================${NC}\n"

# Check if plugins directory exists
if [ ! -d "$PLUGINS_DIR" ]; then
    echo -e "${RED}Error: Plugins directory not found at ${PLUGINS_DIR}${NC}"
    exit 1
fi

# Function to build a single plugin
build_plugin() {
    local plugin_dir="$1"
    local plugin_name=$(basename "$plugin_dir")
    
    if [ ! -f "$plugin_dir/package.json" ]; then
        echo -e "${YELLOW}⚠ Skipping ${plugin_name} - no package.json${NC}"
        return 0
    fi
    
    echo -e "${YELLOW}🔨 Building plugin: ${plugin_name}${NC}"
    
    cd "$plugin_dir"
    
    # Clean if requested
    if [ "$CLEAN" = true ]; then
        echo -e "  ${BLUE}Cleaning ${plugin_name}...${NC}"
        rm -rf dist build node_modules/.cache
        if [ -f "tsconfig.tsbuildinfo" ]; then
            rm tsconfig.tsbuildinfo
        fi
    fi
    
    # Check if build script exists
    if ! grep -q '"build"' package.json; then
        echo -e "${YELLOW}  ⚠ No build script for ${plugin_name}${NC}"
        return 0
    fi
    
    # Build command
    local build_cmd="pnpm build"
    if [ "$WATCH" = true ]; then
        if grep -q '"build:watch"' package.json; then
            build_cmd="pnpm build:watch"
        else
            build_cmd="pnpm build -- --watch"
        fi
    fi
    
    # Run build
    if $build_cmd; then
        echo -e "${GREEN}  ✓ Successfully built ${plugin_name}${NC}"
        return 0
    else
        echo -e "${RED}  ✗ Failed to build ${plugin_name}${NC}"
        return 1
    fi
}

# Export function for parallel execution
export -f build_plugin
export RED GREEN YELLOW BLUE NC CLEAN WATCH

# Get list of plugins
PLUGINS=()
for plugin_dir in "$PLUGINS_DIR"/*; do
    if [ -d "$plugin_dir" ]; then
        PLUGINS+=("$plugin_dir")
    fi
done

# Build plugins
if [ "$PARALLEL" = true ] && [ "$WATCH" = false ]; then
    # Parallel build (not supported in watch mode)
    echo -e "${BLUE}Starting parallel build...${NC}\n"
    
    # Use GNU parallel if available, otherwise use xargs
    if command -v parallel &> /dev/null; then
        printf '%s\n' "${PLUGINS[@]}" | parallel -j "$MAX_PARALLEL" build_plugin {}
    else
        printf '%s\n' "${PLUGINS[@]}" | xargs -P "$MAX_PARALLEL" -I {} bash -c 'build_plugin "$@"' _ {}
    fi
else
    # Sequential build
    TOTAL_PLUGINS=0
    SUCCESSFUL_BUILDS=0
    FAILED_BUILDS=0
    
    for plugin_dir in "${PLUGINS[@]}"; do
        if [ -f "$plugin_dir/package.json" ]; then
            TOTAL_PLUGINS=$((TOTAL_PLUGINS + 1))
            
            if build_plugin "$plugin_dir"; then
                SUCCESSFUL_BUILDS=$((SUCCESSFUL_BUILDS + 1))
            else
                FAILED_BUILDS=$((FAILED_BUILDS + 1))
            fi
        fi
    done
    
    # Summary (not shown in watch mode)
    if [ "$WATCH" = false ]; then
        echo -e "\n${BLUE}===============================================${NC}"
        echo -e "${BLUE}Build Summary:${NC}"
        echo -e "${BLUE}===============================================${NC}"
        echo -e "Total plugins: ${TOTAL_PLUGINS}"
        echo -e "${GREEN}Successful: ${SUCCESSFUL_BUILDS}${NC}"
        echo -e "${RED}Failed: ${FAILED_BUILDS}${NC}"
        
        if [ $FAILED_BUILDS -gt 0 ]; then
            echo -e "\n${RED}⚠ Some plugins failed to build!${NC}"
            exit 1
        else
            echo -e "\n${GREEN}✓ All plugins built successfully!${NC}"
        fi
    fi
fi