import type { Schema, Struct } from "@strapi/strapi";

export interface AdminApiToken extends Struct.CollectionTypeSchema {
    collectionName: "strapi_api_tokens";
    info: {
        description: "";
        displayName: "Api Token";
        name: "Api Token";
        pluralName: "api-tokens";
        singularName: "api-token";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        accessKey: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        description: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }> &
            Schema.Attribute.DefaultTo<"">;
        encryptedKey: Schema.Attribute.Text &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        expiresAt: Schema.Attribute.DateTime;
        lastUsedAt: Schema.Attribute.DateTime;
        lifespan: Schema.Attribute.BigInteger;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "admin::api-token"> &
            Schema.Attribute.Private;
        name: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.Unique &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        permissions: Schema.Attribute.Relation<"oneToMany", "admin::api-token-permission">;
        publishedAt: Schema.Attribute.DateTime;
        type: Schema.Attribute.Enumeration<["read-only", "full-access", "custom"]> &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<"read-only">;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface AdminApiTokenPermission extends Struct.CollectionTypeSchema {
    collectionName: "strapi_api_token_permissions";
    info: {
        description: "";
        displayName: "API Token Permission";
        name: "API Token Permission";
        pluralName: "api-token-permissions";
        singularName: "api-token-permission";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        action: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "admin::api-token-permission"> &
            Schema.Attribute.Private;
        publishedAt: Schema.Attribute.DateTime;
        token: Schema.Attribute.Relation<"manyToOne", "admin::api-token">;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface AdminPermission extends Struct.CollectionTypeSchema {
    collectionName: "admin_permissions";
    info: {
        description: "";
        displayName: "Permission";
        name: "Permission";
        pluralName: "permissions";
        singularName: "permission";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        action: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        actionParameters: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        conditions: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<[]>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "admin::permission"> &
            Schema.Attribute.Private;
        properties: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        publishedAt: Schema.Attribute.DateTime;
        role: Schema.Attribute.Relation<"manyToOne", "admin::role">;
        subject: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface AdminRole extends Struct.CollectionTypeSchema {
    collectionName: "admin_roles";
    info: {
        description: "";
        displayName: "Role";
        name: "Role";
        pluralName: "roles";
        singularName: "role";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        code: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.Unique &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        description: Schema.Attribute.String;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "admin::role"> &
            Schema.Attribute.Private;
        name: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.Unique &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        permissions: Schema.Attribute.Relation<"oneToMany", "admin::permission">;
        publishedAt: Schema.Attribute.DateTime;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        users: Schema.Attribute.Relation<"manyToMany", "admin::user">;
    };
}

export interface AdminTransferToken extends Struct.CollectionTypeSchema {
    collectionName: "strapi_transfer_tokens";
    info: {
        description: "";
        displayName: "Transfer Token";
        name: "Transfer Token";
        pluralName: "transfer-tokens";
        singularName: "transfer-token";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        accessKey: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        description: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }> &
            Schema.Attribute.DefaultTo<"">;
        expiresAt: Schema.Attribute.DateTime;
        lastUsedAt: Schema.Attribute.DateTime;
        lifespan: Schema.Attribute.BigInteger;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "admin::transfer-token"> &
            Schema.Attribute.Private;
        name: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.Unique &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        permissions: Schema.Attribute.Relation<"oneToMany", "admin::transfer-token-permission">;
        publishedAt: Schema.Attribute.DateTime;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface AdminTransferTokenPermission extends Struct.CollectionTypeSchema {
    collectionName: "strapi_transfer_token_permissions";
    info: {
        description: "";
        displayName: "Transfer Token Permission";
        name: "Transfer Token Permission";
        pluralName: "transfer-token-permissions";
        singularName: "transfer-token-permission";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        action: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "admin::transfer-token-permission"> &
            Schema.Attribute.Private;
        publishedAt: Schema.Attribute.DateTime;
        token: Schema.Attribute.Relation<"manyToOne", "admin::transfer-token">;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface AdminUser extends Struct.CollectionTypeSchema {
    collectionName: "admin_users";
    info: {
        description: "";
        displayName: "User";
        name: "User";
        pluralName: "users";
        singularName: "user";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        blocked: Schema.Attribute.Boolean &
            Schema.Attribute.Private &
            Schema.Attribute.DefaultTo<false>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        email: Schema.Attribute.Email &
            Schema.Attribute.Required &
            Schema.Attribute.Private &
            Schema.Attribute.Unique &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 6;
            }>;
        firstname: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        isActive: Schema.Attribute.Boolean &
            Schema.Attribute.Private &
            Schema.Attribute.DefaultTo<false>;
        lastname: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "admin::user"> &
            Schema.Attribute.Private;
        password: Schema.Attribute.Password &
            Schema.Attribute.Private &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 6;
            }>;
        preferedLanguage: Schema.Attribute.String;
        publishedAt: Schema.Attribute.DateTime;
        registrationToken: Schema.Attribute.String & Schema.Attribute.Private;
        resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private;
        roles: Schema.Attribute.Relation<"manyToMany", "admin::role"> & Schema.Attribute.Private;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        username: Schema.Attribute.String;
    };
}

export interface ApiGeographicZoneGeographicZone extends Struct.CollectionTypeSchema {
    collectionName: "geographic_zones";
    info: {
        description: "Represents geographic zones (country, region, department, city)";
        displayName: "Geographic Zone";
        pluralName: "geographic-zones";
        singularName: "geographic-zone";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        boundary: Schema.Attribute.JSON;
        center: Schema.Attribute.JSON;
        children: Schema.Attribute.Relation<"oneToMany", "api::geographic-zone.geographic-zone">;
        code: Schema.Attribute.String & Schema.Attribute.Required & Schema.Attribute.Unique;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<
            "oneToMany",
            "api::geographic-zone.geographic-zone"
        > &
            Schema.Attribute.Private;
        name: Schema.Attribute.String & Schema.Attribute.Required;
        parent: Schema.Attribute.Relation<"manyToOne", "api::geographic-zone.geographic-zone">;
        polls: Schema.Attribute.Relation<"oneToMany", "api::poll.poll">;
        population: Schema.Attribute.Integer;
        publishedAt: Schema.Attribute.DateTime;
        type: Schema.Attribute.Enumeration<["country", "region", "department", "city"]> &
            Schema.Attribute.Required;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        userParticipations: Schema.Attribute.Relation<
            "oneToMany",
            "api::participation.participation"
        >;
    };
}

export interface ApiHealthHealth extends Struct.SingleTypeSchema {
    collectionName: "health";
    info: {
        description: "Health check endpoint";
        displayName: "Health";
        pluralName: "healths";
        singularName: "health";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::health.health"> &
            Schema.Attribute.Private;
        publishedAt: Schema.Attribute.DateTime;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface ApiOptionOption extends Struct.CollectionTypeSchema {
    collectionName: "options";
    info: {
        description: "Options for poll questions";
        displayName: "Option";
        pluralName: "options";
        singularName: "option";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        image: Schema.Attribute.Media<"images">;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::option.option"> &
            Schema.Attribute.Private;
        order: Schema.Attribute.Integer &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMax<
                {
                    min: 0;
                },
                number
            >;
        publishedAt: Schema.Attribute.DateTime;
        question: Schema.Attribute.Relation<"manyToOne", "api::question.question">;
        text: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 200;
            }>;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        value: Schema.Attribute.String;
    };
}

export interface ApiOrganizationOrganization extends Struct.CollectionTypeSchema {
    collectionName: "organizations";
    info: {
        description: "Organizations that can create polls";
        displayName: "Organization";
        pluralName: "organizations";
        singularName: "organization";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        active: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>;
        administrators: Schema.Attribute.Relation<"manyToMany", "plugin::users-permissions.user">;
        contactEmail: Schema.Attribute.Email & Schema.Attribute.Required;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        description: Schema.Attribute.Text;
        geographicZone: Schema.Attribute.Relation<
            "manyToOne",
            "api::geographic-zone.geographic-zone"
        >;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::organization.organization"> &
            Schema.Attribute.Private;
        logo: Schema.Attribute.Media<"images">;
        name: Schema.Attribute.String & Schema.Attribute.Required & Schema.Attribute.Unique;
        polls: Schema.Attribute.Relation<"oneToMany", "api::poll.poll">;
        publishedAt: Schema.Attribute.DateTime;
        smatflowId: Schema.Attribute.String & Schema.Attribute.Unique;
        type: Schema.Attribute.Enumeration<
            ["municipality", "department", "region", "association", "public_service"]
        > &
            Schema.Attribute.Required;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface ApiParticipationParticipation extends Struct.CollectionTypeSchema {
    collectionName: "participations";
    info: {
        description: "User participations in polls";
        displayName: "Participation";
        pluralName: "participations";
        singularName: "participation";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        completed: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
        completedAt: Schema.Attribute.DateTime;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        ipAddress: Schema.Attribute.String & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::participation.participation"> &
            Schema.Attribute.Private;
        poll: Schema.Attribute.Relation<"manyToOne", "api::poll.poll"> & Schema.Attribute.Required;
        publishedAt: Schema.Attribute.DateTime;
        responses: Schema.Attribute.Relation<"oneToMany", "api::response.response">;
        startedAt: Schema.Attribute.DateTime & Schema.Attribute.Required;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        user: Schema.Attribute.Relation<"manyToOne", "plugin::users-permissions.user"> &
            Schema.Attribute.Required;
        userAgent: Schema.Attribute.String & Schema.Attribute.Private;
        userGeographicZone: Schema.Attribute.Relation<
            "manyToOne",
            "api::geographic-zone.geographic-zone"
        >;
    };
}

export interface ApiPollTemplatePollTemplate extends Struct.CollectionTypeSchema {
    collectionName: "poll_templates";
    info: {
        description: "Reusable poll templates";
        displayName: "Poll Template";
        pluralName: "poll-templates";
        singularName: "poll-template";
    };
    options: {
        draftAndPublish: true;
    };
    attributes: {
        category: Schema.Attribute.Enumeration<
            ["satisfaction", "opinion", "voting", "consultation"]
        > &
            Schema.Attribute.Required;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        description: Schema.Attribute.Text;
        isPublic: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::poll-template.poll-template"> &
            Schema.Attribute.Private;
        name: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 200;
            }>;
        organization: Schema.Attribute.Relation<"manyToOne", "api::organization.organization">;
        publishedAt: Schema.Attribute.DateTime;
        questions: Schema.Attribute.JSON;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface ApiPollPoll extends Struct.CollectionTypeSchema {
    collectionName: "polls";
    info: {
        description: "Geographic-based polls";
        displayName: "Poll";
        pluralName: "polls";
        singularName: "poll";
    };
    options: {
        draftAndPublish: true;
    };
    attributes: {
        category: Schema.Attribute.Enumeration<
            ["satisfaction", "opinion", "voting", "consultation"]
        > &
            Schema.Attribute.Required;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        description: Schema.Attribute.RichText & Schema.Attribute.Required;
        endDate: Schema.Attribute.DateTime & Schema.Attribute.Required;
        geographicZone: Schema.Attribute.Relation<
            "manyToOne",
            "api::geographic-zone.geographic-zone"
        >;
        isAnonymous: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::poll.poll"> &
            Schema.Attribute.Private;
        organization: Schema.Attribute.Relation<"manyToOne", "api::organization.organization">;
        participations: Schema.Attribute.Relation<"oneToMany", "api::participation.participation">;
        pollStatus: Schema.Attribute.Enumeration<
            ["draft", "scheduled", "active", "closed", "archived"]
        > &
            Schema.Attribute.DefaultTo<"draft">;
        publishedAt: Schema.Attribute.DateTime;
        questions: Schema.Attribute.Relation<"oneToMany", "api::question.question">;
        requiresValidatedLocation: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>;
        responses: Schema.Attribute.Relation<"oneToMany", "api::response.response">;
        startDate: Schema.Attribute.DateTime & Schema.Attribute.Required;
        template: Schema.Attribute.Relation<"manyToOne", "api::poll-template.poll-template">;
        title: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 200;
            }>;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface ApiQuestionQuestion extends Struct.CollectionTypeSchema {
    collectionName: "questions";
    info: {
        description: "Poll questions";
        displayName: "Question";
        pluralName: "questions";
        singularName: "question";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::question.question"> &
            Schema.Attribute.Private;
        maxSelections: Schema.Attribute.Integer;
        minSelections: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<1>;
        options: Schema.Attribute.Relation<"oneToMany", "api::option.option">;
        order: Schema.Attribute.Integer &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMax<
                {
                    min: 0;
                },
                number
            >;
        poll: Schema.Attribute.Relation<"manyToOne", "api::poll.poll">;
        publishedAt: Schema.Attribute.DateTime;
        ratingScale: Schema.Attribute.JSON;
        required: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>;
        responses: Schema.Attribute.Relation<"oneToMany", "api::response.response">;
        text: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 500;
            }>;
        type: Schema.Attribute.Enumeration<
            ["single_choice", "multiple_choice", "rating", "text", "ranking"]
        > &
            Schema.Attribute.Required;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface ApiResponseResponse extends Struct.CollectionTypeSchema {
    collectionName: "responses";
    info: {
        description: "User responses to poll questions";
        displayName: "Response";
        pluralName: "responses";
        singularName: "response";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        answeredAt: Schema.Attribute.DateTime & Schema.Attribute.Required;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::response.response"> &
            Schema.Attribute.Private;
        participation: Schema.Attribute.Relation<"manyToOne", "api::participation.participation"> &
            Schema.Attribute.Required;
        poll: Schema.Attribute.Relation<"manyToOne", "api::poll.poll"> & Schema.Attribute.Required;
        publishedAt: Schema.Attribute.DateTime;
        question: Schema.Attribute.Relation<"manyToOne", "api::question.question"> &
            Schema.Attribute.Required;
        rankingOrder: Schema.Attribute.JSON;
        ratingValue: Schema.Attribute.Integer;
        selectedOptions: Schema.Attribute.Relation<"manyToMany", "api::option.option">;
        textValue: Schema.Attribute.Text;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface ApiUserLocationUserLocation extends Struct.CollectionTypeSchema {
    collectionName: "user_locations";
    info: {
        description: "Stores and validates user geographic locations";
        displayName: "User Location";
        pluralName: "user-locations";
        singularName: "user-location";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        address: Schema.Attribute.String;
        city: Schema.Attribute.String & Schema.Attribute.Required;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        geographicZone: Schema.Attribute.Relation<
            "manyToOne",
            "api::geographic-zone.geographic-zone"
        >;
        latitude: Schema.Attribute.Decimal;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::user-location.user-location"> &
            Schema.Attribute.Private;
        longitude: Schema.Attribute.Decimal;
        postalCode: Schema.Attribute.String & Schema.Attribute.Required;
        publishedAt: Schema.Attribute.DateTime;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        user: Schema.Attribute.Relation<"oneToOne", "plugin::users-permissions.user"> &
            Schema.Attribute.Unique;
        validated: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
        validatedAt: Schema.Attribute.DateTime;
        validationRequired: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>;
        validationToken: Schema.Attribute.String & Schema.Attribute.Private;
    };
}

export interface PluginAuditLogAuditLog extends Struct.CollectionTypeSchema {
    collectionName: "audit_logs";
    info: {
        description: "System audit log entries";
        displayName: "Audit Log";
        pluralName: "audit-logs";
        singularName: "audit-log";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        action: Schema.Attribute.Enumeration<
            [
                "create",
                "read",
                "update",
                "delete",
                "login",
                "logout",
                "export",
                "anonymize",
                "other",
            ]
        > &
            Schema.Attribute.Required;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        event: Schema.Attribute.String & Schema.Attribute.Required;
        ip: Schema.Attribute.String;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "plugin::audit-log.audit-log"> &
            Schema.Attribute.Private;
        metadata: Schema.Attribute.JSON;
        publishedAt: Schema.Attribute.DateTime;
        targetId: Schema.Attribute.String;
        targetType: Schema.Attribute.String & Schema.Attribute.Required;
        timestamp: Schema.Attribute.DateTime &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<"CURRENT_TIMESTAMP">;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        userAgent: Schema.Attribute.Text;
        userEmail: Schema.Attribute.String;
        userId: Schema.Attribute.Integer;
    };
}

export interface PluginContentReleasesRelease extends Struct.CollectionTypeSchema {
    collectionName: "strapi_releases";
    info: {
        displayName: "Release";
        pluralName: "releases";
        singularName: "release";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        actions: Schema.Attribute.Relation<"oneToMany", "plugin::content-releases.release-action">;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "plugin::content-releases.release"> &
            Schema.Attribute.Private;
        name: Schema.Attribute.String & Schema.Attribute.Required;
        publishedAt: Schema.Attribute.DateTime;
        releasedAt: Schema.Attribute.DateTime;
        scheduledAt: Schema.Attribute.DateTime;
        status: Schema.Attribute.Enumeration<["ready", "blocked", "failed", "done", "empty"]> &
            Schema.Attribute.Required;
        timezone: Schema.Attribute.String;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface PluginContentReleasesReleaseAction extends Struct.CollectionTypeSchema {
    collectionName: "strapi_release_actions";
    info: {
        displayName: "Release Action";
        pluralName: "release-actions";
        singularName: "release-action";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        contentType: Schema.Attribute.String & Schema.Attribute.Required;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        entryDocumentId: Schema.Attribute.String;
        isEntryValid: Schema.Attribute.Boolean;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<
            "oneToMany",
            "plugin::content-releases.release-action"
        > &
            Schema.Attribute.Private;
        publishedAt: Schema.Attribute.DateTime;
        release: Schema.Attribute.Relation<"manyToOne", "plugin::content-releases.release">;
        type: Schema.Attribute.Enumeration<["publish", "unpublish"]> & Schema.Attribute.Required;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface PluginGdprConsent extends Struct.CollectionTypeSchema {
    collectionName: "gdpr_consents";
    info: {
        description: "User consent records for GDPR compliance";
        displayName: "GDPR Consent";
        pluralName: "consents";
        singularName: "consent";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        consentMethod: Schema.Attribute.Enumeration<
            ["website_form", "mobile_app", "api", "manual", "imported"]
        > &
            Schema.Attribute.DefaultTo<"website_form">;
        consentType: Schema.Attribute.Enumeration<
            [
                "marketing",
                "analytics",
                "essential",
                "performance",
                "functionality",
                "targeting",
                "social_media",
            ]
        > &
            Schema.Attribute.Required;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        granted: Schema.Attribute.Boolean &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<false>;
        ipAddress: Schema.Attribute.String;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "plugin::gdpr.consent"> &
            Schema.Attribute.Private;
        publishedAt: Schema.Attribute.DateTime;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        user: Schema.Attribute.Relation<"manyToOne", "plugin::users-permissions.user"> &
            Schema.Attribute.Required;
        userAgent: Schema.Attribute.String;
        version: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<"1.0">;
        withdrawnAt: Schema.Attribute.DateTime;
    };
}

export interface PluginGdprGdprRequest extends Struct.CollectionTypeSchema {
    collectionName: "gdpr_requests";
    info: {
        description: "Track GDPR requests for async processing";
        displayName: "GDPR Request";
        pluralName: "gdpr-requests";
        singularName: "gdpr-request";
    };
    options: {
        comment: "";
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: true;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        completedAt: Schema.Attribute.DateTime;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        error: Schema.Attribute.Text;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "plugin::gdpr.gdpr-request"> &
            Schema.Attribute.Private;
        metadata: Schema.Attribute.JSON;
        publishedAt: Schema.Attribute.DateTime;
        requestedBy: Schema.Attribute.Relation<"manyToOne", "plugin::users-permissions.user">;
        result: Schema.Attribute.JSON;
        status: Schema.Attribute.Enumeration<["pending", "processing", "completed", "failed"]> &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<"pending">;
        type: Schema.Attribute.Enumeration<["export", "delete", "anonymize"]> &
            Schema.Attribute.Required;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        userId: Schema.Attribute.String & Schema.Attribute.Required;
    };
}

export interface PluginI18NLocale extends Struct.CollectionTypeSchema {
    collectionName: "i18n_locale";
    info: {
        collectionName: "locales";
        description: "";
        displayName: "Locale";
        pluralName: "locales";
        singularName: "locale";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        code: Schema.Attribute.String & Schema.Attribute.Unique;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "plugin::i18n.locale"> &
            Schema.Attribute.Private;
        name: Schema.Attribute.String &
            Schema.Attribute.SetMinMax<
                {
                    max: 50;
                    min: 1;
                },
                number
            >;
        publishedAt: Schema.Attribute.DateTime;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface PluginReviewWorkflowsWorkflow extends Struct.CollectionTypeSchema {
    collectionName: "strapi_workflows";
    info: {
        description: "";
        displayName: "Workflow";
        name: "Workflow";
        pluralName: "workflows";
        singularName: "workflow";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        contentTypes: Schema.Attribute.JSON &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<"[]">;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "plugin::review-workflows.workflow"> &
            Schema.Attribute.Private;
        name: Schema.Attribute.String & Schema.Attribute.Required & Schema.Attribute.Unique;
        publishedAt: Schema.Attribute.DateTime;
        stageRequiredToPublish: Schema.Attribute.Relation<
            "oneToOne",
            "plugin::review-workflows.workflow-stage"
        >;
        stages: Schema.Attribute.Relation<"oneToMany", "plugin::review-workflows.workflow-stage">;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface PluginReviewWorkflowsWorkflowStage extends Struct.CollectionTypeSchema {
    collectionName: "strapi_workflows_stages";
    info: {
        description: "";
        displayName: "Stages";
        name: "Workflow Stage";
        pluralName: "workflow-stages";
        singularName: "workflow-stage";
    };
    options: {
        draftAndPublish: false;
        version: "1.1.0";
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        color: Schema.Attribute.String & Schema.Attribute.DefaultTo<"#4945FF">;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<
            "oneToMany",
            "plugin::review-workflows.workflow-stage"
        > &
            Schema.Attribute.Private;
        name: Schema.Attribute.String;
        permissions: Schema.Attribute.Relation<"manyToMany", "admin::permission">;
        publishedAt: Schema.Attribute.DateTime;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        workflow: Schema.Attribute.Relation<"manyToOne", "plugin::review-workflows.workflow">;
    };
}

export interface PluginUploadFile extends Struct.CollectionTypeSchema {
    collectionName: "files";
    info: {
        description: "";
        displayName: "File";
        pluralName: "files";
        singularName: "file";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        alternativeText: Schema.Attribute.String;
        caption: Schema.Attribute.String;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        ext: Schema.Attribute.String;
        folder: Schema.Attribute.Relation<"manyToOne", "plugin::upload.folder"> &
            Schema.Attribute.Private;
        folderPath: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.Private &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        formats: Schema.Attribute.JSON;
        hash: Schema.Attribute.String & Schema.Attribute.Required;
        height: Schema.Attribute.Integer;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "plugin::upload.file"> &
            Schema.Attribute.Private;
        mime: Schema.Attribute.String & Schema.Attribute.Required;
        name: Schema.Attribute.String & Schema.Attribute.Required;
        previewUrl: Schema.Attribute.String;
        provider: Schema.Attribute.String & Schema.Attribute.Required;
        provider_metadata: Schema.Attribute.JSON;
        publishedAt: Schema.Attribute.DateTime;
        related: Schema.Attribute.Relation<"morphToMany">;
        size: Schema.Attribute.Decimal & Schema.Attribute.Required;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        url: Schema.Attribute.String & Schema.Attribute.Required;
        width: Schema.Attribute.Integer;
    };
}

export interface PluginUploadFolder extends Struct.CollectionTypeSchema {
    collectionName: "upload_folders";
    info: {
        displayName: "Folder";
        pluralName: "folders";
        singularName: "folder";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        children: Schema.Attribute.Relation<"oneToMany", "plugin::upload.folder">;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        files: Schema.Attribute.Relation<"oneToMany", "plugin::upload.file">;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "plugin::upload.folder"> &
            Schema.Attribute.Private;
        name: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        parent: Schema.Attribute.Relation<"manyToOne", "plugin::upload.folder">;
        path: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        pathId: Schema.Attribute.Integer & Schema.Attribute.Required & Schema.Attribute.Unique;
        publishedAt: Schema.Attribute.DateTime;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface PluginUsersPermissionsPermission extends Struct.CollectionTypeSchema {
    collectionName: "up_permissions";
    info: {
        description: "";
        displayName: "Permission";
        name: "permission";
        pluralName: "permissions";
        singularName: "permission";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        action: Schema.Attribute.String & Schema.Attribute.Required;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<
            "oneToMany",
            "plugin::users-permissions.permission"
        > &
            Schema.Attribute.Private;
        publishedAt: Schema.Attribute.DateTime;
        role: Schema.Attribute.Relation<"manyToOne", "plugin::users-permissions.role">;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface PluginUsersPermissionsRole extends Struct.CollectionTypeSchema {
    collectionName: "up_roles";
    info: {
        description: "";
        displayName: "Role";
        name: "role";
        pluralName: "roles";
        singularName: "role";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        description: Schema.Attribute.String;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "plugin::users-permissions.role"> &
            Schema.Attribute.Private;
        name: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 3;
            }>;
        permissions: Schema.Attribute.Relation<"oneToMany", "plugin::users-permissions.permission">;
        publishedAt: Schema.Attribute.DateTime;
        type: Schema.Attribute.String & Schema.Attribute.Unique;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        users: Schema.Attribute.Relation<"oneToMany", "plugin::users-permissions.user">;
    };
}

export interface PluginUsersPermissionsUser extends Struct.CollectionTypeSchema {
    collectionName: "up_users";
    info: {
        description: "User extension for CivicPoll";
        displayName: "User";
        name: "user";
        pluralName: "users";
        singularName: "user";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        appRole: Schema.Attribute.Enumeration<["user", "moderator", "admin"]> &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<"user">;
        blocked: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
        confirmationToken: Schema.Attribute.String & Schema.Attribute.Private;
        confirmed: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        email: Schema.Attribute.Email &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 6;
            }>;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "plugin::users-permissions.user"> &
            Schema.Attribute.Private;
        password: Schema.Attribute.Password &
            Schema.Attribute.Private &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 6;
            }>;
        provider: Schema.Attribute.String;
        publishedAt: Schema.Attribute.DateTime;
        resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private;
        role: Schema.Attribute.Relation<"manyToOne", "plugin::users-permissions.role">;
        smatflowOrganizationId: Schema.Attribute.String;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        username: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.Unique &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 3;
            }>;
    };
}

declare module "@strapi/strapi" {
    export module Public {
        export interface ContentTypeSchemas {
            "admin::api-token": AdminApiToken;
            "admin::api-token-permission": AdminApiTokenPermission;
            "admin::permission": AdminPermission;
            "admin::role": AdminRole;
            "admin::transfer-token": AdminTransferToken;
            "admin::transfer-token-permission": AdminTransferTokenPermission;
            "admin::user": AdminUser;
            "api::geographic-zone.geographic-zone": ApiGeographicZoneGeographicZone;
            "api::health.health": ApiHealthHealth;
            "api::option.option": ApiOptionOption;
            "api::organization.organization": ApiOrganizationOrganization;
            "api::participation.participation": ApiParticipationParticipation;
            "api::poll-template.poll-template": ApiPollTemplatePollTemplate;
            "api::poll.poll": ApiPollPoll;
            "api::question.question": ApiQuestionQuestion;
            "api::response.response": ApiResponseResponse;
            "api::user-location.user-location": ApiUserLocationUserLocation;
            "plugin::audit-log.audit-log": PluginAuditLogAuditLog;
            "plugin::content-releases.release": PluginContentReleasesRelease;
            "plugin::content-releases.release-action": PluginContentReleasesReleaseAction;
            "plugin::gdpr.consent": PluginGdprConsent;
            "plugin::gdpr.gdpr-request": PluginGdprGdprRequest;
            "plugin::i18n.locale": PluginI18NLocale;
            "plugin::review-workflows.workflow": PluginReviewWorkflowsWorkflow;
            "plugin::review-workflows.workflow-stage": PluginReviewWorkflowsWorkflowStage;
            "plugin::upload.file": PluginUploadFile;
            "plugin::upload.folder": PluginUploadFolder;
            "plugin::users-permissions.permission": PluginUsersPermissionsPermission;
            "plugin::users-permissions.role": PluginUsersPermissionsRole;
            "plugin::users-permissions.user": PluginUsersPermissionsUser;
        }
    }
}
