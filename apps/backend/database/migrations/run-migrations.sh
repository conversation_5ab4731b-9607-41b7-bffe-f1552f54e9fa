#!/bin/bash

# Phase 1 Database Migration Runner
# This script runs SQL migrations for performance optimization

set -e

# Color codes for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== CivicPoll Database Migration Runner ===${NC}"
echo ""

# Check if PostgreSQL connection environment variables are set
if [ -z "$DATABASE_HOST" ] || [ -z "$DATABASE_PORT" ] || [ -z "$DATABASE_NAME" ] || [ -z "$DATABASE_USERNAME" ]; then
    echo -e "${RED}Error: Database connection environment variables are not set${NC}"
    echo "Required variables: DATABASE_HOST, DATABASE_PORT, DATABASE_NAME, DATABASE_USERNAME, DATABASE_PASSWORD"
    exit 1
fi

# Set PGPASSWORD for non-interactive connection
export PGPASSWORD=$DATABASE_PASSWORD

# Function to run a migration file
run_migration() {
    local migration_file=$1
    local migration_name=$(basename "$migration_file" .sql)
    
    echo -e "${YELLOW}Running migration: ${migration_name}${NC}"
    
    if psql -h "$DATABASE_HOST" -p "$DATABASE_PORT" -U "$DATABASE_USERNAME" -d "$DATABASE_NAME" -f "$migration_file"; then
        echo -e "${GREEN}✓ Successfully applied: ${migration_name}${NC}"
        return 0
    else
        echo -e "${RED}✗ Failed to apply: ${migration_name}${NC}"
        return 1
    fi
}

# Find all SQL migration files and sort them
MIGRATION_DIR="$(dirname "$0")"
MIGRATIONS=$(find "$MIGRATION_DIR" -name "*.sql" -type f | sort)

if [ -z "$MIGRATIONS" ]; then
    echo -e "${YELLOW}No migration files found in $MIGRATION_DIR${NC}"
    exit 0
fi

echo "Found migrations:"
for migration in $MIGRATIONS; do
    echo "  - $(basename "$migration")"
done
echo ""

# Confirm before running migrations
read -p "Do you want to run these migrations? (y/N) " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Migration cancelled"
    exit 0
fi

# Run each migration
FAILED=0
for migration in $MIGRATIONS; do
    if ! run_migration "$migration"; then
        FAILED=$((FAILED + 1))
    fi
    echo ""
done

# Summary
if [ $FAILED -eq 0 ]; then
    echo -e "${GREEN}All migrations completed successfully!${NC}"
    echo ""
    echo "Running ANALYZE to update statistics..."
    psql -h "$DATABASE_HOST" -p "$DATABASE_PORT" -U "$DATABASE_USERNAME" -d "$DATABASE_NAME" -c "ANALYZE;"
    echo -e "${GREEN}Database statistics updated${NC}"
else
    echo -e "${RED}${FAILED} migration(s) failed${NC}"
    exit 1
fi

# Cleanup
unset PGPASSWORD