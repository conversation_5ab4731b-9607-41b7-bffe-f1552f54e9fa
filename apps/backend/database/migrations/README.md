# Database Migrations

This directory contains SQL migrations for optimizing the CivicPoll database performance.

## Running Migrations

### Prerequisites

1. Ensure PostgreSQL client tools are installed:

    ```bash
    # Ubuntu/Debian
    sudo apt-get install postgresql-client

    # macOS
    brew install postgresql
    ```

2. Set database connection environment variables:
    ```bash
    export DATABASE_HOST=localhost
    export DATABASE_PORT=5432
    export DATABASE_NAME=civicpoll
    export DATABASE_USERNAME=strapi
    export DATABASE_PASSWORD=your_password
    ```

### Execute Migrations

Run all migrations:

```bash
./run-migrations.sh
```

Or run a specific migration manually:

```bash
psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_NAME -f 001-phase1-indexes.sql
```

## Migration Files

### 001-phase1-indexes.sql

Creates performance indexes for Phase 1 features:

- Geographic zone queries
- User location validation
- Poll participation tracking
- Response aggregation
- Analytics queries

## Index Strategy

1. **Single Column Indexes**: For frequently filtered columns

    - `geographic_zones.type` - Filter polls by zone type
    - `polls.status` - Find active/closed polls
    - `user_locations.validated` - Check validation status

2. **Composite Indexes**: For common query patterns

    - `participations(user, poll)` - Check if user participated
    - `polls(geographic_zone, status, start_date, end_date)` - Active polls by zone

3. **Foreign Key Indexes**: Improve join performance
    - All foreign key columns have indexes

## Maintenance

After significant data changes, update statistics:

```sql
ANALYZE geographic_zones;
ANALYZE polls;
ANALYZE participations;
ANALYZE responses;
```

## Monitoring Index Usage

Check index usage:

```sql
SELECT
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;
```

Find missing indexes:

```sql
SELECT
    schemaname,
    tablename,
    attname,
    n_distinct,
    most_common_vals
FROM pg_stats
WHERE schemaname = 'public'
    AND n_distinct > 100
    AND tablename NOT IN (
        SELECT tablename
        FROM pg_indexes
        WHERE schemaname = 'public'
    );
```
