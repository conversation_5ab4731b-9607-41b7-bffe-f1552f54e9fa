# Environment Variables Configuration

This document describes all environment variables used in the CivicPoll project to make domain names and other configurations dynamic.

## Domain Configuration

### Primary Domain Variables

| Variable            | Description                               | Default                             | Example                         |
| ------------------- | ----------------------------------------- | ----------------------------------- | ------------------------------- |
| `DOMAIN_NAME`       | Primary domain for production             | `civicpoll.fr.smatflow.xyz`         | `civicpoll.example.com`         |
| `PRODUCTION_DOMAIN` | Production domain (overrides DOMAIN_NAME) | `civicpoll.fr.smatflow.xyz`         | `civicpoll.example.com`         |
| `STAGING_DOMAIN`    | Staging environment domain                | `civicpoll-staging.fr.smatflow.xyz` | `staging.civicpoll.example.com` |
| `DEV_DOMAIN`        | Development environment domain            | `civicpoll-dev.fr.smatflow.xyz`     | `dev.civicpoll.example.com`     |

### Email Configuration

| Variable       | Description                | Default         | Example       |
| -------------- | -------------------------- | --------------- | ------------- |
| `EMAIL_DOMAIN` | Domain for email addresses | `civicpoll.com` | `example.com` |

## Deployment Host Configuration

| Variable          | Description                 | Default                             | Example               |
| ----------------- | --------------------------- | ----------------------------------- | --------------------- |
| `PRODUCTION_HOST` | Production server hostname  | `civicpoll.fr.smatflow.xyz`         | `prod.example.com`    |
| `STAGING_HOST`    | Staging server hostname     | `civicpoll-staging.fr.smatflow.xyz` | `staging.example.com` |
| `DEV_HOST`        | Development server hostname | `civicpoll-dev.fr.smatflow.xyz`     | `dev.example.com`     |

## Usage Examples

### 1. Generate Nginx Configuration

```bash
# Set domain name and generate nginx config
export DOMAIN_NAME="civicpoll.example.com"
./infrastructure/scripts/generate-nginx-config.sh
```

### 2. Docker Deployment

```bash
# Set environment variables for docker-compose
export DOMAIN_NAME="civicpoll.example.com"
export EMAIL_DOMAIN="example.com"
export NODE_ENV="production"

docker-compose -f infrastructure/docker/docker-compose.yml up -d
```

### 3. PM2 Deployment

```bash
# Set deployment hosts
export PRODUCTION_HOST="prod.example.com"
export STAGING_HOST="staging.example.com"
export DEV_HOST="dev.example.com"

pm2 deploy ecosystem.config.js production
```

### 4. GitLab CI Variables

Set these variables in your GitLab CI/CD settings:

```
PRODUCTION_DOMAIN=civicpoll.example.com
STAGING_DOMAIN=staging.civicpoll.example.com
DEV_DOMAIN=dev.civicpoll.example.com
```

## File Templates

The following files have been converted to use environment variables:

### Nginx Configuration

- **Template**: `infrastructure/nginx/civicpoll.conf.template`
- **Generated**: `infrastructure/nginx/civicpoll.conf`
- **Script**: `infrastructure/scripts/generate-nginx-config.sh`

### Backend Configuration

- **File**: `apps/backend/.env.example`
- **Variables**: `DOMAIN_NAME`, `EMAIL_DOMAIN`

### Docker Compose

- **File**: `infrastructure/docker/docker-compose.yml`
- **Variables**: `EMAIL_DOMAIN`

### PM2 Ecosystem

- **File**: `ecosystem.config.js`
- **Variables**: `PRODUCTION_HOST`, `STAGING_HOST`, `DEV_HOST`

### GitLab CI

- **File**: `.gitlab-ci.yml`
- **Variables**: `PRODUCTION_DOMAIN`, `STAGING_DOMAIN`, `DEV_DOMAIN`

## Migration Guide

### From Static to Dynamic Configuration

1. **Set Environment Variables**:

    ```bash
    export DOMAIN_NAME="your-domain.com"
    export EMAIL_DOMAIN="your-domain.com"
    ```

2. **Generate Nginx Configuration**:

    ```bash
    ./infrastructure/scripts/generate-nginx-config.sh
    ```

3. **Update Environment Files**:

    - Copy `apps/backend/.env.example` to `apps/backend/.env`
    - Replace placeholder values with your actual configuration

4. **Configure CI/CD**:
    - Set domain variables in GitLab CI/CD settings
    - Update deployment scripts if needed

### Backward Compatibility

All configurations include default values that maintain backward compatibility with the original `civicpoll.fr.smatflow.xyz` domain structure.

## Security Considerations

- Never commit actual domain names or sensitive configuration to version control
- Use environment variables or CI/CD variables for all deployments
- Ensure SSL certificates match your domain configuration
- Update DNS records to point to your deployment servers

## Troubleshooting

### Common Issues

1. **Nginx Configuration Errors**:

    - Ensure `DOMAIN_NAME` is set before running the generation script
    - Validate nginx configuration with `nginx -t`

2. **SSL Certificate Issues**:

    - Ensure Let's Encrypt certificates exist for your domain
    - Update certificate paths if using custom SSL certificates

3. **Email Delivery Issues**:
    - Verify `EMAIL_DOMAIN` matches your email provider configuration
    - Update SPF/DKIM records for your email domain
