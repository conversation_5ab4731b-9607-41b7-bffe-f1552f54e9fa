#!/usr/bin/env bash

# CI/CD Variables for CivicPoll

set -u
: "${CI_REPOSITORY_URL}"

# ANSIBLE
set -u
: "${ANSIBLE_SERVER_HOST}"

set -u
: "${ANSIBLE_SERVER_USER}"

set -u
: "${ANSIBLE_DEPLOY_KEY}"

# CivicPoll specific environment variables
set -u
: "${CIVICPOLL_DATABASE_URL}"

set -u
: "${CIVICPOLL_DATABASE_HOST}"

set -u
: "${CIVICPOLL_DATABASE_PORT}"

set -u
: "${CIVICPOLL_DATABASE_NAME}"

set -u
: "${CIVICPOLL_DATABASE_USER}"

set -u
: "${CIVICPOLL_DATABASE_PASSWORD}"

# Strapi configuration
set -u
: "${STRAPI_APP_KEYS}"

set -u
: "${STRAPI_API_TOKEN_SALT}"

set -u
: "${STRAPI_ADMIN_JWT_SECRET}"

set -u
: "${STRAPI_TRANSFER_TOKEN_SALT}"

set -u
: "${STRAPI_JWT_SECRET}"

# SendGrid configuration (optional)
# set -u
# : "${SENDGRID_API_KEY}"

# Application URLs
set -u
: "${FRONTEND_URL}"

set -u
: "${BACKEND_URL}"
