---
# Check if .auth-secret file exists
- name: Check if auth secret file exists
  stat:
    path: "{{ project_dir }}/.auth-secret"
  register: auth_secret_file

# Generate new secret if file doesn't exist
- name: Generate OpenSSL random secret
  shell: "openssl rand -base64 32"
  register: new_secret
  when: not auth_secret_file.stat.exists

# Create .auth-secret file with the new secret if it doesn't exist
- name: Create auth secret file
  copy:
    content: "{{ new_secret.stdout }}"
    dest: "{{ project_dir }}/.auth-secret"
  when: not auth_secret_file.stat.exists

# Read existing secret from file
- name: Read auth secret
  slurp:
    src: "{{ project_dir }}/.auth-secret"
  register: auth_secret_raw

# Set the secret as a fact for later use
- name: Set auth secret fact
  set_fact:
    auth_secret: "{{ auth_secret_raw.content | b64decode | trim }}"

# Generate Strapi secrets if not provided
- name: Generate Strapi APP_KEYS if not provided
  shell: "openssl rand -base64 32"
  register: generated_app_keys
  when: lookup('ansible.builtin.env', 'STRAPI_APP_KEYS') == ""

- name: Set Strapi APP_KEYS fact
  set_fact:
    strapi_app_keys: "{{ lookup('ansible.builtin.env', 'STRAPI_APP_KEYS') if lookup('ansible.builtin.env', 'STRAPI_APP_KEYS') != '' else generated_app_keys.stdout }}"

- name: Generate Strapi API_TOKEN_SALT if not provided
  shell: "openssl rand -base64 32"
  register: generated_api_token_salt
  when: lookup('ansible.builtin.env', 'STRAPI_API_TOKEN_SALT') == ""

- name: Set Strapi API_TOKEN_SALT fact
  set_fact:
    strapi_api_token_salt: "{{ lookup('ansible.builtin.env', 'STRAPI_API_TOKEN_SALT') if lookup('ansible.builtin.env', 'STRAPI_API_TOKEN_SALT') != '' else generated_api_token_salt.stdout }}"

- name: Generate Strapi ADMIN_JWT_SECRET if not provided
  shell: "openssl rand -base64 32"
  register: generated_admin_jwt_secret
  when: lookup('ansible.builtin.env', 'STRAPI_ADMIN_JWT_SECRET') == ""

- name: Set Strapi ADMIN_JWT_SECRET fact
  set_fact:
    strapi_admin_jwt_secret: "{{ lookup('ansible.builtin.env', 'STRAPI_ADMIN_JWT_SECRET') if lookup('ansible.builtin.env', 'STRAPI_ADMIN_JWT_SECRET') != '' else generated_admin_jwt_secret.stdout }}"

- name: Generate Strapi TRANSFER_TOKEN_SALT if not provided
  shell: "openssl rand -base64 32"
  register: generated_transfer_token_salt
  when: lookup('ansible.builtin.env', 'STRAPI_TRANSFER_TOKEN_SALT') == ""

- name: Set Strapi TRANSFER_TOKEN_SALT fact
  set_fact:
    strapi_transfer_token_salt: "{{ lookup('ansible.builtin.env', 'STRAPI_TRANSFER_TOKEN_SALT') if lookup('ansible.builtin.env', 'STRAPI_TRANSFER_TOKEN_SALT') != '' else generated_transfer_token_salt.stdout }}"

- name: Generate Strapi JWT_SECRET if not provided
  shell: "openssl rand -base64 32"
  register: generated_jwt_secret
  when: lookup('ansible.builtin.env', 'STRAPI_JWT_SECRET') == ""

- name: Set Strapi JWT_SECRET fact
  set_fact:
    strapi_jwt_secret: "{{ lookup('ansible.builtin.env', 'STRAPI_JWT_SECRET') if lookup('ansible.builtin.env', 'STRAPI_JWT_SECRET') != '' else generated_jwt_secret.stdout }}"
