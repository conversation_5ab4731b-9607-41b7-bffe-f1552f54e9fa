---
- name: Install PostgreSQL and PostGIS
  become: true
  package:
    name: "{{ item }}"
    state: present
  loop:
    - postgresql
    - postgresql-contrib
    - postgresql-14-postgis-3
    - python3-psycopg2

- name: Start and enable PostgreSQL service
  become: true
  service:
    name: postgresql
    state: started
    enabled: yes

- name: Create CivicPoll database user
  become: true
  become_user: postgres
  postgresql_user:
    name: "{{ lookup('ansible.builtin.env', 'CIVICPOLL_DATABASE_USER') }}"
    password: "{{ lookup('ansible.builtin.env', 'CIVICPOLL_DATABASE_PASSWORD') }}"
    state: present
  when: lookup('ansible.builtin.env', 'CIVICPOLL_DATABASE_USER') != ""

- name: Create CivicPoll database
  become: true
  become_user: postgres
  postgresql_db:
    name: "{{ lookup('ansible.builtin.env', 'CIVICPOLL_DATABASE_NAME') }}"
    owner: "{{ lookup('ansible.builtin.env', 'CIVICPOLL_DATABASE_USER') }}"
    state: present
  when: lookup('ansible.builtin.env', 'CIVICPOLL_DATABASE_NAME') != ""

- name: Enable PostGIS extension
  become: true
  become_user: postgres
  postgresql_ext:
    name: postgis
    db: "{{ lookup('ansible.builtin.env', 'CIVICPOLL_DATABASE_NAME') }}"
    state: present
  when: lookup('ansible.builtin.env', 'CIVICPOLL_DATABASE_NAME') != ""

- name: Configure PostgreSQL for remote connections
  become: true
  lineinfile:
    path: /etc/postgresql/14/main/postgresql.conf
    regexp: "^#?listen_addresses"
    line: "listen_addresses = 'localhost'"
    backup: yes
  notify: postgresql restart

- name: Configure PostgreSQL authentication
  become: true
  lineinfile:
    path: /etc/postgresql/14/main/pg_hba.conf
    line: "local   {{ lookup('ansible.builtin.env', 'CIVICPOLL_DATABASE_NAME') }}   {{ lookup('ansible.builtin.env', 'CIVICPOLL_DATABASE_USER') }}   md5"
    backup: yes
  notify: postgresql restart
  when: 
    - lookup('ansible.builtin.env', 'CIVICPOLL_DATABASE_NAME') != ""
    - lookup('ansible.builtin.env', 'CIVICPOLL_DATABASE_USER') != ""
