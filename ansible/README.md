# CivicPoll Deployment

This directory contains Ansible playbooks and configuration files for deploying CivicPoll across different environments.

## Architecture

CivicPoll is deployed as a two-service architecture:

- **Backend**: Strapi CMS with PostgreSQL database (port 1337/1357/1347)
- **Frontend**: Next.js application (port 3000/3020/3010)

## Environments

### Development

- Frontend: http://${DEV_DOMAIN}:3010
- Backend: http://${DEV_DOMAIN}:1347
- Auto-deployed on every push to main

### Staging

- Frontend: http://${STAGING_DOMAIN}:3020
- Backend: http://${STAGING_DOMAIN}:1357
- Manual deployment from GitLab CI

### Production

- Frontend: https://civicpoll.fr.smatflow.xyz
- Backend: https://civicpoll.fr.smatflow.xyz/api
- Manual deployment with SSL enabled

## Required Environment Variables

Set these in your GitLab CI/CD variables:

### Infrastructure

- `ANSIBLE_SERVER_HOST`: Target deployment server
- `ANSIBLE_SERVER_USER`: SSH user for deployment
- `ANSIBLE_DEPLOY_KEY`: SSH private key for deployment

### Database

- `CIVICPOLL_DATABASE_URL`: Full PostgreSQL connection string
- `CIVICPOLL_DATABASE_HOST`: Database host (default: localhost)
- `CIVICPOLL_DATABASE_PORT`: Database port (default: 5432)
- `CIVICPOLL_DATABASE_NAME`: Database name
- `CIVICPOLL_DATABASE_USER`: Database username
- `CIVICPOLL_DATABASE_PASSWORD`: Database password

### Strapi Configuration

- `STRAPI_APP_KEYS`: Strapi application keys (auto-generated if not provided)
- `STRAPI_API_TOKEN_SALT`: API token salt (auto-generated if not provided)
- `STRAPI_ADMIN_JWT_SECRET`: Admin JWT secret (auto-generated if not provided)
- `STRAPI_TRANSFER_TOKEN_SALT`: Transfer token salt (auto-generated if not provided)
- `STRAPI_JWT_SECRET`: JWT secret (auto-generated if not provided)

### URLs

- `FRONTEND_URL`: Public frontend URL
- `BACKEND_URL`: Public backend URL

### Optional

- `SENDGRID_API_KEY`: For email functionality

## Features

### Security

- ✅ HTTPS with automatic SSL certificates (Let's Encrypt)
- ✅ Security headers (CSP, HSTS, etc.)
- ✅ Rate limiting for API and frontend
- ✅ GDPR compliance headers

### Performance

- ✅ Nginx reverse proxy with compression
- ✅ PM2 clustering for frontend
- ✅ PostgreSQL with PostGIS for geo features
- ✅ Automatic health checks

### Monitoring

- ✅ PM2 process monitoring
- ✅ Nginx access logs
- ✅ Application logs in `/opt/civicpoll/logs/`

## Manual Deployment

To deploy manually:

```bash
# Set environment variables
export ANSIBLE_SERVER_HOST="your-server.com"
export ANSIBLE_SERVER_USER="deploy"
export ANSIBLE_DEPLOY_KEY="$(cat ~/.ssh/deploy_key)"

# Deploy to development
ansible-playbook -u "$ANSIBLE_SERVER_USER" -i "$ANSIBLE_SERVER_HOST," ansible/playbook.yaml \
  --extra-vars "repo_url=https://gitlab.com/smatflow/civicpoll.git" \
  --extra-vars "environment=development"

# Deploy to production with SSL
ansible-playbook -u "$ANSIBLE_SERVER_USER" -i "$ANSIBLE_SERVER_HOST," ansible/playbook.yaml \
  --extra-vars "repo_url=https://gitlab.com/smatflow/civicpoll.git" \
  --extra-vars "environment=production" \
  --extra-vars "domain_name=civicpoll.fr.smatflow.xyz" \
  --extra-vars "admin_email=<EMAIL>" \
  --extra-vars "enable_ssl=true" \
  --extra-vars "enable_nginx=true"
```

## Troubleshooting

### Check service status

```bash
pm2 status
pm2 logs civicpoll-backend
pm2 logs civicpoll-frontend
```

### Check Nginx

```bash
sudo nginx -t
sudo systemctl status nginx
sudo tail -f /var/log/nginx/access.log
```

### Check database

```bash
sudo -u postgres psql -d civicpoll_db -c "SELECT version();"
```

### Port conflicts

If you encounter port conflicts, check the generated port info files:

```bash
ls ansible/port_*.info
```
