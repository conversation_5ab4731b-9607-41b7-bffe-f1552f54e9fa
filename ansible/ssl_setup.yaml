---
- name: Install SSL certificate with certbot
  become: true
  command: >
    certbot --nginx 
    --non-interactive 
    --agree-tos 
    --email {{ admin_email }}
    --domains {{ domain_name }}
    --redirect
  register: certbot_result
  changed_when: "'Congratulations' in certbot_result.stdout"
  failed_when: 
    - certbot_result.rc != 0
    - "'Certificate not yet due for renewal' not in certbot_result.stdout"

- name: Setup automatic SSL renewal
  become: true
  cron:
    name: "Certbot automatic renewal"
    job: "/usr/bin/certbot renew --quiet && systemctl reload nginx"
    minute: "0"
    hour: "12"
    user: root

- name: Test SSL configuration
  uri:
    url: "https://{{ domain_name }}/health"
    method: GET
    status_code: 200
  retries: 3
  delay: 5
  when: certbot_result.changed
