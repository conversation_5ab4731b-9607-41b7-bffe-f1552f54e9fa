# Strapi Configuration
HOST=0.0.0.0
PORT={{ backend_port }}
APP_KEYS={{ strapi_app_keys }}
API_TOKEN_SALT={{ strapi_api_token_salt }}
ADMIN_JWT_SECRET={{ strapi_admin_jwt_secret }}
TRANSFER_TOKEN_SALT={{ strapi_transfer_token_salt }}
JWT_SECRET={{ strapi_jwt_secret }}

# Database Configuration
DATABASE_CLIENT=postgres
DATABASE_HOST={{ lookup('ansible.builtin.env', 'CIVICPOLL_DATABASE_HOST') | default('localhost') }}
DATABASE_PORT={{ lookup('ansible.builtin.env', 'CIVICPOLL_DATABASE_PORT') | default('5432') }}
DATABASE_NAME={{ lookup('ansible.builtin.env', 'CIVICPOLL_DATABASE_NAME') }}
DATABASE_USERNAME={{ lookup('ansible.builtin.env', 'CIVICPOLL_DATABASE_USER') }}
DATABASE_PASSWORD={{ lookup('ansible.builtin.env', 'CIVICPOLL_DATABASE_PASSWORD') }}
DATABASE_SSL=false

# URLs
FRONTEND_URL={{ lookup('ansible.builtin.env', 'FRONTEND_URL') | default('http://localhost:' + frontend_port|string) }}
BACKEND_URL={{ lookup('ansible.builtin.env', 'BACKEND_URL') | default('http://localhost:' + backend_port|string) }}

# Environment
NODE_ENV={{ environment }}

# SendGrid Configuration (optional)
{% if lookup('ansible.builtin.env', 'SENDGRID_API_KEY') %}
SENDGRID_API_KEY={{ lookup('ansible.builtin.env', 'SENDGRID_API_KEY') }}
{% endif %}

# GDPR Configuration
GDPR_ENABLED=true
AUDIT_LOG_ENABLED=true

# Security
SECURE_COOKIES={{ 'true' if environment == 'production' else 'false' }}
TRUST_PROXY={{ 'true' if environment == 'production' else 'false' }}
