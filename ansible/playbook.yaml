---
- name: Deploy CivicPoll
  hosts: all
  become: false
  vars:
      project_name: "civicpoll"
      project_dir: "/home/<USER>/{{ project_name }}"
      frontend_port: 3000
      backend_port: 1337
      environment: "{{ environment | default('development') }}"

      # Environment-specific ports
      port_mapping:
          development:
              frontend: 3010
              backend: 1347
          staging:
              frontend: 3020
              backend: 1357
          production:
              frontend: 3000
              backend: 1337

  pre_tasks:
      - name: Update apt cache
        apt:
            update_cache: true
            cache_valid_time: 3600
        become: true

  handlers:
      - name: nginx reload
        become: true
        service:
            name: nginx
            state: reloaded

      - name: nginx restart

      - name: postgresql restart
        become: true
        service:
            name: postgresql
            state: restarted

      - become: true
        service:
            name: nginx
            state: restarted

      - name: restart backend
        command: "pm2 restart {{ project_name }}-backend"
        ignore_errors: true

      - name: restart frontend
        command: "pm2 restart {{ project_name }}-frontend"
        ignore_errors: true

  tasks:
      - name: Set environment-specific ports
        set_fact:
            frontend_port: "{{ port_mapping[environment].frontend }}"
            backend_port: "{{ port_mapping[environment].backend }}"

      - name: Install system dependencies
        when: ansible_os_family == "Debian"
        become: true
        package:
            name: "{{ item }}"
            state: present
        loop:
            - git
            - curl
            - ca-certificates
            - unzip
            - postgresql-client
            - nginx
            - certbot
            - python3-certbot-nginx

      - name: Clone Git repository
        git:
            repo: "{{ repo_url }}"
            dest: "{{ project_dir }}"
            clone: yes
            update: yes
            force: true
            version: main

      - include_tasks: port_check.yaml
        vars:
            app_port: "{{ frontend_port }}"
            service_name: "frontend"

      - include_tasks: port_check.yaml
        vars:
            app_port: "{{ backend_port }}"
            service_name: "backend"

      - include_tasks: auth-secret.yaml

      - include_tasks: nodejs.yaml
        vars:
            nodejs_version: 20

      # - include_tasks: postgresql.yaml
      #   when: environment == "production"

      - name: Template backend environment file
        ansible.builtin.template:
            src: backend.env.j2
            dest: "{{ project_dir }}/apps/backend/.env"
            force: true

      - name: Template frontend environment file
        ansible.builtin.template:
            src: frontend.env.j2
            dest: "{{ project_dir }}/apps/frontend/.env.local"
            force: true

      - name: Install root dependencies
        command: "pnpm install --frozen-lockfile"
        args:
            chdir: "{{ project_dir }}"

      - name: Build all applications
        command: "pnpm run build"
        args:
            chdir: "{{ project_dir }}"

      - name: Stop existing PM2 processes
        block:
            - name: Delete old backend pm2 process
              command: "pm2 delete {{ project_name }}-backend"
              ignore_errors: true

            - name: Delete old frontend pm2 process
              command: "pm2 delete {{ project_name }}-frontend"
              ignore_errors: true

      - name: Start backend server
        command: "pm2 start ecosystem.config.js --only {{ project_name }}-backend --env {{ environment }}"
        args:
            chdir: "{{ project_dir }}"
        environment:
            PORT: "{{ backend_port }}"

      - name: Start frontend server
        command: "pm2 start ecosystem.config.js --only {{ project_name }}-frontend --env {{ environment }}"
        args:
            chdir: "{{ project_dir }}"
        environment:
            PORT: "{{ frontend_port }}"

      - name: Save PM2 process list
        command: "pm2 save"
        changed_when: false

      - name: Generate PM2 startup script
        become: true
        command: "pm2 startup systemd -u {{ ansible_user }} --hp /home/<USER>"
        args:
            creates: "/etc/systemd/system/pm2-{{ ansible_user }}.service"
        ignore_errors: true

      # - name: Configure Nginx
      #   include_tasks: nginx_config.yaml
      #   when: enable_nginx is defined and enable_nginx | bool

      # - name: Setup SSL certificates
      #   include_tasks: ssl_setup.yaml
      #   when: enable_ssl is defined and enable_ssl | bool and environment == "production"

      # - name: Health check
      #   uri:
      #       url: "http://localhost:{{ frontend_port }}"
      #       method: GET
      #       status_code: 200
      #   retries: 5
      #   delay: 10
