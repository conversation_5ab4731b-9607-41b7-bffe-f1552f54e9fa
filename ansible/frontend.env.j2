# Next.js Configuration
NEXT_PUBLIC_APP_ENV={{ environment }}
NEXT_PUBLIC_API_URL={{ lookup('ansible.builtin.env', 'BACKEND_URL') | default('http://localhost:' + backend_port|string) }}

# URLs
FRONTEND_URL={{ lookup('ansible.builtin.env', 'FRONTEND_URL') | default('http://localhost:' + frontend_port|string) }}
BACKEND_URL={{ lookup('ansible.builtin.env', 'BACKEND_URL') | default('http://localhost:' + backend_port|string) }}

# Application Configuration
NEXT_PUBLIC_SITE_NAME="CivicPoll"
NEXT_PUBLIC_SITE_DESCRIPTION="Plateforme de sondages géolocalisés pour la France"

# Environment
NODE_ENV={{ environment }}

# Security
NEXT_PUBLIC_SECURE_COOKIES={{ 'true' if environment == 'production' else 'false' }}

# Features
NEXT_PUBLIC_GDPR_ENABLED=true
NEXT_PUBLIC_GEOLOCATION_ENABLED=true

# Development
{% if environment == 'development' %}
NEXT_PUBLIC_DEV_MODE=true
{% endif %}
