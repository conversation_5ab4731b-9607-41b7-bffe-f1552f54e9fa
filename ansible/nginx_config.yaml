---
- name: Create Nginx configuration for CivicPoll
  become: true
  template:
    src: nginx.conf.j2
    dest: "/etc/nginx/sites-available/{{ domain_name | default('civicpoll.local') }}"
    backup: yes
  notify: nginx reload

- name: Enable CivicPoll site
  become: true
  file:
    src: "/etc/nginx/sites-available/{{ domain_name | default('civicpoll.local') }}"
    dest: "/etc/nginx/sites-enabled/{{ domain_name | default('civicpoll.local') }}"
    state: link
  notify: nginx reload

- name: Remove default Nginx site
  become: true
  file:
    path: "/etc/nginx/sites-enabled/default"
    state: absent
  notify: nginx reload

- name: Test Nginx configuration
  become: true
  command: nginx -t
  register: nginx_test_result
  changed_when: false

- name: Reload Nginx if configuration is valid
  become: true
  service:
    name: nginx
    state: reloaded
  when: nginx_test_result.rc == 0
