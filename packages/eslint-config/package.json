{"name": "@civicpoll/eslint-config", "version": "1.0.0", "description": "Shared ESLint configuration for CivicPoll monorepo", "private": true, "main": "index.js", "files": ["index.js"], "keywords": ["eslint", "config", "monorepo"], "author": "", "license": "ISC", "packageManager": "pnpm@10.12.1", "dependencies": {"@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0"}, "peerDependencies": {"eslint": ">=8.0.0", "typescript": ">=5.0.0"}}