import { isString, isNumber, isObject, isArray } from "../index";

describe("Type Guards", () => {
    describe("isString", () => {
        it("returns true for strings", () => {
            expect(isString("hello")).toBe(true);
            expect(isString("")).toBe(true);
            expect(isString(String("test"))).toBe(true);
        });

        it("returns false for non-strings", () => {
            expect(isString(123)).toBe(false);
            expect(isString(null)).toBe(false);
            expect(isString(undefined)).toBe(false);
            expect(isString({})).toBe(false);
            expect(isString([])).toBe(false);
            expect(isString(true)).toBe(false);
        });
    });

    describe("isNumber", () => {
        it("returns true for valid numbers", () => {
            expect(isNumber(123)).toBe(true);
            expect(isNumber(0)).toBe(true);
            expect(isNumber(-456)).toBe(true);
            expect(isNumber(3.14)).toBe(true);
            expect(isNumber(Number("42"))).toBe(true);
        });

        it("returns false for NaN and non-numbers", () => {
            expect(isNumber(NaN)).toBe(false);
            expect(isNumber("123")).toBe(false);
            expect(isNumber(null)).toBe(false);
            expect(isNumber(undefined)).toBe(false);
            expect(isNumber({})).toBe(false);
            expect(isNumber([])).toBe(false);
            expect(isNumber(Infinity)).toBe(true); // Infinity is a valid number
        });
    });

    describe("isObject", () => {
        it("returns true for plain objects", () => {
            expect(isObject({})).toBe(true);
            expect(isObject({ a: 1 })).toBe(true);
            expect(isObject(new Object())).toBe(true);
        });

        it("returns false for arrays, null, and non-objects", () => {
            expect(isObject([])).toBe(false);
            expect(isObject(null)).toBe(false);
            expect(isObject(undefined)).toBe(false);
            expect(isObject("string")).toBe(false);
            expect(isObject(123)).toBe(false);
            expect(isObject(true)).toBe(false);
            expect(isObject(() => {})).toBe(false);
        });

        it("returns true for class instances", () => {
            class TestClass {}
            expect(isObject(new TestClass())).toBe(true);
        });
    });

    describe("isArray", () => {
        it("returns true for arrays", () => {
            expect(isArray([])).toBe(true);
            expect(isArray([1, 2, 3])).toBe(true);
            expect(isArray(new Array())).toBe(true);
            expect(isArray(Array.from("hello"))).toBe(true);
        });

        it("returns false for non-arrays", () => {
            expect(isArray({})).toBe(false);
            expect(isArray(null)).toBe(false);
            expect(isArray(undefined)).toBe(false);
            expect(isArray("string")).toBe(false);
            expect(isArray(123)).toBe(false);
            expect(isArray({ length: 0 })).toBe(false); // Array-like but not array
        });

        it("works with typed arrays", () => {
            const typedArray: number[] = [1, 2, 3];
            expect(isArray<number>(typedArray)).toBe(true);

            // Type guard narrows type
            if (isArray<string>(["a", "b"])) {
                // This block is type-safe
                expect(true).toBe(true);
            }
        });
    });
});
