import { chunk, unique } from "../index";

describe("Array Utilities", () => {
    describe("chunk", () => {
        it("splits array into chunks of specified size", () => {
            const arr = [1, 2, 3, 4, 5, 6];
            expect(chunk(arr, 2)).toEqual([
                [1, 2],
                [3, 4],
                [5, 6],
            ]);
        });

        it("handles arrays not evenly divisible", () => {
            const arr = [1, 2, 3, 4, 5];
            expect(chunk(arr, 2)).toEqual([[1, 2], [3, 4], [5]]);
        });

        it("handles chunk size larger than array", () => {
            const arr = [1, 2, 3];
            expect(chunk(arr, 5)).toEqual([[1, 2, 3]]);
        });

        it("handles empty arrays", () => {
            expect(chunk([], 2)).toEqual([]);
        });

        it("handles chunk size of 1", () => {
            const arr = [1, 2, 3];
            expect(chunk(arr, 1)).toEqual([[1], [2], [3]]);
        });

        it("works with different types", () => {
            const arr = ["a", "b", "c", "d"];
            expect(chunk(arr, 2)).toEqual([
                ["a", "b"],
                ["c", "d"],
            ]);
        });
    });

    describe("unique", () => {
        it("removes duplicate numbers", () => {
            expect(unique([1, 2, 2, 3, 3, 3])).toEqual([1, 2, 3]);
        });

        it("removes duplicate strings", () => {
            expect(unique(["a", "b", "b", "c"])).toEqual(["a", "b", "c"]);
        });

        it("preserves order of first occurrence", () => {
            expect(unique([3, 1, 2, 1, 3])).toEqual([3, 1, 2]);
        });

        it("handles empty arrays", () => {
            expect(unique([])).toEqual([]);
        });

        it("handles arrays with no duplicates", () => {
            expect(unique([1, 2, 3])).toEqual([1, 2, 3]);
        });

        it("works with mixed types", () => {
            expect(unique([1, "1", 1, "1"])).toEqual([1, "1"]);
        });

        it("handles objects by reference", () => {
            const obj1 = { a: 1 };
            const obj2 = { a: 1 };
            expect(unique([obj1, obj1, obj2])).toEqual([obj1, obj2]);
        });
    });
});
