import { sleep, retry } from "../index";

describe("Async Utilities", () => {
    describe("sleep", () => {
        it("delays for specified milliseconds", async () => {
            const start = Date.now();
            await sleep(100);
            const elapsed = Date.now() - start;

            // Allow some margin for test execution
            expect(elapsed).toBeGreaterThanOrEqual(90);
            expect(elapsed).toBeLessThan(150);
        });

        it("resolves to undefined", async () => {
            const result = await sleep(10);
            expect(result).toBeUndefined();
        });
    });

    describe("retry", () => {
        it("succeeds on first try", async () => {
            const fn = jest.fn().mockResolvedValue("success");
            const result = await retry(fn);

            expect(result).toBe("success");
            expect(fn).toHaveBeenCalledTimes(1);
        });

        it("retries on failure and eventually succeeds", async () => {
            const fn = jest
                .fn()
                .mockRejectedValueOnce(new Error("fail"))
                .mockRejectedValueOnce(new Error("fail"))
                .mockResolvedValue("success");

            const result = await retry(fn);

            expect(result).toBe("success");
            expect(fn).toHaveBeenCalledTimes(3);
        });

        it("throws after max retries", async () => {
            const error = new Error("persistent failure");
            const fn = jest.fn().mockRejectedValue(error);

            await expect(retry(fn, 2)).rejects.toThrow("persistent failure");
            expect(fn).toHaveBeenCalledTimes(3); // Initial + 2 retries
        });

        it("uses exponential backoff", async () => {
            const fn = jest
                .fn()
                .mockRejectedValueOnce(new Error("fail"))
                .mockRejectedValueOnce(new Error("fail"))
                .mockResolvedValue("success");

            const start = Date.now();
            await retry(fn, 3, 100);
            const elapsed = Date.now() - start;

            // Should wait at least 100ms + 200ms = 300ms
            expect(elapsed).toBeGreaterThanOrEqual(250);
        });

        it("works with custom retry count and delay", async () => {
            const fn = jest
                .fn()
                .mockRejectedValueOnce(new Error("fail"))
                .mockResolvedValue("success");

            const result = await retry(fn, 1, 50);

            expect(result).toBe("success");
            expect(fn).toHaveBeenCalledTimes(2);
        });

        it("throws immediately with 0 retries", async () => {
            const error = new Error("immediate failure");
            const fn = jest.fn().mockRejectedValue(error);

            await expect(retry(fn, 0)).rejects.toThrow("immediate failure");
            expect(fn).toHaveBeenCalledTimes(1);
        });
    });
});
