import { formatDate, formatDateTime } from "../index";

describe("Date Utilities", () => {
    describe("formatDate", () => {
        it("formats Date object correctly", () => {
            const date = new Date("2024-01-15T10:30:00Z");
            const result = formatDate(date);
            expect(result).toMatch(/January 15, 2024/);
        });

        it("formats date string correctly", () => {
            const result = formatDate("2024-12-25");
            expect(result).toMatch(/December 25, 2024/);
        });

        it("handles invalid date strings", () => {
            const result = formatDate("invalid-date");
            expect(result).toBe("Invalid Date");
        });
    });

    describe("formatDateTime", () => {
        it("formats Date object with time", () => {
            const date = new Date("2024-01-15T14:30:00");
            const result = formatDateTime(date);
            expect(result).toMatch(/Jan 15, 2024/);
            expect(result).toMatch(/PM/);
        });

        it("formats date string with time", () => {
            const result = formatDateTime("2024-12-25T09:00:00");
            expect(result).toMatch(/Dec 25, 2024/);
        });

        it("handles timezone correctly", () => {
            const date = new Date("2024-01-15T00:00:00Z");
            const result = formatDateTime(date);
            expect(result).toMatch(/\d{1,2}:\d{2}/);
        });
    });
});
