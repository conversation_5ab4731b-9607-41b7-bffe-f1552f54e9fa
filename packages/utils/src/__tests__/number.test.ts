import { clamp, formatNumber } from "../index";

describe("Number Utilities", () => {
    describe("clamp", () => {
        it("returns value within range", () => {
            expect(clamp(5, 0, 10)).toBe(5);
        });

        it("clamps to minimum value", () => {
            expect(clamp(-5, 0, 10)).toBe(0);
        });

        it("clamps to maximum value", () => {
            expect(clamp(15, 0, 10)).toBe(10);
        });

        it("handles equal min and max", () => {
            expect(clamp(5, 5, 5)).toBe(5);
            expect(clamp(0, 5, 5)).toBe(5);
            expect(clamp(10, 5, 5)).toBe(5);
        });

        it("handles negative ranges", () => {
            expect(clamp(-5, -10, -1)).toBe(-5);
            expect(clamp(-15, -10, -1)).toBe(-10);
            expect(clamp(0, -10, -1)).toBe(-1);
        });
    });

    describe("formatNumber", () => {
        it("formats integers", () => {
            expect(formatNumber(1000)).toBe("1,000");
            expect(formatNumber(1000000)).toBe("1,000,000");
        });

        it("formats decimals", () => {
            expect(formatNumber(1234.56)).toBe("1,234.56");
        });

        it("handles negative numbers", () => {
            expect(formatNumber(-1000)).toBe("-1,000");
        });

        it("handles zero", () => {
            expect(formatNumber(0)).toBe("0");
        });

        it("handles very large numbers", () => {
            expect(formatNumber(1e10)).toBe("10,000,000,000");
        });
    });
});
