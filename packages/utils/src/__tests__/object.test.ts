import { pick, omit } from "../index";

describe("Object Utilities", () => {
    describe("pick", () => {
        it("picks specified properties", () => {
            const obj = { a: 1, b: 2, c: 3 };
            expect(pick(obj, ["a", "c"])).toEqual({ a: 1, c: 3 });
        });

        it("ignores non-existent properties", () => {
            const obj = { a: 1, b: 2 };
            expect(pick(obj, ["a", "c" as keyof typeof obj])).toEqual({ a: 1 });
        });

        it("handles empty keys array", () => {
            const obj = { a: 1, b: 2 };
            expect(pick(obj, [])).toEqual({});
        });

        it("handles nested objects", () => {
            const obj = { a: 1, b: { c: 2 }, d: 3 };
            expect(pick(obj, ["b", "d"])).toEqual({ b: { c: 2 }, d: 3 });
        });

        it("preserves property types", () => {
            const obj = { str: "hello", num: 42, bool: true };
            const result = pick(obj, ["str", "num"]);
            expect(result.str).toBe("hello");
            expect(result.num).toBe(42);
        });
    });

    describe("omit", () => {
        it("omits specified properties", () => {
            const obj = { a: 1, b: 2, c: 3 };
            expect(omit(obj, ["b"])).toEqual({ a: 1, c: 3 });
        });

        it("handles multiple properties", () => {
            const obj = { a: 1, b: 2, c: 3, d: 4 };
            expect(omit(obj, ["b", "d"])).toEqual({ a: 1, c: 3 });
        });

        it("ignores non-existent properties", () => {
            const obj = { a: 1, b: 2 };
            expect(omit(obj, ["c" as keyof typeof obj])).toEqual({ a: 1, b: 2 });
        });

        it("handles empty keys array", () => {
            const obj = { a: 1, b: 2 };
            expect(omit(obj, [])).toEqual({ a: 1, b: 2 });
        });

        it("creates a new object", () => {
            const obj = { a: 1, b: 2 };
            const result = omit(obj, ["b"]);
            expect(result).not.toBe(obj);
            expect(obj).toEqual({ a: 1, b: 2 }); // Original unchanged
        });
    });
});
