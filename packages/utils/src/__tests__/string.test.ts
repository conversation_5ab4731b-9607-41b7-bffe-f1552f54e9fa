import { truncate, slugify, isValidEmail, isValidUrl } from "../index";

describe("String Utilities", () => {
    describe("truncate", () => {
        it("returns original string if shorter than max length", () => {
            expect(truncate("hello", 10)).toBe("hello");
        });

        it("truncates long strings with ellipsis", () => {
            expect(truncate("hello world this is a long string", 10)).toBe("hello w...");
        });

        it("handles exact length strings", () => {
            expect(truncate("hello", 5)).toBe("hello");
        });

        it("handles very short max lengths", () => {
            expect(truncate("hello", 3)).toBe("...");
        });
    });

    describe("slugify", () => {
        it("converts to lowercase", () => {
            expect(slugify("Hello World")).toBe("hello-world");
        });

        it("replaces spaces with hyphens", () => {
            expect(slugify("hello world test")).toBe("hello-world-test");
        });

        it("removes special characters", () => {
            expect(slugify("Hello! World? Test#")).toBe("hello-world-test");
        });

        it("handles multiple spaces and hyphens", () => {
            expect(slugify("hello   world - test")).toBe("hello-world-test");
        });

        it("removes leading and trailing hyphens", () => {
            expect(slugify("  hello world  ")).toBe("hello-world");
        });

        it("handles unicode characters", () => {
            expect(slugify("Café résumé")).toBe("caf-rsum");
        });
    });

    describe("isValidEmail", () => {
        it("validates correct email addresses", () => {
            expect(isValidEmail("<EMAIL>")).toBe(true);
            expect(isValidEmail("<EMAIL>")).toBe(true);
            expect(isValidEmail("<EMAIL>")).toBe(true);
        });

        it("rejects invalid email addresses", () => {
            expect(isValidEmail("invalid")).toBe(false);
            expect(isValidEmail("@example.com")).toBe(false);
            expect(isValidEmail("user@")).toBe(false);
            expect(isValidEmail("user @example.com")).toBe(false);
            expect(isValidEmail("user@example")).toBe(false);
        });
    });

    describe("isValidUrl", () => {
        it("validates correct URLs", () => {
            expect(isValidUrl("https://example.com")).toBe(true);
            expect(isValidUrl("http://localhost:3000")).toBe(true);
            expect(isValidUrl("https://example.com/path?query=value")).toBe(true);
            expect(isValidUrl("ftp://files.example.com")).toBe(true);
        });

        it("rejects invalid URLs", () => {
            expect(isValidUrl("not a url")).toBe(false);
            expect(isValidUrl("example.com")).toBe(false);
            expect(isValidUrl("//example.com")).toBe(false);
            expect(isValidUrl("http://")).toBe(false);
        });
    });
});
