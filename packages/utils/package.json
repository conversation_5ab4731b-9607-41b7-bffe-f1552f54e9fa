{"name": "@civicpoll/utils", "version": "1.0.0", "description": "Shared utilities for CivicPoll monorepo", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "files": ["dist"], "keywords": ["utils", "shared", "monorepo"], "author": "", "license": "ISC", "packageManager": "pnpm@10.12.1", "devDependencies": {"@civicpoll/typescript-config": "workspace:*", "@types/jest": "^29.5.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "tsup": "^8.0.0", "typescript": "^5.3.0"}}