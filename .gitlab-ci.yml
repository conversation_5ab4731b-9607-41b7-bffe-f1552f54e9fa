# default:
#   tags:
#     - smatflow-projects-runner

stages:
  - test
  - build
  # - deploy-dev
  # - deploy-staging
  # - deploy-prod

variables:
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"
  NODE_VERSION: "20"

# Test stage
test:
  stage: test
  image: node:${NODE_VERSION}
  cache:
    key: node-cache-$CI_COMMIT_REF_SLUG
    paths:
      - node_modules/
      - apps/frontend/node_modules/
      - apps/backend/node_modules/
      - packages/*/node_modules/
  before_script:
    - corepack enable
    - pnpm install --frozen-lockfile
  script:
    - pnpm run lint
    - pnpm run test
    - pnpm run test:coverage
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

# Build stage
build:
  stage: build
  image: node:${NODE_VERSION}
  cache:
    key: node-cache-$CI_COMMIT_REF_SLUG
    paths:
      - node_modules/
      - apps/frontend/node_modules/
      - apps/backend/node_modules/
      - packages/*/node_modules/
  before_script:
    - corepack enable
    - pnpm install --frozen-lockfile
  script:
    - pnpm run build
  artifacts:
    paths:
      - apps/frontend/.next/
      - apps/backend/dist/
      - apps/backend/build/
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == "main"

# Deployment base configuration
.deploy:
  image: python:3.10
  dependencies:
    - build
  cache:
    key: pip-cache
    paths:
      - .cache/pip
  before_script:
    - bash cicd_vars.sh
    - pip install --upgrade pip
    - pip install ansible
    - export ANSIBLE_HOST_KEY_CHECKING=False
    - eval $(ssh-agent -s)
    - ssh-add <(echo "$ANSIBLE_DEPLOY_KEY")

# Development deployment
deploy-dev:
  extends: .deploy
  stage: deploy-dev
  environment:
    name: development
    url: https://${DEV_DOMAIN:-civicpoll-dev.fr.smatflow.xyz}
  script:
    - ansible-playbook -u "$ANSIBLE_SERVER_USER" -i "$ANSIBLE_SERVER_HOST," ansible/playbook.yaml --extra-vars "repo_url=$CI_REPOSITORY_URL environment=development"
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
  when: always

# Staging deployment
deploy-staging:
  extends: .deploy
  stage: deploy-staging
  environment:
    name: staging
    url: https://${STAGING_DOMAIN:-civicpoll-staging.fr.smatflow.xyz}
  needs: [deploy-dev]
  script:
    - ansible-playbook -u "$ANSIBLE_SERVER_USER" -i "$ANSIBLE_SERVER_HOST," ansible/playbook.yaml --extra-vars "repo_url=$CI_REPOSITORY_URL environment=staging"
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
  when: manual

# Production deployment
deploy-prod:
  extends: .deploy
  stage: deploy-prod
  environment:
    name: production
    url: https://${PRODUCTION_DOMAIN:-civicpoll.fr.smatflow.xyz}
  needs: [deploy-staging]
  script:
    - |
      ansible-playbook -u "$ANSIBLE_SERVER_USER" -i "$ANSIBLE_SERVER_HOST," ansible/playbook.yaml \
        --extra-vars "repo_url=$CI_REPOSITORY_URL" \
        --extra-vars "environment=production" \
        --extra-vars "domain_name=${PRODUCTION_DOMAIN:-civicpoll.fr.smatflow.xyz}" \
        --extra-vars "admin_email=<EMAIL>" \
        --extra-vars "enable_ssl=true" \
        --extra-vars "enable_nginx=true"
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
  when: manual
  allow_failure: false
